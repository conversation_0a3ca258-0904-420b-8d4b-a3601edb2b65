package com.ssraman.drugraman.db.entity;

import com.ssraman.drugraman.db.StringDateConverter;

import org.greenrobot.greendao.annotation.Convert;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Generated;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @author: Administrator
 * @date: 2021/10/9
 */
@Entity(nameInDb = "Ft",createInDb = false)
public class FtInfo {

    @Id(autoincrement = true)
    @Property(nameInDb = "Id")
    private Long Id;

    //类似备注
    @Property(nameInDb = "Mark")
    private String Mark;
    //对应SampleType类型库中的Id
    @Property(nameInDb = "SampleTypeId")
    private Integer SampleTypeId;

    //波数
    @Property(nameInDb = "Wave")
    private byte[] ObWave;

    //强度
    @Property(nameInDb = "Intensity")
    private byte[] ObIntensity;
    //噪声
    @Property(nameInDb = "Noise")
    private byte[] ObNoise;
    //中心波长
    @Property(nameInDb = "SendWave")
    private Double SendWave;
    //含量
    @Property(nameInDb = "HanLiang")
    private Double HanLiang;
    //比例，基液
    @Property(nameInDb = "rate")
    private Double Rate;

    @Property(nameInDb = "type")
    private Integer Type;

    @Property(nameInDb = "SampleName")
    private String SampleName;
    //匹配限值
    @Property(nameInDb = "MatchLimit")
    private Double MatchLimit=10.0;
    //匹配比例
    @Property(nameInDb = "MatchScale")
    private Double MatchScale=0.5;

    //结果置信度
    @Property(nameInDb = "Confidence")
    private Double Confidence=0.90;

    //是否是标准品 0修改为来自系统库，显示时不予显示特征峰值和标点；自建1
    @Property(nameInDb = "Standard")
    private Integer Standard=0;
    //起始波数
    @Property(nameInDb = "StartWave")
    private Integer StartWave=400;

    @Property(nameInDb = "IntegratioTime")
    private Integer IntegratioTime;
    @Property(nameInDb = "LaserPower")
    private Double LaserPower;
    @Property(nameInDb = "AverageCount")
    private Integer AverageCount;

    //谱图来源类别  普通谱图 0、增强谱图 1 （个人认知：增强谱图跟前处理、增强试剂、匹配试剂及方法有很大的关系，需跟普通谱图区分开来）
    @Property(nameInDb = "SourceType")
    private Integer SourceType;

    //描述信息  (含CAS、Molecular_formula等)
    @Property(nameInDb = "DescriptionInformation")
    private byte[] DescriptionInformation;

    //谱图类型 拉曼谱图 0；荧光谱图 1；差分拉曼谱图 2
    @Property(nameInDb = "SpecType")
    private Integer SpecType;


    //检验确认人员（发布人）
    @Property(nameInDb = "Publisher")
    private String Inspector;

    //检验确认时间
    @Property(nameInDb = "PublisherTime")
    @Convert(converter = StringDateConverter.class, columnType = String.class)
    private Date InspectorTime;

    //是否检验确认通过
    @Property(nameInDb = "PublisherPass")
    private Integer InspectorPass;

    //检验MAC地址（发布人）
    @Property(nameInDb = "PublisherMacAddress")
    private String PublisherMacAddress;

    //审核确认人员 （复核）
    @Property(nameInDb = "Reviewer")
    private String Reviewer;

    //审核确认时间
    @Property(nameInDb = "ReviewerTime")
    @Convert(converter = StringDateConverter.class, columnType = String.class)
    private Date ReviewerTime;

    //是否审核确认通过
    @Property(nameInDb = "ReviewerPass")
    private Integer ReviewerPass;

    //审核MAC地址（复核）
    @Property(nameInDb = "ReviewerMacAddress")
    private String ReviewerMacAddress;


    @Generated(hash = 1077851355)
    public FtInfo() {
    }

    @Generated(hash = 713056000)
    public FtInfo(Long Id, String Mark, Integer SampleTypeId, byte[] ObWave,
            byte[] ObIntensity, byte[] ObNoise, Double SendWave, Double HanLiang,
            Double Rate, Integer Type, String SampleName, Double MatchLimit,
            Double MatchScale, Double Confidence, Integer Standard,
            Integer StartWave, Integer IntegratioTime, Double LaserPower,
            Integer AverageCount, Integer SourceType, byte[] DescriptionInformation,
            Integer SpecType, String Inspector, Date InspectorTime,
            Integer InspectorPass, String PublisherMacAddress, String Reviewer,
            Date ReviewerTime, Integer ReviewerPass, String ReviewerMacAddress) {
        this.Id = Id;
        this.Mark = Mark;
        this.SampleTypeId = SampleTypeId;
        this.ObWave = ObWave;
        this.ObIntensity = ObIntensity;
        this.ObNoise = ObNoise;
        this.SendWave = SendWave;
        this.HanLiang = HanLiang;
        this.Rate = Rate;
        this.Type = Type;
        this.SampleName = SampleName;
        this.MatchLimit = MatchLimit;
        this.MatchScale = MatchScale;
        this.Confidence = Confidence;
        this.Standard = Standard;
        this.StartWave = StartWave;
        this.IntegratioTime = IntegratioTime;
        this.LaserPower = LaserPower;
        this.AverageCount = AverageCount;
        this.SourceType = SourceType;
        this.DescriptionInformation = DescriptionInformation;
        this.SpecType = SpecType;
        this.Inspector = Inspector;
        this.InspectorTime = InspectorTime;
        this.InspectorPass = InspectorPass;
        this.PublisherMacAddress = PublisherMacAddress;
        this.Reviewer = Reviewer;
        this.ReviewerTime = ReviewerTime;
        this.ReviewerPass = ReviewerPass;
        this.ReviewerMacAddress = ReviewerMacAddress;
    }

    

    public Long getId() {
        return this.Id;
    }
    public void setId(Long Id) {
        this.Id = Id;
    }
    public String getMark() {
        return this.Mark;
    }
    public void setMark(String Mark) {
        this.Mark = Mark;
    }
    public Integer getSampleTypeId() {
        return this.SampleTypeId;
    }
    public void setSampleTypeId(Integer SampleTypeId) {
        this.SampleTypeId = SampleTypeId;
    }
    public byte[] getObWave() {
        return this.ObWave;
    }
    public void setObWave(byte[] ObWave) {
        this.ObWave = ObWave;
    }
    public byte[] getObIntensity() {
        return this.ObIntensity;
    }
    public void setObIntensity(byte[] ObIntensity) {
        this.ObIntensity = ObIntensity;
    }
    public byte[] getObNoise() {
        return this.ObNoise;
    }
    public void setObNoise(byte[] ObNoise) {
        this.ObNoise = ObNoise;
    }
    public Double getSendWave() {
        return this.SendWave;
    }
    public void setSendWave(Double SendWave) {
        this.SendWave = SendWave;
    }
    public Double getHanLiang() {
        return this.HanLiang;
    }
    public void setHanLiang(Double HanLiang) {
        this.HanLiang = HanLiang;
    }
    public Double getRate() {
        return this.Rate;
    }
    public void setRate(Double Rate) {
        this.Rate = Rate;
    }
    public Integer getType() {
        return this.Type;
    }
    public void setType(Integer Type) {
        this.Type = Type;
    }
    public String getSampleName() {
        return this.SampleName;
    }
    public void setSampleName(String SampleName) {
        this.SampleName = SampleName;
    }
    public Double getMatchLimit() {
        return this.MatchLimit;
    }
    public void setMatchLimit(Double MatchLimit) {
        this.MatchLimit = MatchLimit;
    }
    public Double getMatchScale() {
        return this.MatchScale;
    }
    public void setMatchScale(Double MatchScale) {
        this.MatchScale = MatchScale;
    }
    public Integer getStandard() {
        return this.Standard;
    }
    public void setStandard(Integer Standard) {
        this.Standard = Standard;
    }
    public Integer getStartWave() {
        return this.StartWave;
    }
    public void setStartWave(Integer StartWave) {
        this.StartWave = StartWave;
    }
    public Integer getIntegratioTime() {
        return this.IntegratioTime;
    }
    public void setIntegratioTime(Integer IntegratioTime) {
        this.IntegratioTime = IntegratioTime;
    }
    public Double getLaserPower() {
        return this.LaserPower;
    }
    public void setLaserPower(Double LaserPower) {
        this.LaserPower = LaserPower;
    }
    public Integer getAverageCount() {
        return this.AverageCount;
    }
    public void setAverageCount(Integer AverageCount) {
        this.AverageCount = AverageCount;
    }
    public byte[] getDescriptionInformation() {
        return this.DescriptionInformation;
    }
    public void setDescriptionInformation(byte[] DescriptionInformation) {
        this.DescriptionInformation = DescriptionInformation;
    }
    public Integer getSourceType() {
        return this.SourceType;
    }
    public void setSourceType(Integer SourceType) {
        this.SourceType = SourceType;
    }
    public Integer getSpecType() {
        return this.SpecType;
    }
    public void setSpecType(Integer SpecType) {
        this.SpecType = SpecType;
    }
    public Double getConfidence() {
        return this.Confidence;
    }
    public void setConfidence(Double Confidence) {
        this.Confidence = Confidence;
    }
    public String getInspector() {
        return this.Inspector;
    }
    public void setInspector(String Inspector) {
        this.Inspector = Inspector;
    }
    public Date getInspectorTime() {
        return this.InspectorTime;
    }
    public void setInspectorTime(java.sql.Timestamp InspectorTime) {
        this.InspectorTime = InspectorTime;
    }
    public Integer getInspectorPass() {
        return this.InspectorPass;
    }
    public void setInspectorPass(Integer InspectorPass) {
        this.InspectorPass = InspectorPass;
    }
    public String getPublisherMacAddress() {
        return this.PublisherMacAddress;
    }
    public void setPublisherMacAddress(String PublisherMacAddress) {
        this.PublisherMacAddress = PublisherMacAddress;
    }
    public String getReviewer() {
        return this.Reviewer;
    }
    public void setReviewer(String Reviewer) {
        this.Reviewer = Reviewer;
    }
    public Date getReviewerTime() {
        return this.ReviewerTime;
    }
    public void setReviewerTime(Date ReviewerTime) {
        this.ReviewerTime = ReviewerTime;
    }
    public Integer getReviewerPass() {
        return this.ReviewerPass;
    }
    public void setReviewerPass(Integer ReviewerPass) {
        this.ReviewerPass = ReviewerPass;
    }
    public String getReviewerMacAddress() {
        return this.ReviewerMacAddress;
    }
    public void setReviewerMacAddress(String ReviewerMacAddress) {
        this.ReviewerMacAddress = ReviewerMacAddress;
    }



    public void setInspectorTime(Date InspectorTime) {
        this.InspectorTime = InspectorTime;
        //this.InspectorTime = new Timestamp(InspectorTime.getTime());
    }
    
}
