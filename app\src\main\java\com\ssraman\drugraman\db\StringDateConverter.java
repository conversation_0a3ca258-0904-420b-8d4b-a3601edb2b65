package com.ssraman.drugraman.db;

import android.text.format.DateUtils;

import org.greenrobot.greendao.converter.PropertyConverter;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * @author: Administrator
 * @date: 2021/12/30
 */
public class StringDateConverter implements PropertyConverter<Date, String> {
//    private final static List<String> FORMATS = Arrays.asList(
//            "yyyy-MM-dd HH:mm:ss"
//    );
    private final static String FORMAT_STR ="yyyy-MM-dd HH:mm:ss";

    @Override
    public Date convertToEntityProperty(String databaseValue) {
        Date date = null;
//        for (String format : FORMATS) {
//            try {
//                date = DateUtils.parseDate(databaseValue, format);
//                break;
//            } catch (ParseException e) {
//                // do nothing
//            }
//        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(FORMAT_STR);
            date = format.parse(databaseValue);
        } catch (ParseException e) {
            // do nothing
        }
        if (date == null) {
            date = new Date(0);
        }
        return date;
    }

    @Override
    public String convertToDatabaseValue(Date entityProperty) {
        SimpleDateFormat format = new SimpleDateFormat(FORMAT_STR);
        return format.format(entityProperty);
    }
}
