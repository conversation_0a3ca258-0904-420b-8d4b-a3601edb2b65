package com.ssraman.drugraman.rbac.api;

import com.ssraman.drugraman.rbac.annotation.RequirePermission;
import com.ssraman.drugraman.rbac.annotation.RequireRole;
import com.ssraman.drugraman.rbac.dto.*;
import com.ssraman.drugraman.rbac.entity.Role;
import com.ssraman.drugraman.rbac.entity.User;
import com.ssraman.drugraman.rbac.service.IRoleService;

import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Observable;
import io.reactivex.Single;

/**
 * 角色管理API接口
 * 提供角色管理相关的REST API
 */
public class RoleManagementApi {
    
    private final IRoleService roleService;
    
    public RoleManagementApi(IRoleService roleService) {
        this.roleService = roleService;
    }
    
    // ========== 角色管理相关API ==========
    
    /**
     * 创建角色
     * POST /api/roles
     */
    @RequirePermission("ROLE_MANAGE")
    public Single<CreateRoleResponse> createRole(CreateRoleRequest request) {
        return roleService.createRole(request)
                .map(role -> {
                    CreateRoleResponse response = new CreateRoleResponse();
                    response.setSuccess(true);
                    response.setRole(role);
                    response.setMessage("角色创建成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    CreateRoleResponse response = new CreateRoleResponse();
                    response.setSuccess(false);
                    response.setMessage("角色创建失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 更新角色信息
     * PUT /api/roles/{roleId}
     */
    @RequirePermission("ROLE_MANAGE")
    public Single<UpdateRoleResponse> updateRole(Long roleId, UpdateRoleRequest request) {
        return roleService.updateRole(roleId, request)
                .map(role -> {
                    UpdateRoleResponse response = new UpdateRoleResponse();
                    response.setSuccess(true);
                    response.setRole(role);
                    response.setMessage("角色信息更新成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    UpdateRoleResponse response = new UpdateRoleResponse();
                    response.setSuccess(false);
                    response.setMessage("角色信息更新失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 删除角色
     * DELETE /api/roles/{roleId}
     */
    @RequirePermission("ROLE_MANAGE")
    public Single<DeleteRoleResponse> deleteRole(Long roleId) {
        return roleService.deleteRole(roleId)
                .toSingle(() -> {
                    DeleteRoleResponse response = new DeleteRoleResponse();
                    response.setSuccess(true);
                    response.setMessage("角色删除成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    DeleteRoleResponse response = new DeleteRoleResponse();
                    response.setSuccess(false);
                    response.setMessage("角色删除失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取角色详情
     * GET /api/roles/{roleId}
     */
    @RequirePermission("ROLE_MANAGE")
    public Single<GetRoleResponse> getRole(Long roleId) {
        return roleService.getRoleById(roleId)
                .map(role -> {
                    GetRoleResponse response = new GetRoleResponse();
                    response.setSuccess(true);
                    response.setRole(role);
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetRoleResponse response = new GetRoleResponse();
                    response.setSuccess(false);
                    response.setMessage("获取角色信息失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取所有角色
     * GET /api/roles
     */
    @RequirePermission("ROLE_MANAGE")
    public Single<GetRolesResponse> getAllRoles() {
        return roleService.getAllRoles()
                .firstOrError()
                .map(roles -> {
                    GetRolesResponse response = new GetRolesResponse();
                    response.setSuccess(true);
                    response.setRoles(roles);
                    response.setTotal(roles.size());
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetRolesResponse response = new GetRolesResponse();
                    response.setSuccess(false);
                    response.setMessage("获取角色列表失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    // ========== 用户角色关联管理API ==========
    
    /**
     * 为用户分配角色
     * POST /api/users/{userId}/roles
     */
    @RequirePermission("ROLE_ASSIGN")
    public Single<AssignRoleResponse> assignRoleToUser(Long userId, AssignRoleRequest request) {
        return roleService.assignRoleToUser(userId, request.getRoleId(), request.getAssignedBy())
                .toSingle(() -> {
                    AssignRoleResponse response = new AssignRoleResponse();
                    response.setSuccess(true);
                    response.setMessage("角色分配成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    AssignRoleResponse response = new AssignRoleResponse();
                    response.setSuccess(false);
                    response.setMessage("角色分配失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 撤销用户角色
     * DELETE /api/users/{userId}/roles/{roleId}
     */
    @RequirePermission("ROLE_REVOKE")
    public Single<RevokeRoleResponse> revokeRoleFromUser(Long userId, Long roleId) {
        return roleService.revokeRoleFromUser(userId, roleId)
                .toSingle(() -> {
                    RevokeRoleResponse response = new RevokeRoleResponse();
                    response.setSuccess(true);
                    response.setMessage("角色撤销成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    RevokeRoleResponse response = new RevokeRoleResponse();
                    response.setSuccess(false);
                    response.setMessage("角色撤销失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取用户的角色列表
     * GET /api/users/{userId}/roles
     */
    @RequirePermission("USER_VIEW")
    public Single<GetUserRolesResponse> getUserRoles(Long userId) {
        return roleService.getUserRoles(userId)
                .firstOrError()
                .map(roles -> {
                    GetUserRolesResponse response = new GetUserRolesResponse();
                    response.setSuccess(true);
                    response.setRoles(roles);
                    response.setTotal(roles.size());
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetUserRolesResponse response = new GetUserRolesResponse();
                    response.setSuccess(false);
                    response.setMessage("获取用户角色失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取角色的用户列表
     * GET /api/roles/{roleId}/users
     */
    @RequirePermission("ROLE_MANAGE")
    public Single<GetRoleUsersResponse> getRoleUsers(Long roleId) {
        return roleService.getRoleUsers(roleId)
                .firstOrError()
                .map(users -> {
                    GetRoleUsersResponse response = new GetRoleUsersResponse();
                    response.setSuccess(true);
                    response.setUsers(users);
                    response.setTotal(users.size());
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetRoleUsersResponse response = new GetRoleUsersResponse();
                    response.setSuccess(false);
                    response.setMessage("获取角色用户失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 批量分配角色
     * POST /api/users/{userId}/roles/batch
     */
    @RequirePermission("ROLE_ASSIGN")
    public Single<BatchAssignRoleResponse> batchAssignRoles(Long userId, BatchAssignRoleRequest request) {
        BatchAssignRoleResponse response = new BatchAssignRoleResponse();
        
        // 使用Completable.merge来并行处理多个角色分配
        List<Completable> assignTasks = new java.util.ArrayList<>();
        for (Long roleId : request.getRoleIds()) {
            Completable assignTask = roleService.assignRoleToUser(userId, roleId, request.getAssignedBy())
                    .doOnComplete(() -> response.addSuccessRole(roleId))
                    .doOnError(throwable -> response.addFailedRole(roleId, throwable.getMessage()));
            assignTasks.add(assignTask);
        }
        
        return Completable.merge(assignTasks)
                .toSingle(() -> {
                    response.setSuccess(response.getFailedRoles().isEmpty());
                    if (response.isSuccess()) {
                        response.setMessage("所有角色分配成功");
                    } else {
                        response.setMessage("部分角色分配失败");
                    }
                    return response;
                })
                .onErrorReturn(throwable -> {
                    response.setSuccess(false);
                    response.setMessage("批量角色分配失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 批量撤销角色
     * DELETE /api/users/{userId}/roles/batch
     */
    @RequirePermission("ROLE_REVOKE")
    public Single<BatchRevokeRoleResponse> batchRevokeRoles(Long userId, BatchRevokeRoleRequest request) {
        BatchRevokeRoleResponse response = new BatchRevokeRoleResponse();
        
        // 使用Completable.merge来并行处理多个角色撤销
        List<Completable> revokeTasks = new java.util.ArrayList<>();
        for (Long roleId : request.getRoleIds()) {
            Completable revokeTask = roleService.revokeRoleFromUser(userId, roleId)
                    .doOnComplete(() -> response.addSuccessRole(roleId))
                    .doOnError(throwable -> response.addFailedRole(roleId, throwable.getMessage()));
            revokeTasks.add(revokeTask);
        }
        
        return Completable.merge(revokeTasks)
                .toSingle(() -> {
                    response.setSuccess(response.getFailedRoles().isEmpty());
                    if (response.isSuccess()) {
                        response.setMessage("所有角色撤销成功");
                    } else {
                        response.setMessage("部分角色撤销失败");
                    }
                    return response;
                })
                .onErrorReturn(throwable -> {
                    response.setSuccess(false);
                    response.setMessage("批量角色撤销失败: " + throwable.getMessage());
                    return response;
                });
    }
}
