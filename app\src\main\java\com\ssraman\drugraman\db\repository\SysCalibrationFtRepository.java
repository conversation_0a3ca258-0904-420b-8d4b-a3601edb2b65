package com.ssraman.drugraman.db.repository;

import com.ssraman.drugraman.db.entity.CalibrationFtInfo;
import com.ssraman.drugraman.db.entity.FtInfo;
import com.ssraman.drugraman.db.gen.CalibrationFtInfoDao;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.gen.FtInfoDao;

import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Completable;

/**
 * @author: Administrator
 * @date: 2022/7/18
 */
public class SysCalibrationFtRepository {
    private DaoSession mDaoSession;
    private CalibrationFtInfoDao calibrationFtDao;

    public SysCalibrationFtRepository(DaoSession mDaoSession) {
        this.mDaoSession = mDaoSession;
        calibrationFtDao=this.mDaoSession.getCalibrationFtInfoDao();
    }


    public Completable insertCalibrationFt(CalibrationFtInfo calibrationFtInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                calibrationFtDao.insert(calibrationFtInfo);
                return null;
            }
        });
    }

    public long insertCalibrationFtByRe(CalibrationFtInfo calibrationFtInfo)
    {
        return calibrationFtDao.insert(calibrationFtInfo);
    }

    public Completable updateCalibrationFt(CalibrationFtInfo calibrationFtInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                calibrationFtDao.update(calibrationFtInfo);
                return null;
            }
        });
    }

    public void updateCalibrationFtByRe(CalibrationFtInfo calibrationFtInfo)
    {
        List<CalibrationFtInfo> list = calibrationFtDao.queryBuilder().list();
        if(list.size()>0)
        {
            calibrationFtInfo.setId(list.get(0).getId());
            calibrationFtDao.update(calibrationFtInfo);
        }
        else
        {
            calibrationFtDao.insert(calibrationFtInfo);
        }

    }

    public CalibrationFtInfo getCalibrationFtInfo()
    {
        List<CalibrationFtInfo> list = calibrationFtDao.queryBuilder().list();
        if(list.size()>0)
        {
            return list.get(0);
        }
        return null;
    }


}
