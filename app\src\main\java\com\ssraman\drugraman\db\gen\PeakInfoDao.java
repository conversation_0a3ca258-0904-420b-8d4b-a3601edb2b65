package com.ssraman.drugraman.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.ssraman.drugraman.db.entity.PeakInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "Peak".
*/
public class PeakInfoDao extends AbstractDao<PeakInfo, Long> {

    public static final String TABLENAME = "Peak";

    /**
     * Properties of entity PeakInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "Id", true, "Id");
        public final static Property FtId = new Property(1, Integer.class, "FtId", false, "FtId");
        public final static Property PeakId = new Property(2, Integer.class, "PeakId", false, "PeakId");
        public final static Property SendWave = new Property(3, Double.class, "SendWave", false, "SendWave");
        public final static Property Lumbda = new Property(4, Double.class, "Lumbda", false, "Lumbda");
        public final static Property Wave = new Property(5, Double.class, "Wave", false, "Wave");
        public final static Property Intensity = new Property(6, Double.class, "Intensity", false, "Intensity");
        public final static Property Type = new Property(7, Integer.class, "Type", false, "Type");
        public final static Property Must = new Property(8, Integer.class, "Must", false, "Must");
        public final static Property StartIntensity = new Property(9, Double.class, "StartIntensity", false, "StartIntensity");
        public final static Property Calibration = new Property(10, Double.class, "Calibration", false, "Calibration");
        public final static Property EnableCalibration = new Property(11, Integer.class, "EnableCalibration", false, "EnableCalibration");
        public final static Property MatchLimit = new Property(12, Double.class, "MatchLimit", false, "MatchLimit");
    }


    public PeakInfoDao(DaoConfig config) {
        super(config);
    }
    
    public PeakInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, PeakInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        Integer FtId = entity.getFtId();
        if (FtId != null) {
            stmt.bindLong(2, FtId);
        }
 
        Integer PeakId = entity.getPeakId();
        if (PeakId != null) {
            stmt.bindLong(3, PeakId);
        }
 
        Double SendWave = entity.getSendWave();
        if (SendWave != null) {
            stmt.bindDouble(4, SendWave);
        }
 
        Double Lumbda = entity.getLumbda();
        if (Lumbda != null) {
            stmt.bindDouble(5, Lumbda);
        }
 
        Double Wave = entity.getWave();
        if (Wave != null) {
            stmt.bindDouble(6, Wave);
        }
 
        Double Intensity = entity.getIntensity();
        if (Intensity != null) {
            stmt.bindDouble(7, Intensity);
        }
 
        Integer Type = entity.getType();
        if (Type != null) {
            stmt.bindLong(8, Type);
        }
 
        Integer Must = entity.getMust();
        if (Must != null) {
            stmt.bindLong(9, Must);
        }
 
        Double StartIntensity = entity.getStartIntensity();
        if (StartIntensity != null) {
            stmt.bindDouble(10, StartIntensity);
        }
 
        Double Calibration = entity.getCalibration();
        if (Calibration != null) {
            stmt.bindDouble(11, Calibration);
        }
 
        Integer EnableCalibration = entity.getEnableCalibration();
        if (EnableCalibration != null) {
            stmt.bindLong(12, EnableCalibration);
        }
 
        Double MatchLimit = entity.getMatchLimit();
        if (MatchLimit != null) {
            stmt.bindDouble(13, MatchLimit);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, PeakInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        Integer FtId = entity.getFtId();
        if (FtId != null) {
            stmt.bindLong(2, FtId);
        }
 
        Integer PeakId = entity.getPeakId();
        if (PeakId != null) {
            stmt.bindLong(3, PeakId);
        }
 
        Double SendWave = entity.getSendWave();
        if (SendWave != null) {
            stmt.bindDouble(4, SendWave);
        }
 
        Double Lumbda = entity.getLumbda();
        if (Lumbda != null) {
            stmt.bindDouble(5, Lumbda);
        }
 
        Double Wave = entity.getWave();
        if (Wave != null) {
            stmt.bindDouble(6, Wave);
        }
 
        Double Intensity = entity.getIntensity();
        if (Intensity != null) {
            stmt.bindDouble(7, Intensity);
        }
 
        Integer Type = entity.getType();
        if (Type != null) {
            stmt.bindLong(8, Type);
        }
 
        Integer Must = entity.getMust();
        if (Must != null) {
            stmt.bindLong(9, Must);
        }
 
        Double StartIntensity = entity.getStartIntensity();
        if (StartIntensity != null) {
            stmt.bindDouble(10, StartIntensity);
        }
 
        Double Calibration = entity.getCalibration();
        if (Calibration != null) {
            stmt.bindDouble(11, Calibration);
        }
 
        Integer EnableCalibration = entity.getEnableCalibration();
        if (EnableCalibration != null) {
            stmt.bindLong(12, EnableCalibration);
        }
 
        Double MatchLimit = entity.getMatchLimit();
        if (MatchLimit != null) {
            stmt.bindDouble(13, MatchLimit);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public PeakInfo readEntity(Cursor cursor, int offset) {
        PeakInfo entity = new PeakInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // Id
            cursor.isNull(offset + 1) ? null : cursor.getInt(offset + 1), // FtId
            cursor.isNull(offset + 2) ? null : cursor.getInt(offset + 2), // PeakId
            cursor.isNull(offset + 3) ? null : cursor.getDouble(offset + 3), // SendWave
            cursor.isNull(offset + 4) ? null : cursor.getDouble(offset + 4), // Lumbda
            cursor.isNull(offset + 5) ? null : cursor.getDouble(offset + 5), // Wave
            cursor.isNull(offset + 6) ? null : cursor.getDouble(offset + 6), // Intensity
            cursor.isNull(offset + 7) ? null : cursor.getInt(offset + 7), // Type
            cursor.isNull(offset + 8) ? null : cursor.getInt(offset + 8), // Must
            cursor.isNull(offset + 9) ? null : cursor.getDouble(offset + 9), // StartIntensity
            cursor.isNull(offset + 10) ? null : cursor.getDouble(offset + 10), // Calibration
            cursor.isNull(offset + 11) ? null : cursor.getInt(offset + 11), // EnableCalibration
            cursor.isNull(offset + 12) ? null : cursor.getDouble(offset + 12) // MatchLimit
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, PeakInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setFtId(cursor.isNull(offset + 1) ? null : cursor.getInt(offset + 1));
        entity.setPeakId(cursor.isNull(offset + 2) ? null : cursor.getInt(offset + 2));
        entity.setSendWave(cursor.isNull(offset + 3) ? null : cursor.getDouble(offset + 3));
        entity.setLumbda(cursor.isNull(offset + 4) ? null : cursor.getDouble(offset + 4));
        entity.setWave(cursor.isNull(offset + 5) ? null : cursor.getDouble(offset + 5));
        entity.setIntensity(cursor.isNull(offset + 6) ? null : cursor.getDouble(offset + 6));
        entity.setType(cursor.isNull(offset + 7) ? null : cursor.getInt(offset + 7));
        entity.setMust(cursor.isNull(offset + 8) ? null : cursor.getInt(offset + 8));
        entity.setStartIntensity(cursor.isNull(offset + 9) ? null : cursor.getDouble(offset + 9));
        entity.setCalibration(cursor.isNull(offset + 10) ? null : cursor.getDouble(offset + 10));
        entity.setEnableCalibration(cursor.isNull(offset + 11) ? null : cursor.getInt(offset + 11));
        entity.setMatchLimit(cursor.isNull(offset + 12) ? null : cursor.getDouble(offset + 12));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(PeakInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(PeakInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(PeakInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
