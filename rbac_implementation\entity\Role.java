package com.ssraman.drugraman.rbac.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.ToMany;
import org.greenrobot.greendao.annotation.Unique;
import org.greenrobot.greendao.annotation.Generated;

import java.util.Date;
import java.util.List;

/**
 * 角色实体类
 * 定义系统中的角色信息
 */
@Entity(nameInDb = "tb_role")
public class Role {
    
    @Id(autoincrement = true)
    @Property(nameInDb = "id")
    private Long id;
    
    @Property(nameInDb = "role_code")
    @Unique
    private String roleCode;
    
    @Property(nameInDb = "role_name")
    private String roleName;
    
    @Property(nameInDb = "description")
    private String description;
    
    @Property(nameInDb = "level")
    private Integer level; // 角色级别，数字越大权限越高
    
    @Property(nameInDb = "status")
    private Integer status; // 0:禁用, 1:启用
    
    @Property(nameInDb = "created_at")
    private Date createdAt;
    
    @Property(nameInDb = "updated_at")
    private Date updatedAt;
    
    @ToMany(referencedJoinProperty = "roleId")
    private List<RolePermission> rolePermissions;
    
    @ToMany(referencedJoinProperty = "roleId")
    private List<UserRole> userRoles;

    @Generated(hash = 1355838961)
    public Role(Long id, String roleCode, String roleName, String description,
            Integer level, Integer status, Date createdAt, Date updatedAt) {
        this.id = id;
        this.roleCode = roleCode;
        this.roleName = roleName;
        this.description = description;
        this.level = level;
        this.status = status;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    @Generated(hash = 1044386176)
    public Role() {
    }
    
    // 构造函数
    public Role(String roleCode, String roleName, String description, Integer level) {
        this.roleCode = roleCode;
        this.roleName = roleName;
        this.description = description;
        this.level = level;
        this.status = 1; // 默认启用
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }
    
    // 业务方法
    public boolean isEnabled() {
        return status != null && status == 1;
    }
    
    public void enable() {
        this.status = 1;
        this.updatedAt = new Date();
    }
    
    public void disable() {
        this.status = 0;
        this.updatedAt = new Date();
    }
    
    public boolean hasHigherLevelThan(Role otherRole) {
        if (otherRole == null || this.level == null || otherRole.getLevel() == null) {
            return false;
        }
        return this.level > otherRole.getLevel();
    }
    
    public boolean hasLowerLevelThan(Role otherRole) {
        if (otherRole == null || this.level == null || otherRole.getLevel() == null) {
            return false;
        }
        return this.level < otherRole.getLevel();
    }
    
    public boolean hasSameLevelAs(Role otherRole) {
        if (otherRole == null || this.level == null || otherRole.getLevel() == null) {
            return false;
        }
        return this.level.equals(otherRole.getLevel());
    }

    // Getters and Setters
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRoleCode() {
        return this.roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return this.roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getLevel() {
        return this.level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<RolePermission> getRolePermissions() {
        return this.rolePermissions;
    }

    public void setRolePermissions(List<RolePermission> rolePermissions) {
        this.rolePermissions = rolePermissions;
    }

    public List<UserRole> getUserRoles() {
        return this.userRoles;
    }

    public void setUserRoles(List<UserRole> userRoles) {
        this.userRoles = userRoles;
    }
}
