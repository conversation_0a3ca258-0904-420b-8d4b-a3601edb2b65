<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
xmlns:tools="http://schemas.android.com/tools">

<data>

    <variable
        name="mpresenter"
        type="com.ssraman.drugraman.ui.other.AddLibDetectionSpecFragment.MPresenter" />

    <variable
        name="addLibDetectionSpecViewModel"
        type="com.ssraman.drugraman.ui.vm.AddLibDetectionSpecViewModel" />
</data>

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/nc_main"
    tools:context=".ui.other.AddLibDetectionSpecFragment">


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.51" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.08" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline11"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.08" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.92" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:drawableTop="@drawable/ic_spec_compare_24"
        android:text="@string/title_spectrum_details"
        android:textColor="@color/nc_light"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/guideline11"
        app:layout_constraintEnd_toStartOf="@+id/guideline10"
        app:layout_constraintStart_toStartOf="@+id/guideline10" />

    <com.ssraman.drugraman.custom.SpectrumLineChart
        android:id="@+id/spec_detection_result1"
        android:layout_width="0dp"
        android:layout_height="245dp"
        android:layout_marginTop="5dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline12"
        app:layout_constraintStart_toStartOf="@+id/guideline7"
        app:layout_constraintTop_toTopOf="@+id/guideline11" />

    <com.ssraman.drugraman.sortableview.PeakSorTableView
        android:id="@+id/table_addlib_peak_main"
        android:layout_width="300dp"
        android:layout_height="240dp"
        android:layout_marginTop="8dp"
        app:layout_constraintStart_toStartOf="@+id/guideline7"
        app:layout_constraintTop_toBottomOf="@+id/spec_detection_result1" />

    <TextView
        android:id="@+id/btn_add_standardlib1"
        style="@style/ButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="20dp"
        android:drawableTop="@drawable/ic_add_lib_32"
        android:onClick="@{(view)->mpresenter.BtnAddStandardLibClick(view)}"
        android:text="@string/btn_add_to_library"
        android:textColor="@color/nc_light"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/btn_next_sample1"
        style="@style/ButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:drawableTop="@drawable/ic_next_32"
        android:onClick="@{(view)->mpresenter.BtnNextSampleClick(view)}"
        android:text="@string/btn_next_sample"
        android:textColor="@color/nc_light"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/btn_share1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:drawableStart="@drawable/ic_share_32"
        android:onClick="@{(view)->mpresenter.BtnShareClick(view)}"
        android:textColor="@color/nc_light"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@+id/guideline11"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>