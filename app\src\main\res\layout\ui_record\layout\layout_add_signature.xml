<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/nc_main"
    android:orientation="vertical"
    android:paddingLeft="10dp"
    android:paddingRight="10dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/nc_sub"
        android:layout_centerInParent="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_add_signature_title"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginBottom="10dp"
                android:background="@color/gray"
                android:gravity="left|center"
                android:text="@string/title_add_signature"
                android:textColor="@color/black"
                android:textSize="17dp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="5dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="2dp"
                    android:text="@string/title_signer_with_colon"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/txt_signer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="2dp"
                    android:text="@string/title_inspector_one"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="20dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="2dp"
                    android:text="@string/title_signature_meaning_with_colon"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <com.ssraman.control.spinner.MaterialSpinner
                    android:id="@+id/et_signature_meaning"
                    android:layout_width="200dp"
                    android:layout_height="44dp"
                    android:background="@drawable/input_border_bottom_border"
                    android:textSize="24sp"
                    app:ms_background_color="@color/dark_gray"
                    app:ms_popupwindow_height="wrap_content"
                    app:ms_popupwindow_maxheight="200dp"
                    app:ms_text_color="@android:color/black"
                    tools:layout_editor_absoluteX="129dp"
                    tools:layout_editor_absoluteY="69dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="20dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="2dp"
                    android:text="@string/title_method_protection_with_colon"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"

                    android:text="@string/title_method_protection_text"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="2dp"
                    android:text="@string/title_mac_address_with_colon"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/txt_mac_adress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="2dp"
                    android:text="@string/title_mac_address_default"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/chk_continutation_test"
                    style="@style/CustomCheckboxSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:paddingLeft="10dp"
                    android:paddingRight="2dp"
                    android:text="@string/title_time_limit_with_colon"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <RelativeLayout
                    android:id="@+id/relativeLayout"
                    android:layout_width="140dp"
                    android:layout_height="32dp"
                    android:layout_gravity="right"
                    android:background="@drawable/shape_stroke_white"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/txt_cal_start"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:paddingLeft="2dp"
                        android:textColor="@color/black"
                        tools:text="@string/title_date_format" />

                    <ImageView
                        android:id="@+id/imv_cal_start"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="6dp"
                        android:onClick="@{(view)->mpresenter.ImvCalStartClick(view)}"
                        android:src="@drawable/ic_calendar_28" />
                </RelativeLayout>

            </LinearLayout>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginTop="10dp"
                android:background="@color/nc_light" />

            <LinearLayout
                android:id="@+id/box_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_signature_cancel"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_dialog_ios_left_light"
                    android:clickable="true"
                    android:gravity="center"
                    android:text="@string/title_cancel"
                    android:textColor="@color/nc_light"
                    android:textSize="17dp" />

                <ImageView
                    android:layout_width="1px"
                    android:layout_height="match_parent"
                    android:background="@color/nc_light" />

                <TextView
                    android:id="@+id/btn_signature_ok"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_dialog_ios_right_light"
                    android:clickable="true"
                    android:gravity="center"
                    android:text="@string/title_signature"
                    android:textColor="@color/nc_light"
                    android:textSize="17dp" />
            </LinearLayout>

        </LinearLayout>


    </RelativeLayout>
</RelativeLayout>
