package com.ssraman.drugraman.db;

import android.content.Context;

import com.ssraman.drugraman.db.gen.DaoMaster;
import com.ssraman.drugraman.db.gen.DaoSession;

import org.greenrobot.greendao.database.Database;

/**
 * @author: Administrator
 * @date: 2021/6/20
 */
public class ArchiveDatabase {
    private DaoMaster mDaoMaster;
    private DaoSession mDaoSession;
    private static ArchiveDatabase mInstance;
    //public static boolean ENCRYPTED = true;

    public static ArchiveDatabase getInstance(Context context, String dbName_i){
        final String dbName=dbName_i;
        if (mInstance==null){
            //保证异步处理安全操作
            synchronized (ArchiveDatabase .class){
                if (mInstance==null){
                    mInstance=new ArchiveDatabase (context,dbName);
                }
            }
        }
        return mInstance;
    }

    private ArchiveDatabase (Context context, String dbName){
        if (mInstance==null){
            final boolean ENCRYPTED=true;
            //DaoMaster.DevOpenHelper devOpenHelper = new DaoMaster.DevOpenHelper(context, dbName, null);
            DaoMaster.DevOpenHelper devOpenHelper = new DaoMaster.DevOpenHelper(context, dbName);
            Database db = ENCRYPTED ? devOpenHelper.getEncryptedWritableDb("www.ss-raman.com") : devOpenHelper.getWritableDb();
            mDaoMaster = new DaoMaster(db);
            mDaoSession=mDaoMaster.newSession();
        }
    }

    public DaoMaster getMaster(){
        return mDaoMaster;
    }
    public DaoSession getSession(){
        return mDaoSession;
    }
    public DaoSession getNewSession(){
        mDaoSession=mDaoMaster.newSession();
        return mDaoSession;
    }

}
