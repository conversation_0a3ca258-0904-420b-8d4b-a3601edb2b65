package com.ssraman.drugraman.rbac.api;

import com.ssraman.drugraman.rbac.annotation.RequirePermission;
import com.ssraman.drugraman.rbac.dto.*;
import com.ssraman.drugraman.rbac.entity.Permission;
import com.ssraman.drugraman.rbac.service.IPermissionService;
import com.ssraman.drugraman.rbac.security.PermissionChecker;
import com.ssraman.drugraman.rbac.security.SecurityContext;

import java.util.List;
import java.util.Set;

import io.reactivex.Completable;
import io.reactivex.Single;

/**
 * 权限管理API接口
 * 提供权限管理相关的REST API
 */
public class PermissionManagementApi {
    
    private final IPermissionService permissionService;
    private final PermissionChecker permissionChecker;
    
    public PermissionManagementApi(IPermissionService permissionService, PermissionChecker permissionChecker) {
        this.permissionService = permissionService;
        this.permissionChecker = permissionChecker;
    }
    
    // ========== 权限管理相关API ==========
    
    /**
     * 创建权限
     * POST /api/permissions
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<CreatePermissionResponse> createPermission(CreatePermissionRequest request) {
        return permissionService.createPermission(request)
                .map(permission -> {
                    CreatePermissionResponse response = new CreatePermissionResponse();
                    response.setSuccess(true);
                    response.setPermission(permission);
                    response.setMessage("权限创建成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    CreatePermissionResponse response = new CreatePermissionResponse();
                    response.setSuccess(false);
                    response.setMessage("权限创建失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 更新权限信息
     * PUT /api/permissions/{permissionId}
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<UpdatePermissionResponse> updatePermission(Long permissionId, UpdatePermissionRequest request) {
        return permissionService.updatePermission(permissionId, request)
                .map(permission -> {
                    UpdatePermissionResponse response = new UpdatePermissionResponse();
                    response.setSuccess(true);
                    response.setPermission(permission);
                    response.setMessage("权限信息更新成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    UpdatePermissionResponse response = new UpdatePermissionResponse();
                    response.setSuccess(false);
                    response.setMessage("权限信息更新失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 删除权限
     * DELETE /api/permissions/{permissionId}
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<DeletePermissionResponse> deletePermission(Long permissionId) {
        return permissionService.deletePermission(permissionId)
                .toSingle(() -> {
                    DeletePermissionResponse response = new DeletePermissionResponse();
                    response.setSuccess(true);
                    response.setMessage("权限删除成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    DeletePermissionResponse response = new DeletePermissionResponse();
                    response.setSuccess(false);
                    response.setMessage("权限删除失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取权限详情
     * GET /api/permissions/{permissionId}
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<GetPermissionResponse> getPermission(Long permissionId) {
        return permissionService.getPermissionById(permissionId)
                .map(permission -> {
                    GetPermissionResponse response = new GetPermissionResponse();
                    response.setSuccess(true);
                    response.setPermission(permission);
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetPermissionResponse response = new GetPermissionResponse();
                    response.setSuccess(false);
                    response.setMessage("获取权限信息失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取所有权限
     * GET /api/permissions
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<GetPermissionsResponse> getAllPermissions() {
        return permissionService.getAllPermissions()
                .firstOrError()
                .map(permissions -> {
                    GetPermissionsResponse response = new GetPermissionsResponse();
                    response.setSuccess(true);
                    response.setPermissions(permissions);
                    response.setTotal(permissions.size());
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetPermissionsResponse response = new GetPermissionsResponse();
                    response.setSuccess(false);
                    response.setMessage("获取权限列表失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    // ========== 角色权限关联管理API ==========
    
    /**
     * 为角色分配权限
     * POST /api/roles/{roleId}/permissions
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<AssignPermissionResponse> assignPermissionToRole(Long roleId, AssignPermissionRequest request) {
        return permissionService.assignPermissionToRole(roleId, request.getPermissionId())
                .toSingle(() -> {
                    AssignPermissionResponse response = new AssignPermissionResponse();
                    response.setSuccess(true);
                    response.setMessage("权限分配成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    AssignPermissionResponse response = new AssignPermissionResponse();
                    response.setSuccess(false);
                    response.setMessage("权限分配失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 撤销角色权限
     * DELETE /api/roles/{roleId}/permissions/{permissionId}
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<RevokePermissionResponse> revokePermissionFromRole(Long roleId, Long permissionId) {
        return permissionService.revokePermissionFromRole(roleId, permissionId)
                .toSingle(() -> {
                    RevokePermissionResponse response = new RevokePermissionResponse();
                    response.setSuccess(true);
                    response.setMessage("权限撤销成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    RevokePermissionResponse response = new RevokePermissionResponse();
                    response.setSuccess(false);
                    response.setMessage("权限撤销失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取角色的权限列表
     * GET /api/roles/{roleId}/permissions
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<GetRolePermissionsResponse> getRolePermissions(Long roleId) {
        return permissionService.getRolePermissions(roleId)
                .firstOrError()
                .map(permissions -> {
                    GetRolePermissionsResponse response = new GetRolePermissionsResponse();
                    response.setSuccess(true);
                    response.setPermissions(permissions);
                    response.setTotal(permissions.size());
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetRolePermissionsResponse response = new GetRolePermissionsResponse();
                    response.setSuccess(false);
                    response.setMessage("获取角色权限失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取用户的权限列表
     * GET /api/users/{userId}/permissions
     */
    @RequirePermission("USER_VIEW")
    public Single<GetUserPermissionsResponse> getUserPermissions(Long userId) {
        return permissionService.getUserPermissions(userId)
                .firstOrError()
                .map(permissions -> {
                    GetUserPermissionsResponse response = new GetUserPermissionsResponse();
                    response.setSuccess(true);
                    response.setPermissions(permissions);
                    response.setTotal(permissions.size());
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetUserPermissionsResponse response = new GetUserPermissionsResponse();
                    response.setSuccess(false);
                    response.setMessage("获取用户权限失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 检查用户权限
     * GET /api/users/{userId}/permissions/check
     */
    public Single<CheckPermissionResponse> checkUserPermission(Long userId, CheckPermissionRequest request) {
        CheckPermissionResponse response = new CheckPermissionResponse();
        
        try {
            boolean hasPermission;
            
            if (request.getPermissionCode() != null && !request.getPermissionCode().isEmpty()) {
                hasPermission = permissionChecker.hasPermission(userId, request.getPermissionCode());
            } else if (request.getResource() != null && request.getAction() != null) {
                hasPermission = permissionChecker.hasPermission(userId, request.getResource(), request.getAction());
            } else {
                response.setSuccess(false);
                response.setMessage("权限检查参数不完整");
                return Single.just(response);
            }
            
            response.setSuccess(true);
            response.setHasPermission(hasPermission);
            response.setMessage(hasPermission ? "用户具有该权限" : "用户不具有该权限");
            
        } catch (Exception e) {
            response.setSuccess(false);
            response.setHasPermission(false);
            response.setMessage("权限检查失败: " + e.getMessage());
        }
        
        return Single.just(response);
    }
    
    /**
     * 获取当前用户权限
     * GET /api/auth/permissions
     */
    public Single<GetCurrentUserPermissionsResponse> getCurrentUserPermissions() {
        Long currentUserId = SecurityContext.getCurrentUserId();
        if (currentUserId == null) {
            GetCurrentUserPermissionsResponse response = new GetCurrentUserPermissionsResponse();
            response.setSuccess(false);
            response.setMessage("用户未登录");
            return Single.just(response);
        }
        
        Set<String> permissionCodes = permissionChecker.getUserPermissionCodes(currentUserId);
        
        GetCurrentUserPermissionsResponse response = new GetCurrentUserPermissionsResponse();
        response.setSuccess(true);
        response.setPermissionCodes(permissionCodes);
        response.setTotal(permissionCodes.size());
        
        return Single.just(response);
    }
    
    /**
     * 批量分配权限
     * POST /api/roles/{roleId}/permissions/batch
     */
    @RequirePermission("PERMISSION_MANAGE")
    public Single<BatchAssignPermissionResponse> batchAssignPermissions(Long roleId, BatchAssignPermissionRequest request) {
        BatchAssignPermissionResponse response = new BatchAssignPermissionResponse();
        
        // 使用Completable.merge来并行处理多个权限分配
        List<Completable> assignTasks = new java.util.ArrayList<>();
        for (Long permissionId : request.getPermissionIds()) {
            Completable assignTask = permissionService.assignPermissionToRole(roleId, permissionId)
                    .doOnComplete(() -> response.addSuccessPermission(permissionId))
                    .doOnError(throwable -> response.addFailedPermission(permissionId, throwable.getMessage()));
            assignTasks.add(assignTask);
        }
        
        return Completable.merge(assignTasks)
                .toSingle(() -> {
                    response.setSuccess(response.getFailedPermissions().isEmpty());
                    if (response.isSuccess()) {
                        response.setMessage("所有权限分配成功");
                    } else {
                        response.setMessage("部分权限分配失败");
                    }
                    return response;
                })
                .onErrorReturn(throwable -> {
                    response.setSuccess(false);
                    response.setMessage("批量权限分配失败: " + throwable.getMessage());
                    return response;
                });
    }
}
