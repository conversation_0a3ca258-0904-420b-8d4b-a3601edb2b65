package com.ssraman.drugraman.custom;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.github.mikephil.charting.components.MarkerView;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.utils.MPPointF;
import com.ssraman.drugraman.R;

import java.text.DecimalFormat;

/**
 * @author: Administrator
 * @date: 2021/6/25
 */
@SuppressLint("ViewConstructor")
public class SpectrumMarkerView  extends MarkerView {

    private final TextView tvContentX;
    private final TextView tvContentY;
    private DecimalFormat format = new DecimalFormat(".00");

    public SpectrumMarkerView(Context context, int layoutResource) {
        super(context, layoutResource);
        tvContentX = findViewById(R.id.tvContentX);
        tvContentY = findViewById(R.id.tvContentY);
    }

    @Override
    public void refreshContent(Entry e, Highlight highlight) {
        tvContentX.setText("X : " + format.format(e.getX()));
        tvContentY.setText("Y : " + format.format(e.getY()));
        super.refreshContent(e, highlight);
    }

    @Override
    public MPPointF getOffset() {
        return new MPPointF(-(getWidth() / 2), -getHeight()*2);
    }

}
