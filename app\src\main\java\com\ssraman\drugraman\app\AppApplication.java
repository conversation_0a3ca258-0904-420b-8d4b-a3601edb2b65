package com.ssraman.drugraman.app;

import android.app.Activity;
import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;

import com.jeremyliao.liveeventbus.LiveEventBus;
import com.kongzue.dialog.util.BaseDialog;
import com.kongzue.dialog.util.DialogSettings;
import com.kongzue.dialog.v3.Notification;
//import com.mikepenz.iconics.Iconics;
import com.ssraman.drugraman.BuildConfig;
//import com.ssraman.drugraman.typeface.CustomFont;
import com.ssraman.drugraman.ui.MainActivity;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.util.UIContextUtils;
import com.ssraman.lib_common.base.BaseApplication;
import com.ssraman.lib_common.crash.CaocConfig;
import com.ssraman.lib_common.crash.MyCrashHandler;
import com.ssraman.lib_common.utils.KLog;

import net.sqlcipher.database.SQLiteDatabase;

public class AppApplication extends BaseApplication implements ViewModelStoreOwner {

    private ViewModelStore mAppViewModelStore;
    private ViewModelProvider.Factory mFactory;

    @Override
    public void onCreate() {
        super.onCreate();
        mAppViewModelStore = new ViewModelStore();
        MyCrashHandler handler = new MyCrashHandler(this);
        Thread.setDefaultUncaughtExceptionHandler(handler);
        KLog.init(BuildConfig.DEBUG);
        initCrash();
        DialogInit();
        UIContextUtils.init(getApplicationContext());
        LiveEventBus
                .config()
                .autoClear(true)
                .lifecycleObserverAlwaysActive(true);
    }

    @Override
    public void onTerminate() {
        BaseDialog.unload();
        super.onTerminate();
    }

    private void initCrash() {
        CaocConfig.Builder.create()
                .backgroundMode(CaocConfig.BACKGROUND_MODE_SILENT)
                .enabled(true)
                .showErrorDetails(true)
                .showRestartButton(true)
                .trackActivities(true)
                .minTimeBetweenCrashesMs(2000)
                .errorDrawable(R.mipmap.ic_launcher)
                .restartActivity(MainActivity.class)
                .apply();
    }

    private void DialogInit() {
        DialogSettings.init();
        DialogSettings.checkRenderscriptSupport(this);
        DialogSettings.DEBUGMODE = true;
        DialogSettings.isUseBlur = true;
        DialogSettings.autoShowInputKeyboard = true;
        DialogSettings.style = DialogSettings.STYLE.STYLE_IOS;
        DialogSettings.theme = DialogSettings.THEME.DARK;
        DialogSettings.cancelable = false;
        Notification.mode = Notification.Mode.FLOATING_WINDOW;
    }

    @NonNull
    @Override
    public ViewModelStore getViewModelStore() {
        return mAppViewModelStore;
    }

    public ViewModelProvider getAppViewModelProvider(Activity activity) {
        return new ViewModelProvider((AppApplication) activity.getApplicationContext(),
                ((AppApplication) activity.getApplicationContext()).getAppFactory(activity));
    }

    private ViewModelProvider.Factory getAppFactory(Activity activity) {
        Application application = checkApplication(activity);
        if (mFactory == null) {
            mFactory = ViewModelProvider.AndroidViewModelFactory.getInstance(application);
        }
        return mFactory;
    }

    private Application checkApplication(Activity activity) {
        Application application = activity.getApplication();
        if (application == null) {
            throw new IllegalStateException("Your activity/fragment is not yet attached to "
                    + "Application. You can't request ViewModel before onCreate call.");
        }
        return application;
    }

}
