package com.ssraman.drugraman.typeface;

/**
 * @author: Administrator
 * @date: 2021/10/11
 */
public enum HomeFuncType {
    Verification(0), // 验证
    Identification(1), // 识别
    BatchValidation(2), // 批验证
    Brown_record(3), // 记录
    Settings(4);// 设置

    private int value = 0;

    private HomeFuncType(int value) { // 必须是private的，否则编译错误
        this.value = value;
    }

    public static HomeFuncType valueOf(int value) { // 手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return Verification;
            case 1:
                return Identification;
            case 2:
                return BatchValidation;
            case 3:
                return Brown_record;
            case 4:
                return Settings;
            default:
                return Verification;
        }
    }

    public int value() {
        return this.value;
    }
}
