package com.ssraman.drugraman.db.repository;


import com.ssraman.drugraman.db.entity.User_info;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.gen.User_infoDao;
import com.ssraman.lib_common.room.EmptyResultSetException;
import com.ssraman.lib_common.room.RxRoom;

import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Completable;
import io.reactivex.Observable;
import io.reactivex.Single;


public class LoginRepository {
    private DaoSession mDaoSession;
    private User_infoDao userDao;

    public LoginRepository(DaoSession daoSession) {
        this.mDaoSession = daoSession;
        userDao = this.mDaoSession.getUser_infoDao();
    }

    public Completable insertUser(User_info userinfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                userDao.insert(userinfo);
                return null;
            }
        });
    }

    public long insertUserByRe(User_info userinfo)
    {
        return userDao.insert(userinfo);
    }

    public Completable deleteUser(User_info userInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                userDao.delete(userInfo);
                return null;
            }
        });
    }

    public Completable updateUser(User_info userInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                userDao.update(userInfo);
                return null;
            }
        });
    }

    public Single<User_info> getUserPassInfo(String userName) {
        return RxRoom.createSingle(new Callable<User_info>() {
            @Override
            public User_info call() throws Exception {
                if(userName==null)
                {
                    throw new EmptyResultSetException("userName is null");
                }
                List<User_info> userInfoList = userDao.queryBuilder().where(User_infoDao.Properties.Login_name.eq(userName.trim())).list();
                if (userInfoList.size() > 0) {
                    return userInfoList.get(0);
                }
                return null;
            }
        });
    }

    public Observable<List<User_info>> getAllUserData() {
        return RxRoom.createObservable(new Callable<List<User_info>>() {
            @Override
            public List<User_info> call() throws Exception {
                List<User_info> userInfoList = userDao.loadAll();
                return userInfoList;
            }
        });
    }

public boolean existsUser(String user_name)
{
    List<User_info> userInfoList = userDao.queryBuilder().where(User_infoDao.Properties.Login_name.eq(user_name.trim())).list();
    if(userInfoList.size()>0)
    {
        return true;
    }
    return false;
}


}
