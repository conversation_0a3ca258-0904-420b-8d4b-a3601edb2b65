package com.ssraman.drugraman.common;


import android.app.Service;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.Nullable;

/**
 * Created by Administrator on 2018/7/24.
 */

public class ioService extends Service {
    ReviceBroadcast reviceBroadcast;
    iotrue iotrue;
    iofalse iofalse;
    ioclose ioclose;
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();

        iotrue=new iotrue();
        iofalse=new iofalse();
        ioclose= new ioclose();

        IntentFilter iotrueIntentFilter=new IntentFilter();
        iotrueIntentFilter.addAction("RET");//自定义广播false
        iotrueIntentFilter.addAction("RCLOSE");//自定义广播超时关闭电源板通知
        iotrueIntentFilter.addAction("RDESTORY");//自定义广播服务被关闭通知，这个广播没有用到

        IntentFilter iofalseIntentFilter=new IntentFilter("REF");//自定义广播true
        IntentFilter iofalseIntentFilter2=new IntentFilter("RCLOSE");//自定义广播true

        //iotrueIntentFilter接收三种反馈，只对"RET"反应
        registerReceiver(iotrue,iotrueIntentFilter);
        //iofalseIntentFilter接收一种反馈，只对"RET"反应
        registerReceiver(iofalse,iofalseIntentFilter);
        //ioclose只对"RCLOSE"处理
        registerReceiver(ioclose,iofalseIntentFilter);
        registerReceiver(ioclose,iofalseIntentFilter2);
      //  Log.d("IOservice", "是否执行了  service-------------");


//        2020duty  合并,马达控制部分的返回接收
        reviceBroadcast=new ReviceBroadcast();
        //创建过滤器
        IntentFilter filter=new IntentFilter("com.raman.sclm.focusrevice");
        //注册
        registerReceiver(reviceBroadcast,filter);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        unregisterReceiver(iotrue);
        unregisterReceiver(iofalse);
        unregisterReceiver(ioclose);
        unregisterReceiver(reviceBroadcast);

     //   Log.d("ioservice", "服务关闭");
    }

    @Override
    public int onStartCommand(Intent intent,  int flags, int startId) {

        return super.onStartCommand(intent, flags, startId);
    }

}
