package com.ssraman.drugraman.rbac.interceptor;

import com.ssraman.drugraman.rbac.annotation.RequirePermission;
import com.ssraman.drugraman.rbac.annotation.RequireRole;
import com.ssraman.drugraman.rbac.security.SecurityContext;
import com.ssraman.drugraman.rbac.security.PermissionChecker;
import com.ssraman.drugraman.rbac.exception.PermissionDeniedException;
import com.ssraman.drugraman.rbac.exception.AuthenticationRequiredException;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 权限拦截器
 * 基于注解的权限验证拦截器
 */
public class PermissionInterceptor {
    
    private final PermissionChecker permissionChecker;
    
    public PermissionInterceptor(PermissionChecker permissionChecker) {
        this.permissionChecker = permissionChecker;
    }
    
    /**
     * 拦截方法调用，进行权限验证
     * @param method 被调用的方法
     * @param args 方法参数
     * @return 是否允许访问
     * @throws PermissionDeniedException 权限不足异常
     * @throws AuthenticationRequiredException 未认证异常
     */
    public boolean intercept(Method method, Object[] args) throws PermissionDeniedException, AuthenticationRequiredException {
        
        // 检查是否已认证
        if (!SecurityContext.isAuthenticated()) {
            throw new AuthenticationRequiredException("用户未登录，请先登录");
        }
        
        Long currentUserId = SecurityContext.getCurrentUserId();
        if (currentUserId == null) {
            throw new AuthenticationRequiredException("无法获取当前用户信息");
        }
        
        // 检查方法级别的权限注解
        RequirePermission methodPermissionAnnotation = method.getAnnotation(RequirePermission.class);
        RequireRole methodRoleAnnotation = method.getAnnotation(RequireRole.class);
        
        // 检查类级别的权限注解
        Class<?> declaringClass = method.getDeclaringClass();
        RequirePermission classPermissionAnnotation = declaringClass.getAnnotation(RequirePermission.class);
        RequireRole classRoleAnnotation = declaringClass.getAnnotation(RequireRole.class);
        
        // 验证权限注解
        if (methodPermissionAnnotation != null) {
            validatePermissionAnnotation(currentUserId, methodPermissionAnnotation);
        } else if (classPermissionAnnotation != null) {
            validatePermissionAnnotation(currentUserId, classPermissionAnnotation);
        }
        
        // 验证角色注解
        if (methodRoleAnnotation != null) {
            validateRoleAnnotation(currentUserId, methodRoleAnnotation);
        } else if (classRoleAnnotation != null) {
            validateRoleAnnotation(currentUserId, classRoleAnnotation);
        }
        
        return true;
    }
    
    /**
     * 验证权限注解
     * @param userId 用户ID
     * @param annotation 权限注解
     * @throws PermissionDeniedException 权限不足异常
     */
    private void validatePermissionAnnotation(Long userId, RequirePermission annotation) throws PermissionDeniedException {
        
        // 获取权限代码列表
        String[] permissionCodes = getPermissionCodes(annotation);
        
        if (permissionCodes.length == 0) {
            return; // 没有指定权限要求
        }
        
        boolean hasPermission;
        
        if (annotation.logical() == RequirePermission.LogicalOperator.AND) {
            // AND逻辑：需要所有权限
            hasPermission = permissionChecker.hasAllPermissions(userId, permissionCodes);
        } else {
            // OR逻辑：需要任一权限
            hasPermission = permissionChecker.hasAnyPermission(userId, permissionCodes);
        }
        
        if (!hasPermission) {
            String message = annotation.message();
            if (message.isEmpty()) {
                message = "权限不足，需要权限: " + Arrays.toString(permissionCodes);
            }
            throw new PermissionDeniedException(message);
        }
    }
    
    /**
     * 验证角色注解
     * @param userId 用户ID
     * @param annotation 角色注解
     * @throws PermissionDeniedException 权限不足异常
     */
    private void validateRoleAnnotation(Long userId, RequireRole annotation) throws PermissionDeniedException {
        
        // 检查最低角色级别
        if (annotation.minLevel() > 0) {
            if (!permissionChecker.hasRoleLevel(userId, annotation.minLevel())) {
                String message = annotation.message();
                if (message.isEmpty()) {
                    message = "角色级别不足，需要最低级别: " + annotation.minLevel();
                }
                throw new PermissionDeniedException(message);
            }
        }
        
        // 获取角色代码列表
        String[] roleCodes = getRoleCodes(annotation);
        
        if (roleCodes.length == 0) {
            return; // 没有指定角色要求
        }
        
        boolean hasRole = false;
        
        if (annotation.logical() == RequireRole.LogicalOperator.AND) {
            // AND逻辑：需要所有角色
            hasRole = true;
            for (String roleCode : roleCodes) {
                if (!SecurityContext.hasRole(roleCode)) {
                    hasRole = false;
                    break;
                }
            }
        } else {
            // OR逻辑：需要任一角色
            for (String roleCode : roleCodes) {
                if (SecurityContext.hasRole(roleCode)) {
                    hasRole = true;
                    break;
                }
            }
        }
        
        if (!hasRole) {
            String message = annotation.message();
            if (message.isEmpty()) {
                message = "角色权限不足，需要角色: " + Arrays.toString(roleCodes);
            }
            throw new PermissionDeniedException(message);
        }
    }
    
    /**
     * 获取权限代码列表
     * @param annotation 权限注解
     * @return 权限代码数组
     */
    private String[] getPermissionCodes(RequirePermission annotation) {
        // 优先使用value属性
        if (annotation.value().length > 0) {
            return annotation.value();
        }
        
        // 其次使用permissions属性
        if (annotation.permissions().length > 0) {
            return annotation.permissions();
        }
        
        // 最后使用resource和action组合
        if (!annotation.resource().isEmpty() && !annotation.action().isEmpty()) {
            String permissionCode = annotation.resource().toUpperCase() + "_" + annotation.action().toUpperCase();
            return new String[]{permissionCode};
        }
        
        return new String[0];
    }
    
    /**
     * 获取角色代码列表
     * @param annotation 角色注解
     * @return 角色代码数组
     */
    private String[] getRoleCodes(RequireRole annotation) {
        // 优先使用value属性
        if (annotation.value().length > 0) {
            return annotation.value();
        }
        
        // 其次使用roles属性
        if (annotation.roles().length > 0) {
            return annotation.roles();
        }
        
        return new String[0];
    }
}
