package com.ssraman.drugraman.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.ssraman.drugraman.db.StringDateConverter;
import java.util.Date;

import com.ssraman.drugraman.db.entity.CalibrationFtInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "CalibrationFt".
*/
public class CalibrationFtInfoDao extends AbstractDao<CalibrationFtInfo, Long> {

    public static final String TABLENAME = "CalibrationFt";

    /**
     * Properties of entity CalibrationFtInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "Id", true, "Id");
        public final static Property ObWave = new Property(1, byte[].class, "ObWave", false, "Wave");
        public final static Property ObIntensity = new Property(2, byte[].class, "ObIntensity", false, "Intensity");
        public final static Property SendWave = new Property(3, Double.class, "SendWave", false, "SendWave");
        public final static Property SampleName = new Property(4, String.class, "SampleName", false, "SampleName");
        public final static Property Ratio = new Property(5, String.class, "Ratio", false, "Ratio");
        public final static Property Remark = new Property(6, String.class, "Remark", false, "Remark");
        public final static Property Inspector = new Property(7, String.class, "Inspector", false, "Publisher");
        public final static Property InspectorTime = new Property(8, String.class, "InspectorTime", false, "PublisherTime");
        public final static Property InspectorPass = new Property(9, Integer.class, "InspectorPass", false, "PublisherPass");
        public final static Property PublisherMacAddress = new Property(10, String.class, "PublisherMacAddress", false, "PublisherMacAddress");
        public final static Property Reviewer = new Property(11, String.class, "Reviewer", false, "Reviewer");
        public final static Property ReviewerTime = new Property(12, String.class, "ReviewerTime", false, "ReviewerTime");
        public final static Property ReviewerPass = new Property(13, Integer.class, "ReviewerPass", false, "ReviewerPass");
        public final static Property ReviewerMacAddress = new Property(14, String.class, "ReviewerMacAddress", false, "ReviewerMacAddress");
    }

    private final StringDateConverter InspectorTimeConverter = new StringDateConverter();
    private final StringDateConverter ReviewerTimeConverter = new StringDateConverter();

    public CalibrationFtInfoDao(DaoConfig config) {
        super(config);
    }
    
    public CalibrationFtInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, CalibrationFtInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        byte[] ObWave = entity.getObWave();
        if (ObWave != null) {
            stmt.bindBlob(2, ObWave);
        }
 
        byte[] ObIntensity = entity.getObIntensity();
        if (ObIntensity != null) {
            stmt.bindBlob(3, ObIntensity);
        }
 
        Double SendWave = entity.getSendWave();
        if (SendWave != null) {
            stmt.bindDouble(4, SendWave);
        }
 
        String SampleName = entity.getSampleName();
        if (SampleName != null) {
            stmt.bindString(5, SampleName);
        }
 
        String Ratio = entity.getRatio();
        if (Ratio != null) {
            stmt.bindString(6, Ratio);
        }
 
        String Remark = entity.getRemark();
        if (Remark != null) {
            stmt.bindString(7, Remark);
        }
 
        String Inspector = entity.getInspector();
        if (Inspector != null) {
            stmt.bindString(8, Inspector);
        }
 
        Date InspectorTime = entity.getInspectorTime();
        if (InspectorTime != null) {
            stmt.bindString(9, InspectorTimeConverter.convertToDatabaseValue(InspectorTime));
        }
 
        Integer InspectorPass = entity.getInspectorPass();
        if (InspectorPass != null) {
            stmt.bindLong(10, InspectorPass);
        }
 
        String PublisherMacAddress = entity.getPublisherMacAddress();
        if (PublisherMacAddress != null) {
            stmt.bindString(11, PublisherMacAddress);
        }
 
        String Reviewer = entity.getReviewer();
        if (Reviewer != null) {
            stmt.bindString(12, Reviewer);
        }
 
        Date ReviewerTime = entity.getReviewerTime();
        if (ReviewerTime != null) {
            stmt.bindString(13, ReviewerTimeConverter.convertToDatabaseValue(ReviewerTime));
        }
 
        Integer ReviewerPass = entity.getReviewerPass();
        if (ReviewerPass != null) {
            stmt.bindLong(14, ReviewerPass);
        }
 
        String ReviewerMacAddress = entity.getReviewerMacAddress();
        if (ReviewerMacAddress != null) {
            stmt.bindString(15, ReviewerMacAddress);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, CalibrationFtInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        byte[] ObWave = entity.getObWave();
        if (ObWave != null) {
            stmt.bindBlob(2, ObWave);
        }
 
        byte[] ObIntensity = entity.getObIntensity();
        if (ObIntensity != null) {
            stmt.bindBlob(3, ObIntensity);
        }
 
        Double SendWave = entity.getSendWave();
        if (SendWave != null) {
            stmt.bindDouble(4, SendWave);
        }
 
        String SampleName = entity.getSampleName();
        if (SampleName != null) {
            stmt.bindString(5, SampleName);
        }
 
        String Ratio = entity.getRatio();
        if (Ratio != null) {
            stmt.bindString(6, Ratio);
        }
 
        String Remark = entity.getRemark();
        if (Remark != null) {
            stmt.bindString(7, Remark);
        }
 
        String Inspector = entity.getInspector();
        if (Inspector != null) {
            stmt.bindString(8, Inspector);
        }
 
        Date InspectorTime = entity.getInspectorTime();
        if (InspectorTime != null) {
            stmt.bindString(9, InspectorTimeConverter.convertToDatabaseValue(InspectorTime));
        }
 
        Integer InspectorPass = entity.getInspectorPass();
        if (InspectorPass != null) {
            stmt.bindLong(10, InspectorPass);
        }
 
        String PublisherMacAddress = entity.getPublisherMacAddress();
        if (PublisherMacAddress != null) {
            stmt.bindString(11, PublisherMacAddress);
        }
 
        String Reviewer = entity.getReviewer();
        if (Reviewer != null) {
            stmt.bindString(12, Reviewer);
        }
 
        Date ReviewerTime = entity.getReviewerTime();
        if (ReviewerTime != null) {
            stmt.bindString(13, ReviewerTimeConverter.convertToDatabaseValue(ReviewerTime));
        }
 
        Integer ReviewerPass = entity.getReviewerPass();
        if (ReviewerPass != null) {
            stmt.bindLong(14, ReviewerPass);
        }
 
        String ReviewerMacAddress = entity.getReviewerMacAddress();
        if (ReviewerMacAddress != null) {
            stmt.bindString(15, ReviewerMacAddress);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public CalibrationFtInfo readEntity(Cursor cursor, int offset) {
        CalibrationFtInfo entity = new CalibrationFtInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // Id
            cursor.isNull(offset + 1) ? null : cursor.getBlob(offset + 1), // ObWave
            cursor.isNull(offset + 2) ? null : cursor.getBlob(offset + 2), // ObIntensity
            cursor.isNull(offset + 3) ? null : cursor.getDouble(offset + 3), // SendWave
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // SampleName
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // Ratio
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // Remark
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // Inspector
            cursor.isNull(offset + 8) ? null : InspectorTimeConverter.convertToEntityProperty(cursor.getString(offset + 8)), // InspectorTime
            cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9), // InspectorPass
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // PublisherMacAddress
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // Reviewer
            cursor.isNull(offset + 12) ? null : ReviewerTimeConverter.convertToEntityProperty(cursor.getString(offset + 12)), // ReviewerTime
            cursor.isNull(offset + 13) ? null : cursor.getInt(offset + 13), // ReviewerPass
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14) // ReviewerMacAddress
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, CalibrationFtInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setObWave(cursor.isNull(offset + 1) ? null : cursor.getBlob(offset + 1));
        entity.setObIntensity(cursor.isNull(offset + 2) ? null : cursor.getBlob(offset + 2));
        entity.setSendWave(cursor.isNull(offset + 3) ? null : cursor.getDouble(offset + 3));
        entity.setSampleName(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setRatio(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setRemark(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setInspector(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setInspectorTime(cursor.isNull(offset + 8) ? null : InspectorTimeConverter.convertToEntityProperty(cursor.getString(offset + 8)));
        entity.setInspectorPass(cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9));
        entity.setPublisherMacAddress(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setReviewer(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setReviewerTime(cursor.isNull(offset + 12) ? null : ReviewerTimeConverter.convertToEntityProperty(cursor.getString(offset + 12)));
        entity.setReviewerPass(cursor.isNull(offset + 13) ? null : cursor.getInt(offset + 13));
        entity.setReviewerMacAddress(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(CalibrationFtInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(CalibrationFtInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(CalibrationFtInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
