package com.ssraman.drugraman.rbac;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.security.PermissionChecker;
import com.ssraman.drugraman.rbac.security.PermissionCache;
import com.ssraman.drugraman.rbac.service.IRbacPermissionService;
import com.ssraman.drugraman.rbac.service.IRbacRoleService;
import com.ssraman.drugraman.db.entity.RbacPermission;
import com.ssraman.drugraman.db.entity.RbacRole;

import java.util.Arrays;
import java.util.List;

/**
 * RBAC权限系统单元测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class RbacPermissionTest {
    
    @Mock
    private IRbacPermissionService mockPermissionService;
    
    @Mock
    private IRbacRoleService mockRoleService;
    
    @Mock
    private PermissionCache mockPermissionCache;
    
    private PermissionChecker permissionChecker;
    
    private static final Long TEST_USER_ID = 1L;
    private static final Long ADMIN_USER_ID = 2L;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        permissionChecker = new PermissionChecker(mockPermissionService, mockRoleService, mockPermissionCache);
        
        // 设置测试数据
        setupTestData();
    }
    
    private void setupTestData() {
        // 模拟操作员角色
        RbacRole operatorRole = new RbacRole();
        operatorRole.setId(1L);
        operatorRole.setRoleCode(RoleType.OPERATOR.getCode());
        operatorRole.setRoleName(RoleType.OPERATOR.getName());
        operatorRole.setLevel(RoleType.OPERATOR.getLevel());
        operatorRole.setEnabled(true);
        
        // 模拟系统管理员角色
        RbacRole adminRole = new RbacRole();
        adminRole.setId(2L);
        adminRole.setRoleCode(RoleType.SYSTEM_ADMIN.getCode());
        adminRole.setRoleName(RoleType.SYSTEM_ADMIN.getName());
        adminRole.setLevel(RoleType.SYSTEM_ADMIN.getLevel());
        adminRole.setEnabled(true);
        
        // 模拟权限
        RbacPermission detectPermission = new RbacPermission();
        detectPermission.setId(1L);
        detectPermission.setPermissionCode(PermissionType.DETECTION_EXECUTE.getCode());
        detectPermission.setPermissionName(PermissionType.DETECTION_EXECUTE.getName());
        detectPermission.setEnabled(true);
        
        RbacPermission userCreatePermission = new RbacPermission();
        userCreatePermission.setId(2L);
        userCreatePermission.setPermissionCode(PermissionType.USER_CREATE.getCode());
        userCreatePermission.setPermissionName(PermissionType.USER_CREATE.getName());
        userCreatePermission.setEnabled(true);
        
        // 设置mock行为
        when(mockRoleService.getUserRoles(TEST_USER_ID))
                .thenReturn(Arrays.asList(operatorRole));
        
        when(mockRoleService.getUserRoles(ADMIN_USER_ID))
                .thenReturn(Arrays.asList(adminRole));
        
        when(mockPermissionService.getUserPermissions(TEST_USER_ID))
                .thenReturn(Arrays.asList(detectPermission));
        
        when(mockPermissionService.getUserPermissions(ADMIN_USER_ID))
                .thenReturn(Arrays.asList(detectPermission, userCreatePermission));
        
        when(mockPermissionCache.getUserPermissions(any())).thenReturn(null);
    }
    
    @Test
    public void testOperatorHasDetectionPermission() {
        // 测试操作员是否具有检测权限
        boolean hasPermission = permissionChecker.hasPermission(TEST_USER_ID, PermissionType.DETECTION_EXECUTE.getCode());
        assertTrue("操作员应该具有检测权限", hasPermission);
    }
    
    @Test
    public void testOperatorDoesNotHaveUserCreatePermission() {
        // 测试操作员是否没有用户创建权限
        boolean hasPermission = permissionChecker.hasPermission(TEST_USER_ID, PermissionType.USER_CREATE.getCode());
        assertFalse("操作员不应该具有用户创建权限", hasPermission);
    }
    
    @Test
    public void testAdminHasAllPermissions() {
        // 测试系统管理员是否具有所有权限
        boolean hasDetectionPermission = permissionChecker.hasPermission(ADMIN_USER_ID, PermissionType.DETECTION_EXECUTE.getCode());
        boolean hasUserCreatePermission = permissionChecker.hasPermission(ADMIN_USER_ID, PermissionType.USER_CREATE.getCode());
        
        assertTrue("系统管理员应该具有检测权限", hasDetectionPermission);
        assertTrue("系统管理员应该具有用户创建权限", hasUserCreatePermission);
    }
    
    @Test
    public void testRoleLevelCheck() {
        // 测试角色级别检查
        boolean operatorLevel = permissionChecker.hasRoleLevel(TEST_USER_ID, RoleType.OPERATOR.getLevel());
        boolean reviewerLevel = permissionChecker.hasRoleLevel(TEST_USER_ID, RoleType.REVIEWER.getLevel());
        boolean adminLevel = permissionChecker.hasRoleLevel(ADMIN_USER_ID, RoleType.SYSTEM_ADMIN.getLevel());
        
        assertTrue("操作员应该满足操作员级别要求", operatorLevel);
        assertFalse("操作员不应该满足审核员级别要求", reviewerLevel);
        assertTrue("系统管理员应该满足系统管理员级别要求", adminLevel);
    }
    
    @Test
    public void testHasRole() {
        // 测试角色检查
        boolean operatorHasOperatorRole = permissionChecker.hasRole(TEST_USER_ID, RoleType.OPERATOR);
        boolean operatorHasAdminRole = permissionChecker.hasRole(TEST_USER_ID, RoleType.SYSTEM_ADMIN);
        boolean adminHasAdminRole = permissionChecker.hasRole(ADMIN_USER_ID, RoleType.SYSTEM_ADMIN);
        
        assertTrue("操作员应该具有操作员角色", operatorHasOperatorRole);
        assertFalse("操作员不应该具有系统管理员角色", operatorHasAdminRole);
        assertTrue("系统管理员应该具有系统管理员角色", adminHasAdminRole);
    }
    
    @Test
    public void testHasAnyPermission() {
        // 测试是否具有任一权限
        boolean hasAnyPermission = permissionChecker.hasAnyPermission(TEST_USER_ID, 
                PermissionType.DETECTION_EXECUTE.getCode(), 
                PermissionType.USER_CREATE.getCode());
        
        assertTrue("操作员应该具有检测权限或用户创建权限中的至少一个", hasAnyPermission);
    }
    
    @Test
    public void testHasAllPermissions() {
        // 测试是否具有所有权限
        boolean hasAllPermissions = permissionChecker.hasAllPermissions(TEST_USER_ID, 
                PermissionType.DETECTION_EXECUTE.getCode(), 
                PermissionType.USER_CREATE.getCode());
        
        assertFalse("操作员不应该同时具有检测权限和用户创建权限", hasAllPermissions);
        
        boolean adminHasAllPermissions = permissionChecker.hasAllPermissions(ADMIN_USER_ID, 
                PermissionType.DETECTION_EXECUTE.getCode(), 
                PermissionType.USER_CREATE.getCode());
        
        assertTrue("系统管理员应该同时具有检测权限和用户创建权限", adminHasAllPermissions);
    }
    
    @Test
    public void testNullUserIdHandling() {
        // 测试空用户ID的处理
        boolean hasPermission = permissionChecker.hasPermission(null, PermissionType.DETECTION_EXECUTE.getCode());
        assertFalse("空用户ID应该返回false", hasPermission);
    }
    
    @Test
    public void testNullPermissionCodeHandling() {
        // 测试空权限代码的处理
        boolean hasPermission = permissionChecker.hasPermission(TEST_USER_ID, null);
        assertFalse("空权限代码应该返回false", hasPermission);
    }
    
    @Test
    public void testGetUserMaxRoleLevel() {
        // 测试获取用户最高角色级别
        int operatorMaxLevel = permissionChecker.getUserMaxRoleLevel(TEST_USER_ID);
        int adminMaxLevel = permissionChecker.getUserMaxRoleLevel(ADMIN_USER_ID);
        
        assertEquals("操作员的最高角色级别应该是1", RoleType.OPERATOR.getLevel(), operatorMaxLevel);
        assertEquals("系统管理员的最高角色级别应该是5", RoleType.SYSTEM_ADMIN.getLevel(), adminMaxLevel);
    }
    
    @Test
    public void testPermissionCacheInteraction() {
        // 测试权限缓存交互
        permissionChecker.hasPermission(TEST_USER_ID, PermissionType.DETECTION_EXECUTE.getCode());
        
        // 验证是否调用了缓存
        verify(mockPermissionCache, times(1)).getUserPermissions(TEST_USER_ID);
        verify(mockPermissionCache, times(1)).setUserPermissions(eq(TEST_USER_ID), any());
    }
    
    @Test
    public void testClearUserPermissionCache() {
        // 测试清除用户权限缓存
        permissionChecker.clearUserPermissionCache(TEST_USER_ID);
        
        verify(mockPermissionCache, times(1)).removeUserPermissions(TEST_USER_ID);
    }
    
    @Test
    public void testClearAllPermissionCache() {
        // 测试清除所有权限缓存
        permissionChecker.clearAllPermissionCache();
        
        verify(mockPermissionCache, times(1)).clear();
    }
}
