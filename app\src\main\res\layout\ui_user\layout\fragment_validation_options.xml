<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:tools="http://schemas.android.com/tools">

<data>

    <variable
        name="mpresenter"
        type="com.ssraman.drugraman.ui.other.ValidationOptionsFragment.MPresenter" />

    <variable
        name="validationOptionsViewModel"
        type="com.ssraman.drugraman.ui.vm.ValidationOptionsViewModel" />
</data>

<androidx.core.widget.NestedScrollView
    android:id="@+id/nestedScrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginBottom="5dp"
    android:background="@color/nc_main"
    tools:context=".ui.other.ValidationOptionsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.other.ValidationOptionsFragment">

        <View style="@style/LinearSplitStyle" />

        <LinearLayout
            android:id="@+id/lin_continutation_test"
            style="@style/LinearItemStyle"

            android:background="@color/nc_main"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/title_work_in_validation"
                android:layout_weight="1"
                android:textColor="@color/black" />


            <CheckBox
                android:id="@+id/chk_validation"
                style="@style/CustomCheckboxSwitch2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <View style="@style/LinearSplitStyle"
            android:background="@color/nc_sub"/>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>

</layout>