package com.ssraman.drugraman.sortableview;

import android.content.Context;
import android.graphics.Color;
import android.text.method.PasswordTransformationMethod;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.db.entity.OperLogInfo;
import com.ssraman.drugraman.typeface.OperationType;
import com.ssraman.lib_common.utils.TimeUtils;

import java.util.List;

import de.codecrafters.tableview.TableView;
import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * @author: Administrator
 * @date: 2021/7/9
 */
public class OperatorRecordsSorTableDataAdapter  extends LongPressAwareTableDataAdapter<OperLogInfo> {
    private static final int TEXT_SIZE = 16;
    private static final int TIME_TEXT_SIZE = 12;

    public OperatorRecordsSorTableDataAdapter(Context context, List<OperLogInfo> data, TableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final OperLogInfo recordInfo = getRowData(rowIndex);
        View renderedView = null;
        switch (columnIndex) {
            case 0:
                renderedView = renderChecked(parentView,recordInfo,rowIndex);
                break;
            case 1:
                String detection_time = TimeUtils.date2String(recordInfo.getOperatorTime());
                renderedView = renderTimeString(detection_time);
                break;
            case 2:
                renderedView = renderString(recordInfo.getOperator());
                break;
            case 3:
                String oper_type = OperationType.valueOf(recordInfo.getOperatorType()).name();
                renderedView = renderString(oper_type);
                break;
        }
        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final OperLogInfo recordInfo = getRowData(rowIndex);
        View renderedView = null;

        renderedView = getDefaultCellView(rowIndex, columnIndex, parentView);

        return renderedView;
    }

    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        if(value==null||value.equals("null"))
        {
            textView.setText("");
        }
        else
        {
            textView.setText(value);
        }
        textView.setPadding(2, 1, 2, 1);
        textView.setTextSize(TEXT_SIZE);
        textView.setTextColor(Color.WHITE);
        textView.setGravity(Gravity.CENTER);
        return textView;
    }

    private View renderTimeString(final String value) {
        final TextView textView = new TextView(getContext());
        if(value==null||value.equals("null"))
        {
            textView.setText("");
        }
        else
        {
            textView.setText(value);
        }
        textView.setPadding(2, 1, 2, 1);
        textView.setTextSize(TIME_TEXT_SIZE);
        textView.setTextColor(Color.WHITE);
        textView.setGravity(Gravity.CENTER);
        return textView;
    }

    private View renderPassString(final String value) {
        final TextView textView = new TextView(getContext());
       // textView.setInputType(InputType.TYPE_CLASS_TEXT|InputType.TYPE_TEXT_VARIATION_PASSWORD);
        textView.setTransformationMethod(PasswordTransformationMethod.getInstance());
        if(value==null||value.equals("null"))
        {
            textView.setText("");
        }
        else
        {
            textView.setText(value);
        }
        textView.setBackground(null);
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        return textView;
    }


    private OnCellItemClickListener mOnCellItemClickListener;

    public void setOnCellItemClickListener(OnCellItemClickListener mOnItemClickListener) {
        this.mOnCellItemClickListener = mOnItemClickListener;
    }

    private View renderChecked(final ViewGroup parentView,final OperLogInfo recordInfo,int position) {
        final CheckBox checkBox =new CheckBox(getContext());
        checkBox.setChecked(recordInfo.isCheck());
        checkBox.setPadding(5, 1, 2, 1);
        checkBox.setGravity(Gravity.CENTER);
        checkBox.setButtonDrawable(R.drawable.my_checkbox_style);
        if (mOnCellItemClickListener != null)
        {
            checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    mOnCellItemClickListener.OnItemCheckedChangeClick(buttonView,recordInfo, position);//需要对其他的数据做清除操作
                    recordInfo.setCheck(!recordInfo.isCheck());
                }
            });
        }
        return checkBox;
    }

    public interface OnCellItemClickListener {

        //单点监听
        void OnItemClick(View view, OperLogInfo item, int position);
        //选择改变监听
        void OnItemCheckedChangeClick(View view,OperLogInfo item, int position);
    }

}
