package com.ssraman.drugraman.ui.adapter;

import android.content.Context;

import com.ssraman.drugraman.databinding.ItemDialogCustomClassifyBinding;
import com.ssraman.drugraman.item.SampleItem;
import com.ssraman.lib_common.adapter.SingleRecyclerAdapter;

/**
 * @author: Administrator
 * @date: 2021/8/9
 */
public class ClassifyIcoAdapter extends SingleRecyclerAdapter<SampleItem, ItemDialogCustomClassifyBinding> {
    private final int layout;

    public ClassifyIcoAdapter(Context mContext, int layout) {
        super(mContext);
        this.layout = layout;
    }

    @Override
    protected int getLayoutResId(int viewType) {
        return this.layout;
    }

    @Override
    protected void onBindItem(ItemDialogCustomClassifyBinding binding, SampleItem item, ViewHolder holder) {
        holder.setIvSelect(binding.ivSelect);
        binding.setSampleItem(item);
        binding.ivSelect.bringToFront();
    }
}