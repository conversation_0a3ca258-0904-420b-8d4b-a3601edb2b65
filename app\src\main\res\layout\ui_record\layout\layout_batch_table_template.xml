<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/activity_table1"
        android:layout_width="340dp"
        android:layout_height="550dp"
        android:background="@color/white"
        android:orientation="horizontal">

        <include
            android:id="@+id/item_t1_name"
            layout="@layout/layout_title_batch_table_template"
            android:layout_width="30dp"
            android:layout_height="550dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="280dp"
            android:layout_height="550dp"
            android:layout_weight="1"
            android:background="@color/white"
            android:paddingLeft="2dp"
            android:paddingTop="8dp"
            android:paddingRight="2dp"
            android:paddingBottom="8dp">

            <com.ssraman.control.excelpanel.ExcelPanel
                android:id="@+id/excel_table"
                android:layout_width="530dp"
                android:layout_height="253dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="@drawable/shape_stroke_gray_99_execl"
                android:rotation="270"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:left_cell_width="@dimen/batch_table_cell_length"
                app:normal_cell_width="@dimen/batch_table_cell_length"
                app:top_cell_height="@dimen/batch_table_cell_length" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="20dp"
            android:layout_height="550dp"
            android:background="@color/white">

            <TextView
                android:id="@+id/title_publisher"
                android:layout_width="128dp"
                android:layout_height="18dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="1dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="150dp"
                android:rotation="270"
                android:text="@string/title_publisher_with_colon"
                android:textColor="@color/report_txt_color"
                android:textSize="8sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/edit_publisher"
                android:layout_width="128dp"
                android:layout_height="18dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="200dp"
                android:rotation="270"
                android:text="@string/title_publisher_name"
                android:textColor="@color/report_txt_color"
                android:textSize="8sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/title_reviewer"
                android:layout_width="128dp"
                android:layout_height="18dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="342dp"
                android:rotation="270"
                android:text="@string/title_reviewer_with_colon"
                android:textColor="@color/report_txt_color"
                android:textSize="8sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/edit_reviewer"
                android:layout_width="128dp"
                android:layout_height="18dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="148dp"
                android:layout_marginEnd="5dp"
                android:rotation="270"
                android:text="@string/title_reviewer_name"
                android:textColor="@color/report_txt_color"
                android:textSize="8sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="20dp"
            android:layout_height="550dp"
            android:background="@color/white">

            <TextView
                android:id="@+id/title_creat_date"
                android:layout_width="128dp"
                android:layout_height="18dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="150dp"
                android:rotation="270"
                android:text="@string/title_create_date_with_colon"
                android:textColor="@color/report_txt_color"
                android:textSize="8sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/edit_creat_date"
                android:layout_width="128dp"
                android:layout_height="18dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="200dp"
                android:rotation="270"
                android:text="@string/title_date_time_format"
                android:textColor="@color/report_txt_color"
                android:textSize="8sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/page_no"
                android:layout_width="128dp"
                android:layout_height="18dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="300dp"
                android:rotation="270"
                android:text="@string/title_page_format"
                android:textColor="@color/report_txt_color"
                android:textSize="8sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/edit_creat_mac"
                android:layout_width="128dp"
                android:layout_height="18dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="190dp"
                android:layout_marginEnd="5dp"
                android:rotation="270"
                android:text="@string/title_mac_address_format"
                android:textColor="@color/report_txt_color"
                android:textSize="8sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</layout>
