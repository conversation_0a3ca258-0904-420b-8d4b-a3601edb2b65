<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="mpresenter"
            type="com.ssraman.drugraman.ui.user.UserManageFragment.MPresenter" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/nc_main"
        tools:context=".ui.user.UserManageFragment">

        <TextView
            android:id="@+id/textView3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="36dp"
            android:layout_marginTop="16dp"
            android:text="@string/title_user_management"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/title_user_management" />

        <View
            android:id="@+id/view6"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="14dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:background="@color/nc_sub"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView3" />

        <LinearLayout
            android:id="@+id/btn_user_add"
            android:layout_width="70dp"
            android:layout_height="100dp"
            android:layout_marginStart="30dp"
            android:layout_marginTop="15dp"
            android:gravity="center"
            android:onClick="@{(view)->mpresenter.UserAddClick(view)}"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view6">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_person_add_32" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/btn_add"
                android:textColor="@color/nc_light"
                android:textSize="24sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_user_delete"
            android:layout_width="70dp"
            android:layout_height="100dp"
            android:layout_marginStart="10dp"
            android:gravity="center"
            android:onClick="@{(view)->mpresenter.UserDeleteClick(view)}"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/btn_user_add"
            app:layout_constraintStart_toEndOf="@+id/btn_user_add"
            app:layout_constraintTop_toTopOf="@+id/btn_user_add">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_person_delete_32" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/btn_delete"
                android:textColor="@color/nc_light"
                android:textSize="24sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_user_modify"
            android:layout_width="70dp"
            android:layout_height="100dp"
            android:layout_marginStart="10dp"
            android:gravity="center"
            android:onClick="@{(view)->mpresenter.UserModifyClick(view)}"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/btn_user_delete"
            app:layout_constraintStart_toEndOf="@+id/btn_user_delete"
            app:layout_constraintTop_toTopOf="@+id/btn_user_delete">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_person_modify_32" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/btn_modify"
                android:textColor="@color/nc_light"
                android:textSize="24sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_user_loadall"
            android:layout_width="70dp"
            android:layout_height="100dp"
            android:layout_marginStart="10dp"
            android:gravity="center"
            android:onClick="@{(view)->mpresenter.UserLoadAllClick(view)}"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/btn_user_modify"
            app:layout_constraintStart_toEndOf="@+id/btn_user_modify"
            app:layout_constraintTop_toTopOf="@+id/btn_user_modify">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_person_load_32" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/btn_all"
                android:textColor="@color/nc_light"
                android:textSize="24sp" />

        </LinearLayout>

        <com.ssraman.drugraman.sortableview.UserManagementSorTableView
            android:id="@+id/user_table"
            android:layout_width="match_parent"
            android:layout_height="350dp"
            android:layout_marginTop="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_user_add"
            app:tableView_columnCount="5"
            app:tableView_headerColor="@color/table_data_cow_header"
            app:tableView_headerElevation="10" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>