package com.ssraman.drugraman.common;

public class CommonParameter {

    public static int constant_interval=5;

    public static final String USBPATH="/storage/usbotg";
    public static final  int NR=20;

    //public static int start_wave=260;
    public static double matchRatio=0.5;
    public static boolean LanguageIsEn=false;

    public static boolean OPENBroadcast=false;
    public static boolean BroadcastStatus=false;
    public static int BroadcastStatus2=1;

    public static String path= "/MailSpec" ;
    public static String pathString="/MailSpec/specSource.txt" ;

    public static final int HighLevelDirectory = 1;
    public static final int LowLevelDirectory = 2;
    public static final int ItemLevelDirectory = 3;

    public static String SUCCESS = "success";
    public static String NO_DATA = "noData";
    public static String ERROR = "error";

}
