<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="aboutViewModel"
            type="com.ssraman.drugraman.ui.vm.AboutViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/nc_main"
        tools:context=".ui.AboutFragment">


        <ImageView
            android:id="@+id/imageView2"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView16"
            tools:srcCompat="@tools:sample/avatars" />

        <TextView
            android:id="@+id/textView16"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="37dp"
            android:text="@string/title_about_device"
            android:textColor="@color/nc_light"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="45dp"
            android:layout_marginTop="170dp"
            android:layout_marginEnd="45dp"
            android:background="#E9E9E9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view2"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="45dp"
            android:layout_marginTop="55dp"
            android:layout_marginEnd="45dp"
            android:background="#E9E9E9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view1" />

        <View
            android:id="@+id/view3"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="45dp"
            android:layout_marginTop="55dp"
            android:layout_marginEnd="45dp"
            android:background="#E9E9E9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view2" />

        <View
            android:id="@+id/view4"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="45dp"
            android:layout_marginTop="55dp"
            android:layout_marginEnd="45dp"
            android:background="#E9E9E9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view3" />

        <View
            android:id="@+id/view5"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="45dp"
            android:layout_marginTop="55dp"
            android:layout_marginEnd="45dp"
            android:background="#E9E9E9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view4" />

        <View
            android:id="@+id/view6"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="45dp"
            android:layout_marginTop="55dp"
            android:layout_marginEnd="45dp"
            android:background="#E9E9E9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view5" />

        <TextView
            android:id="@+id/textView17"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="@string/title_raw_material_detection_software"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toTopOf="@+id/view2"
            app:layout_constraintStart_toStartOf="@+id/view1"
            app:layout_constraintTop_toBottomOf="@+id/view1"
            app:layout_constraintVertical_bias="0.095" />

        <TextView
            android:id="@+id/textView18"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/title_version"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toTopOf="@+id/view3"
            app:layout_constraintStart_toStartOf="@+id/view2"
            app:layout_constraintTop_toBottomOf="@+id/view2" />

        <TextView
            android:id="@+id/textView19"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Copy right 20181.0.1.0"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toTopOf="@+id/view4"
            app:layout_constraintStart_toStartOf="@+id/view3"
            app:layout_constraintTop_toBottomOf="@+id/view3"
            tools:text="Copy right 20241.0.1.0" />

<!--        <TextView-->
<!--            android:id="@+id/txt_company"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="中科云析（南京）科技有限公司"-->
<!--            android:textColor="@color/black"-->
<!--            android:textSize="18sp"-->
<!--            app:layout_constraintBottom_toTopOf="@+id/view5"-->
<!--            app:layout_constraintStart_toStartOf="@+id/view4"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/view4"-->
<!--            tools:text="中科云析（南京）科技有限公司" />-->


        <TextView
            android:id="@+id/textView21"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/title_phone"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toTopOf="@+id/view6"
            app:layout_constraintStart_toStartOf="@+id/view5"
            app:layout_constraintTop_toBottomOf="@+id/view5"
            tools:text="电话" />

        <TextView
            android:id="@+id/textView22"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="@string/title_uid"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/view6"
            app:layout_constraintTop_toBottomOf="@+id/view6"
            app:layout_constraintVertical_bias="0.01999998"
            tools:text="UID" />

        <TextView
            android:id="@+id/txt_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0.00002"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="@+id/textView18"
            app:layout_constraintEnd_toEndOf="@+id/view2"
            tools:text="0.00002" />

        <TextView
            android:id="@+id/txt_telepone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="************"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="@+id/textView21"
            app:layout_constraintEnd_toEndOf="@+id/view5"
            tools:text="************" />


        <TextView
            android:id="@+id/txt_uid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="123456"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="@+id/textView22"
            app:layout_constraintEnd_toEndOf="@+id/view6"
            tools:text="123456" />



    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>