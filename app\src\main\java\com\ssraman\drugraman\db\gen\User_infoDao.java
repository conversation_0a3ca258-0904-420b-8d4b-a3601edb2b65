package com.ssraman.drugraman.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.ssraman.drugraman.db.entity.User_info;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "tb_uesr".
*/
public class User_infoDao extends AbstractDao<User_info, Long> {

    public static final String TABLENAME = "tb_uesr";

    /**
     * Properties of entity User_info.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "id");
        public final static Property Login_name = new Property(1, String.class, "login_name", false, "login_name");
        public final static Property Login_pwd = new Property(2, String.class, "login_pwd", false, "login_pwd");
        public final static Property Priority = new Property(3, Integer.class, "Priority", false, "priority");
        public final static Property Status = new Property(4, String.class, "status", false, "status");
    }


    public User_infoDao(DaoConfig config) {
        super(config);
    }
    
    public User_infoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, User_info entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String login_name = entity.getLogin_name();
        if (login_name != null) {
            stmt.bindString(2, login_name);
        }
 
        String login_pwd = entity.getLogin_pwd();
        if (login_pwd != null) {
            stmt.bindString(3, login_pwd);
        }
 
        Integer Priority = entity.getPriority();
        if (Priority != null) {
            stmt.bindLong(4, Priority);
        }
 
        String status = entity.getStatus();
        if (status != null) {
            stmt.bindString(5, status);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, User_info entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String login_name = entity.getLogin_name();
        if (login_name != null) {
            stmt.bindString(2, login_name);
        }
 
        String login_pwd = entity.getLogin_pwd();
        if (login_pwd != null) {
            stmt.bindString(3, login_pwd);
        }
 
        Integer Priority = entity.getPriority();
        if (Priority != null) {
            stmt.bindLong(4, Priority);
        }
 
        String status = entity.getStatus();
        if (status != null) {
            stmt.bindString(5, status);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public User_info readEntity(Cursor cursor, int offset) {
        User_info entity = new User_info( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // login_name
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // login_pwd
            cursor.isNull(offset + 3) ? null : cursor.getInt(offset + 3), // Priority
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4) // status
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, User_info entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setLogin_name(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setLogin_pwd(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setPriority(cursor.isNull(offset + 3) ? null : cursor.getInt(offset + 3));
        entity.setStatus(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(User_info entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(User_info entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(User_info entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
