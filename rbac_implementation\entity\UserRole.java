package com.ssraman.drugraman.rbac.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.ToOne;
import org.greenrobot.greendao.annotation.Generated;

import java.util.Date;

/**
 * 用户角色关联实体类
 * 管理用户和角色的多对多关系
 */
@Entity(nameInDb = "tb_user_role")
public class UserRole {
    
    @Id(autoincrement = true)
    @Property(nameInDb = "id")
    private Long id;
    
    @Property(nameInDb = "user_id")
    private Long userId;
    
    @Property(nameInDb = "role_id")
    private Long roleId;
    
    @Property(nameInDb = "assigned_at")
    private Date assignedAt;
    
    @Property(nameInDb = "assigned_by")
    private Long assignedBy; // 分配者用户ID
    
    @Property(nameInDb = "expires_at")
    private Date expiresAt; // 过期时间，null表示永不过期
    
    @Property(nameInDb = "status")
    private Integer status; // 0:禁用, 1:启用
    
    @ToOne(joinProperty = "userId")
    private User user;
    
    @ToOne(joinProperty = "roleId")
    private Role role;

    @Generated(hash = 1186799675)
    public UserRole(Long id, Long userId, Long roleId, Date assignedAt,
            Long assignedBy, Date expiresAt, Integer status) {
        this.id = id;
        this.userId = userId;
        this.roleId = roleId;
        this.assignedAt = assignedAt;
        this.assignedBy = assignedBy;
        this.expiresAt = expiresAt;
        this.status = status;
    }

    @Generated(hash = 1906849798)
    public UserRole() {
    }
    
    // 构造函数
    public UserRole(Long userId, Long roleId, Long assignedBy) {
        this.userId = userId;
        this.roleId = roleId;
        this.assignedBy = assignedBy;
        this.assignedAt = new Date();
        this.status = 1; // 默认启用
    }
    
    public UserRole(Long userId, Long roleId, Long assignedBy, Date expiresAt) {
        this.userId = userId;
        this.roleId = roleId;
        this.assignedBy = assignedBy;
        this.assignedAt = new Date();
        this.expiresAt = expiresAt;
        this.status = 1; // 默认启用
    }
    
    // 业务方法
    public boolean isActive() {
        return status != null && status == 1;
    }
    
    public boolean isExpired() {
        return expiresAt != null && expiresAt.before(new Date());
    }
    
    public boolean isValid() {
        return isActive() && !isExpired();
    }
    
    public void activate() {
        this.status = 1;
    }
    
    public void deactivate() {
        this.status = 0;
    }
    
    public void setExpiration(Date expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public void removeExpiration() {
        this.expiresAt = null;
    }
    
    public long getDaysUntilExpiration() {
        if (expiresAt == null) {
            return Long.MAX_VALUE; // 永不过期
        }
        long diffInMillies = expiresAt.getTime() - new Date().getTime();
        return diffInMillies / (24 * 60 * 60 * 1000);
    }

    // Getters and Setters
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getRoleId() {
        return this.roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Date getAssignedAt() {
        return this.assignedAt;
    }

    public void setAssignedAt(Date assignedAt) {
        this.assignedAt = assignedAt;
    }

    public Long getAssignedBy() {
        return this.assignedBy;
    }

    public void setAssignedBy(Long assignedBy) {
        this.assignedBy = assignedBy;
    }

    public Date getExpiresAt() {
        return this.expiresAt;
    }

    public void setExpiresAt(Date expiresAt) {
        this.expiresAt = expiresAt;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Role getRole() {
        return this.role;
    }

    public void setRole(Role role) {
        this.role = role;
    }
}
