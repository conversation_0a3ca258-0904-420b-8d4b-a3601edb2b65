package com.ssraman.drugraman.searchview;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.ssraman.control.spinner.MaterialSpinnerBaseAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2022/7/21
 */
public class SearchViewAdapter <T> extends BaseAdapter {
    private final Context context;
    private List<T> items;

    public SearchViewAdapter(Context context) {
        this.context = context;
    }

    public String getItemText(int position) {
        return getItem(position).toString();
    }

    public void setItems(List<T> _items )
    {
        this.items.clear();
        this.items.addAll(_items);
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return items != null ? items.size() : 0;
    }

    @Override
    public T getItem(int position) {
        return items.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        final TextView textView;
        if (convertView == null) {
            LayoutInflater inflater = LayoutInflater.from(context);
            convertView = inflater.inflate(android.R.layout.simple_list_item_1, parent, false);
            textView = (TextView) convertView.findViewById(android.R.id.text1);

            convertView.setTag(new ViewHolder(textView));
        } else {
            textView = ((ViewHolder) convertView.getTag()).textView;
        }
        textView.setText(getItemText(position));
        return convertView;
    }

    private static class ViewHolder {

        private TextView textView;

        private ViewHolder(TextView textView) {
            this.textView = textView;
        }
    }

}
