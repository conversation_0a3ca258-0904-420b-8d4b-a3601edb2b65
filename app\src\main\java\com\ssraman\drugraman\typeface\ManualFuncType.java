package com.ssraman.drugraman.typeface;

/**
 * @author: Administrator
 * @date: 2021/7/20
 */
public enum ManualFuncType {
    DocManual(0),
    VideoManual(1);

    private int value = 0;

    private ManualFuncType(int value) {    //    必须是private的，否则编译错误
        this.value = value;
    }

    public static ManualFuncType valueOf(int value) {    //    手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return DocManual;
            case 1:
                return VideoManual;
            default:
                return DocManual;
        }
    }

    public int value() {
        return this.value;
    }
}
