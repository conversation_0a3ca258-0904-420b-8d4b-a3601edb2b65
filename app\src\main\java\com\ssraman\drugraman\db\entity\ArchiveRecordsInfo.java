package com.ssraman.drugraman.db.entity;

import com.ssraman.drugraman.db.StringDateConverter;

import org.greenrobot.greendao.annotation.Convert;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Transient;

import java.util.Date;
import org.greenrobot.greendao.annotation.Generated;

/**
 * @author: Administrator
 * @date: 2021/10/13
 */
@Entity(nameInDb = "ArchiveRecordsTable", createInDb = false)
public class ArchiveRecordsInfo {
    @Id(autoincrement = true)
    @Property(nameInDb = "Id")
    private Long Id;

    //物质类别：识别（样品名） 辨识（样品类别）
    @Property(nameInDb = "Material")
    private String Material;
    //样品名称
    @Property(nameInDb = "SampleName")
    private String SampleName;
    //样品形态
    @Property(nameInDb = "SampleMorphology")
    private String SampleMorphology;
    //序列号
    @Property(nameInDb = "SeriesNumber")
    private String SeriesNumber;
    //批次数量
    @Property(nameInDb = "Batch")
    private Integer Batch;
    //货柜、位置
    @Property(nameInDb = "Container")
    private String Container;
    //仪器型号
    @Property(nameInDb = "InstrumentModel")
    private String InstrumentModel;
    //软件版本
    @Property(nameInDb = "SoftwareVersion")
    private String SoftwareVersion;
    //评估方法：辨识 1、验证 0
    @Property(nameInDb = "EvaluationMode")
    private Integer EvaluationMode;
    //样品描述
    @Property(nameInDb = "Description")
    private String Description;
    //检测结果
    @Property(nameInDb = "DetectionResult")
    private Integer DetectionResult;
    //检测人员
    @Property(nameInDb = "DetectionMan")
    private String DetectionMan;
    //使用核心方法
    @Property(nameInDb = "CoreMethod")
    private Integer CoreMethod;
    // 标准置信度(通过后参照样本的置信度)
    @Property(nameInDb = "Confidence")
    private double Confidence;
    //谱图ID
    @Property(nameInDb = "SpecId")
    private Long SpecId;
    //测试时间
    @Property(nameInDb = "DetectionTime")
    @Convert(converter = StringDateConverter.class, columnType = String.class)
    private Date DetectionTime;
    //匹配结果
    @Property(nameInDb = "MatchReport")
    private byte[] MatchReport;
    //匹配样本集名称
    @Property(nameInDb = "CompoundName")
    private String CompoundName;
    //匹配样本相似度
    @Property(nameInDb = "Correlation")
    private String Correlation;
    //是否上传
    @Property(nameInDb = "IsPost")
    private Integer IsPost;
    //MQTT是否上传 0没有上传操作 1已上传 2上传未成功
    @Property(nameInDb = "IsMQTT")
    private Integer IsMQTT;

    //批次扫描位次
    @Property(nameInDb = "IsBatch")
    private Integer IsBatch;

    //批检测名称
    @Property(nameInDb = "BatchName")
    private String BatchName;

    //样本容器
    @Property(nameInDb = "SampleContainer")
    private String SampleContainer;

    //批检测样品数量
    @Property(nameInDb = "SampleNumber")
    private Integer SampleNumber;

    //经度
    @Property(nameInDb = "Longitude")
    private Double longitude;

    //纬度
    @Property(nameInDb = "Latitude")
    private Double latitude;

    //检验确认人员（发布人）
    @Property(nameInDb = "Publisher")
    private String Inspector;

    //检验确认时间
    @Property(nameInDb = "PublisherTime")
    @Convert(converter = StringDateConverter.class, columnType = String.class)
    private Date InspectorTime;

    //是否检验确认通过
    @Property(nameInDb = "PublisherPass")
    private Integer InspectorPass;

    //检验MAC地址（发布人）
    @Property(nameInDb = "PublisherMacAddress")
    private String PublisherMacAddress;

    //审核确认人员 （复核）
    @Property(nameInDb = "Reviewer")
    private String Reviewer;

    //审核确认时间
    @Property(nameInDb = "ReviewerTime")
    @Convert(converter = StringDateConverter.class, columnType = String.class)
    private Date ReviewerTime;

    //是否审核确认通过
    @Property(nameInDb = "ReviewerPass")
    private Integer ReviewerPass;

    //审核MAC地址（复核）
    @Property(nameInDb = "ReviewerMacAddress")
    private String ReviewerMacAddress;

    //检测认证结果  人工确认后的结果
    @Property(nameInDb = "CertificationResult")
    private Integer CertificationResult;

    //记录来源
    @Property(nameInDb = "RecordSource")
    private String RecordSource;

    //预留
    @Property(nameInDb = "Reserve")
    private byte[] Reserve1;

    /**
     * 是否选中 购删除使用
     */
    @Transient
    private boolean isCheck;
    @Transient
    private boolean isEdit;

    
    @Generated(hash = 1274817459)
    public ArchiveRecordsInfo() {
    }

    @Generated(hash = 1863513466)
    public ArchiveRecordsInfo(Long Id, String Material, String SampleName,
            String SampleMorphology, String SeriesNumber, Integer Batch, String Container,
            String InstrumentModel, String SoftwareVersion, Integer EvaluationMode,
            String Description, Integer DetectionResult, String DetectionMan,
            Integer CoreMethod, double Confidence, Long SpecId, Date DetectionTime,
            byte[] MatchReport, String CompoundName, String Correlation, Integer IsPost,
            Integer IsMQTT, Integer IsBatch, String BatchName, String SampleContainer,
            Integer SampleNumber, Double longitude, Double latitude, String Inspector,
            Date InspectorTime, Integer InspectorPass, String PublisherMacAddress,
            String Reviewer, Date ReviewerTime, Integer ReviewerPass,
            String ReviewerMacAddress, Integer CertificationResult, String RecordSource,
            byte[] Reserve1) {
        this.Id = Id;
        this.Material = Material;
        this.SampleName = SampleName;
        this.SampleMorphology = SampleMorphology;
        this.SeriesNumber = SeriesNumber;
        this.Batch = Batch;
        this.Container = Container;
        this.InstrumentModel = InstrumentModel;
        this.SoftwareVersion = SoftwareVersion;
        this.EvaluationMode = EvaluationMode;
        this.Description = Description;
        this.DetectionResult = DetectionResult;
        this.DetectionMan = DetectionMan;
        this.CoreMethod = CoreMethod;
        this.Confidence = Confidence;
        this.SpecId = SpecId;
        this.DetectionTime = DetectionTime;
        this.MatchReport = MatchReport;
        this.CompoundName = CompoundName;
        this.Correlation = Correlation;
        this.IsPost = IsPost;
        this.IsMQTT = IsMQTT;
        this.IsBatch = IsBatch;
        this.BatchName = BatchName;
        this.SampleContainer = SampleContainer;
        this.SampleNumber = SampleNumber;
        this.longitude = longitude;
        this.latitude = latitude;
        this.Inspector = Inspector;
        this.InspectorTime = InspectorTime;
        this.InspectorPass = InspectorPass;
        this.PublisherMacAddress = PublisherMacAddress;
        this.Reviewer = Reviewer;
        this.ReviewerTime = ReviewerTime;
        this.ReviewerPass = ReviewerPass;
        this.ReviewerMacAddress = ReviewerMacAddress;
        this.CertificationResult = CertificationResult;
        this.RecordSource = RecordSource;
        this.Reserve1 = Reserve1;
    }


    public Long getId() {
        return this.Id;
    }
    public void setId(Long Id) {
        this.Id = Id;
    }
    public String getMaterial() {
        return this.Material;
    }
    public void setMaterial(String Material) {
        this.Material = Material;
    }
    public String getSampleName() {
        return this.SampleName;
    }
    public void setSampleName(String SampleName) {
        this.SampleName = SampleName;
    }
    public String getSampleMorphology() {
        return this.SampleMorphology;
    }
    public void setSampleMorphology(String SampleMorphology) {
        this.SampleMorphology = SampleMorphology;
    }
    public String getSeriesNumber() {
        return this.SeriesNumber;
    }
    public void setSeriesNumber(String SeriesNumber) {
        this.SeriesNumber = SeriesNumber;
    }
    public Integer getBatch() {
        return this.Batch;
    }
    public void setBatch(Integer Batch) {
        this.Batch = Batch;
    }
    public String getContainer() {
        return this.Container;
    }
    public void setContainer(String Container) {
        this.Container = Container;
    }
    public String getInstrumentModel() {
        return this.InstrumentModel;
    }
    public void setInstrumentModel(String InstrumentModel) {
        this.InstrumentModel = InstrumentModel;
    }
    public String getSoftwareVersion() {
        return this.SoftwareVersion;
    }
    public void setSoftwareVersion(String SoftwareVersion) {
        this.SoftwareVersion = SoftwareVersion;
    }
    public Integer getEvaluationMode() {
        return this.EvaluationMode;
    }
    public void setEvaluationMode(Integer EvaluationMode) {
        this.EvaluationMode = EvaluationMode;
    }
    public String getDescription() {
        return this.Description;
    }
    public void setDescription(String Description) {
        this.Description = Description;
    }
    public Integer getDetectionResult() {
        return this.DetectionResult;
    }
    public void setDetectionResult(Integer DetectionResult) {
        this.DetectionResult = DetectionResult;
    }
    public String getDetectionMan() {
        return this.DetectionMan;
    }
    public void setDetectionMan(String DetectionMan) {
        this.DetectionMan = DetectionMan;
    }
    public Long getSpecId() {
        return this.SpecId;
    }
    public void setSpecId(Long SpecId) {
        this.SpecId = SpecId;
    }
    public Date getDetectionTime() {
        return this.DetectionTime;
    }
    public void setDetectionTime(Date DetectionTime) {
        this.DetectionTime = DetectionTime;
    }
    public byte[] getMatchReport() {
        return this.MatchReport;
    }
    public void setMatchReport(byte[] MatchReport) {
        this.MatchReport = MatchReport;
    }
    public String getCompoundName() {
        return this.CompoundName;
    }
    public void setCompoundName(String CompoundName) {
        this.CompoundName = CompoundName;
    }
    public Integer getIsPost() {
        return this.IsPost;
    }
    public void setIsPost(Integer IsPost) {
        this.IsPost = IsPost;
    }
    public Integer getIsMQTT() {
        return this.IsMQTT;
    }
    public void setIsMQTT(Integer IsMQTT) {
        this.IsMQTT = IsMQTT;
    }
    public Double getLongitude() {
        return this.longitude;
    }
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
    public Double getLatitude() {
        return this.latitude;
    }
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
    public byte[] getReserve1() {
        return this.Reserve1;
    }
    public void setReserve1(byte[] Reserve1) {
        this.Reserve1 = Reserve1;
    }
    public Integer getCoreMethod() {
        return this.CoreMethod;
    }
    public void setCoreMethod(Integer CoreMethod) {
        this.CoreMethod = CoreMethod;
    }
    public String getCorrelation() {
        return this.Correlation;
    }
    public void setCorrelation(String Correlation) {
        this.Correlation = Correlation;
    }

    public boolean isCheck() {
        return isCheck;
    }

    public void setCheck(boolean check) {
        isCheck = check;
    }

    public boolean isEdit() {
        return isEdit;
    }

    public void setEdit(boolean edit) {
        isEdit = edit;
    }
    public Integer getIsBatch() {
        return this.IsBatch;
    }
    public void setIsBatch(Integer IsBatch) {
        this.IsBatch = IsBatch;
    }
    public String getInspector() {
        return this.Inspector;
    }
    public void setInspector(String Inspector) {
        this.Inspector = Inspector;
    }
    public Date getInspectorTime() {
        return this.InspectorTime;
    }
    public void setInspectorTime(Date InspectorTime) {
        this.InspectorTime = InspectorTime;
    }
    public Integer getInspectorPass() {
        return this.InspectorPass;
    }
    public void setInspectorPass(Integer InspectorPass) {
        this.InspectorPass = InspectorPass;
    }
    public String getReviewer() {
        return this.Reviewer;
    }
    public void setReviewer(String Reviewer) {
        this.Reviewer = Reviewer;
    }
    public Date getReviewerTime() {
        return this.ReviewerTime;
    }
    public void setReviewerTime(Date ReviewerTime) {
        this.ReviewerTime = ReviewerTime;
    }
    public Integer getReviewerPass() {
        return this.ReviewerPass;
    }
    public void setReviewerPass(Integer ReviewerPass) {
        this.ReviewerPass = ReviewerPass;
    }
    public Integer getCertificationResult() {
        return this.CertificationResult;
    }
    public void setCertificationResult(Integer CertificationResult) {
        this.CertificationResult = CertificationResult;
    }
    public double getConfidence() {
        return this.Confidence;
    }
    public void setConfidence(double Confidence) {
        this.Confidence = Confidence;
    }
    public String getPublisherMacAddress() {
        return this.PublisherMacAddress;
    }
    public void setPublisherMacAddress(String PublisherMacAddress) {
        this.PublisherMacAddress = PublisherMacAddress;
    }
    public String getReviewerMacAddress() {
        return this.ReviewerMacAddress;
    }
    public void setReviewerMacAddress(String ReviewerMacAddress) {
        this.ReviewerMacAddress = ReviewerMacAddress;
    }

    public String getRecordSource() {
        return this.RecordSource;
    }

    public void setRecordSource(String RecordSource) {
        this.RecordSource = RecordSource;
    }
    
    public String getSampleContainer() {
        return this.SampleContainer;
    }

    public void setSampleContainer(String SampleContainer) {
        this.SampleContainer = SampleContainer;
    }

    public Integer getSampleNumber() {
        return this.SampleNumber;
    }

    public void setSampleNumber(Integer SampleNumber) {
        this.SampleNumber = SampleNumber;
    }

    public String getBatchName() {
        return this.BatchName;
    }

    public void setBatchName(String BatchName) {
        this.BatchName = BatchName;
    }
}


