package com.ssraman.drugraman.newentiry;

import com.ssraman.drugraman.constant.InterFaceConst;
import com.ssraman.drugraman.db.entity.FtInfo;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.lib_common.communication.TransitionUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/7/4
 */
public class MatchFtInfo {
    private FtInfo ftInfo;
    private List<PeakInfo> peakList;

    private double sendWave = 0;
    private int startIndex = 0;
    private int startWave=InterFaceConst.start_wave;
    //原始数据
    private double[] sourceWave ;
    private double[] sourceIntensity;
    //private double[] soureLumbda;

    //有效数据
    private double[] useSourceWave;
    private double[] useSourceIntensity;
    //private double[] useSourceLumbda;

    //有效数据特征峰
    private List<PeakInfo> usePeakList;

    //拉基线后数据
    private double[] autoLineSourceWave;
    private double[] autoLineIntensity;
    //private double[] autoLineLumbda;

    //有拉基线后特征峰
    private List<PeakInfo> autoLinePeakList;

    private byte[] detectionData;

    /// <summary>
    /// 获取有效数据
    /// </summary>
    private void GetUseData()
    {
        int length = 0;
        for (int i = 0; i < sourceWave.length; i++)
        {
            if (sourceWave[i] > startWave)
            {
                length = sourceWave.length - i;
                break;
            }
        }

        int index = 0;
        useSourceWave = new double[length];
        useSourceIntensity = new double[length];
        //useSourceLumbda = new double[length];

        for (int i = 0; i < sourceWave.length; i++)
        {
            if (sourceWave[i] > startWave)
            {
                useSourceWave[index] = sourceWave[i];
                useSourceIntensity[index] = sourceIntensity[i];
                //useSourceLumbda[index] = soureLumbda[i];
                index++;
            }
        }
    }
    private void GetUsePeakList()
    {
        if(usePeakList==null)
        {
            usePeakList = new ArrayList<>();
        }
        usePeakList.clear();
        for(int k=0;k<peakList.size();k++)
        {
            PeakInfo item =peakList.get(k);
           // if (SpecUtils.ChangeLumbdaToWave(item.getLumbda(), sendWave) >= startWave)
            if (item.getWave() >= startWave)
            //if (item.get >= InterFaceConst.start_wave)
            {
                usePeakList.add(item);
            }
        }
    }

    public FtInfo getFtInfo() {
        return ftInfo;
    }

    public void setFtInfo(FtInfo ftInfo) {
        this.ftInfo = ftInfo;
        sourceIntensity = TransitionUtil.byteArray2DoubleArray(this.ftInfo.getObIntensity());

        if (this.ftInfo.getObNoise() != null)//根据数据中的噪声值去噪处理
        {
            double[] nosie = TransitionUtil.byteArray2DoubleArray(this.ftInfo.getObNoise());
            if (sourceIntensity[0] > nosie[0])
            {
                for (int i = 0; i < sourceIntensity.length; i++)
                {
                    sourceIntensity[i] = sourceIntensity[i] - nosie[i];
                    if (sourceIntensity[i] < 0)
                        sourceIntensity[i] = 0;
                }
            }
        }
        //soureLumbda = TransitionUtil.byteArray2DoubleArray(this.ftInfo.getObLumbda());
        //sourceWave = SpecUtils.ChangeLumbdaToWave(soureLumbda, this.ftInfo.getSendWave());
        sourceWave = TransitionUtil.byteArray2DoubleArray(this.ftInfo.getObWave());
        sendWave = this.ftInfo.getSendWave();
        startWave=ftInfo.getStartWave();
        GetUseData();
    }

    public List<PeakInfo> getPeakList() {
        return peakList;
    }

    public void setPeakList(List<PeakInfo> peakList) {
        this.peakList = peakList;
        GetUsePeakList();
    }

    public double[] getSourceWave() {
        return sourceWave;
    }

    public double[] getSourceIntensity() {
        return sourceIntensity;
    }

//    public double[] getSoureLumbda() {
//        return soureLumbda;
//    }

    public double[] getUseSourceWave() {
        return useSourceWave;
    }

    public double[] getUseSourceIntensity() {
        return useSourceIntensity;
    }

//    public double[] getUseSourceLumbda() {
//        return useSourceLumbda;
//    }

    public List<PeakInfo> getUsePeakList() {
        return usePeakList;
    }

    public double[] getAutoLineSourceWave() {
        return autoLineSourceWave;
    }

    public void setAutoLineSourceWave(double[] autoLineSourceWave) {
        this.autoLineSourceWave = autoLineSourceWave;
    }

    public double[] getAutoLineIntensity() {
        return autoLineIntensity;
    }

    public void setAutoLineIntensity(double[] autoLineIntensity) {
        this.autoLineIntensity = autoLineIntensity;
    }

    public List<PeakInfo> getAutoLinePeakList() {
        return autoLinePeakList;
    }

    public void setAutoLinePeakList(List<PeakInfo> autoLinePeakList) {
        this.autoLinePeakList = autoLinePeakList;
    }

    public byte[] getDetectionData() {
        return detectionData;
    }

    public void setDetectionData(byte[] detectionData) {
        this.detectionData = detectionData;
    }
}
