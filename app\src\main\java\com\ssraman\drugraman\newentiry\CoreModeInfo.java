package com.ssraman.drugraman.newentiry;

import androidx.annotation.NonNull;

import com.ssraman.drugraman.typeface.CoreModeType;

/**
 * @author: Administrator
 * @date: 2022/9/9
 */
public class CoreModeInfo {
    private String core_mode_name;
    private int core_mode_value;

    public CoreModeInfo(int core_mode_value) {
        this.core_mode_value = core_mode_value;
        this.core_mode_name = CoreModeType.valueOf(this.core_mode_value).name();
    }

    public String getCoreModeName() {
        return this.core_mode_name;
    }

    public int getCoreModeValue() {
        return this.core_mode_value;
    }



    @NonNull
    @Override
    public String toString() {
        return this.core_mode_name.toString();
    }
}
