package com.ssraman.drugraman.common.algorithm;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.ssraman.drugraman.business.SpecAndNoise;
import com.ssraman.drugraman.business.SpecPeakData;
import com.ssraman.drugraman.constant.InterFaceConst;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.ssbj.support.ssrfunlib;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;

/**
 * @author: Administrator
 * @date: 2021/7/4
 */
public class soAlgorithm {
    public static ssrfunlib soLib = new ssrfunlib();

    /**
     * 获取新x轴的坐标
     *
     * @param len
     * @return
     */
    public static double[] getnewx(int len) {
        double[] newx = new double[len];
        for (int i = 0; i < len; i++)
            newx[i] = i + 20;
        return newx;
    }

    /**
     * 获取检测后y轴的坐标
     *
     * @param data
     * @param size
     * @return
     */
    public static double[] getIntensity(byte[] data, int size) {
        double[] intensity = new double[2048];
        int tempvalue, tempvalue2;
        tempvalue = 0;

        for (int i = 1; i < 5; i++) {
            tempvalue2 = data[size - i];
            tempvalue2 = tempvalue2 & 0xff;
            tempvalue += tempvalue2 + (tempvalue << 8);
        }
        double tempvalue3 = (tempvalue == 1) ? tempvalue : 0xffff / tempvalue;

        for (int i = 0; i < 2048; i++) {
            tempvalue = data[i << 1];
            tempvalue = tempvalue & 0xff;
            tempvalue2 = data[(i << 1) + 1];
            tempvalue2 = (tempvalue2 & 0xff) << 8;
            intensity[i] = (tempvalue + tempvalue2) * tempvalue3;
        }
        return intensity;
    }

    public static double[] getIntensityNew(byte[] data,  int size) {
        final String TAG = "数据转换函数";
        int tempvalue, tempvalue2;
        tempvalue = 0;

        for (int i = 1; i < 5; i++) {
            tempvalue2 = data[size - i];
            tempvalue2 = tempvalue2 & 0xff;
            tempvalue += tempvalue2 + (tempvalue << 8);
        }
        double tempvalue3 = (tempvalue == 1) ? tempvalue : 0xffff / tempvalue;

        int[] absData = new int[data.length];
        for (int i = 0; i < data.length; i++) {
            absData[i] = data[i];
            absData[i] = absData[i] & 0xff;
        }

        char[] cintensity = new char[2048];
        for (int i = 0; i < 2048; i++) {
            int i1 = absData[i * 2] + absData[i * 2 + 1] * 256;
            cintensity[i] = (char) (absData[ i * 2] + absData[ i * 2 + 1] * 256);
        }
        double[] jpIntensity = new double[cintensity.length];
        int convertdata = soLib.od_convertdata(cintensity, jpIntensity, jpIntensity.length, tempvalue3);
        if (convertdata != 1) {
            Log.e(TAG, "指纹认证错误");
            return jpIntensity;
        }
        return jpIntensity;
    }

    public static int PreData(double[] y) {
        int re = soLib.SSR_encodeSoft(y);
        return re;
    }


    /**
     * @param x       //x轴数据
     * @param y       //y轴数据
     * @param newx    // 数据长度
     * @param lowline // 基线平滑启始波数
     * @return 平滑的重采样数据，newx长度，重采样后波数为0-sampleNum cm-1，数据间距为1cm-1
     * @Title: PreCacu
     * @Description: 预处理
     * <AUTHOR>
     * @date 2017年11月15日
     */
    public static double[] PreCacu(double[] x, double[] y, double[] newx, double lowline
    ) {

        int len = newx.length;
        double[] newy = new double[len];
        soLib.od_newspec(x, y, newx, newy, (int) lowline);
        return newy;
    }

    /**
     * @param x       //x轴数据
     * @param y       //y轴数据
     * @param newx    // 数据长度
     * @param lowline // 基线平滑启始波数
     * @return 平滑的重采样数据，newx长度，重采样后波数为0-sampleNum cm-1，数据间距为1cm-1
     * @Title: PreCacu
     * @Description: 预处理
     * <AUTHOR>
     * @date 2017年11月15日
     */
    public static SpecAndNoise PreCacu2(double[] x, double[] y, double[] newx, double lowline) {
        SpecAndNoise returnSpecAndNoise = new SpecAndNoise();
        int len = newx.length;
        double[] newy = new double[len];
        int noise = soLib.od_newspec(x, y, newx, newy, (int) lowline);
        returnSpecAndNoise.sepc = newy;
        returnSpecAndNoise.noise = noise;
        return returnSpecAndNoise;

    }

    /**
     * @param wave_num //x轴数据
     * @param peak     //y轴数据
     * @param snr      信噪比 30比较符合肉眼习惯
     * @return 特征峰波数
     * @Title: date_xunfen
     * @Description: 搜索与最高信号幅度比小于30：1的特征峰，需要先运行平滑函数smooth
     * <AUTHOR>
     * @date 2017年10月27日
     */
    public static List<PeakInfo> date_xunfen(double[] wave_num, double[] peak, double snr, int noise) {
        final String TAG = "寻峰函数";
        List<PeakInfo> re_peak_list = new ArrayList<>();
        double[] peakspos = new double[14];       //返回峰位
        double[] peakwidth = new double[14];   //返回峰宽
        double[] peakh = new double[14];       //返回峰高
        Log.d(TAG, "do_seekPeaks");

        int newCnt = 0;
        try {
            newCnt = soLib.od_seekPeaks(wave_num,
                    peak,             //光谱
                    snr,
                    peakspos,
                    peakh
            );
            String peaks_str = "peaks:\n";
            for (int i = 0; i < newCnt; i++) {
                peaks_str += peakspos[i] + "," + peakh[i] + "\n";
            }
            Log.d(TAG, peaks_str);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, e.getMessage());
        }

        //===========================
        double max_peak_h = 0;
        double mean_peak_h = 0;
        if (newCnt == 0) {
            return re_peak_list;
        }
        for (int i = 0; i < newCnt; i++) {
            if (peakh[i] > max_peak_h) {
                max_peak_h = peakh[i];
                //max_peak_id = i;
            }
            mean_peak_h += peakh[i];
        }
        mean_peak_h /= newCnt;
        //最大值和噪声比值小
        double noise_d = max_peak_h / noise;
        //最大值和平均值的比值
        max_peak_h /= mean_peak_h;

        if (max_peak_h < 2 && noise_d < 15 && newCnt == 14) {
            newCnt = 0;
            return re_peak_list;
        }
        //===========================
        //获取对应的值
        for (int i = 0; i < newCnt; i++) {
            if (peakh[i] < 800.0) {
                continue;
            }
            PeakInfo peakInfo = new PeakInfo();
            peakInfo.setWave(peakspos[i]);
            peakInfo.setIntensity(peakh[i]);
            peakInfo.setMust(1);
            peakInfo.setMatchLimit(10.0);
            peakInfo.setCalibration(0.0);
            peakInfo.setEnableCalibration(0);
            peakInfo.setFtId(0);
            peakInfo.setStartIntensity(0.0);
            peakInfo.setType(0);
            peakInfo.setLumbda(0.0);
            int peak_index = 0;
            for (int k = 0; k < wave_num.length; k++) {
                if (wave_num[k] == peakspos[i]) {
                    peak_index = k;
                    break;
                }
            }
            peakInfo.setPeakId(peak_index);
            peakInfo.setSendWave(InterFaceConst.SEND_WAVE);
            re_peak_list.add(peakInfo);
        }
        return re_peak_list;
    }

    public static List<PeakInfo> date_xunfen_idx(double[] wave_num, double[] peak, double snr,int noise) {
        final String TAG = "寻峰函数";
        List<PeakInfo> re_peak_list = new ArrayList<>();
        double maxV = 0;
        int maxID = 0;

        int len = peak.length;
        int max_peak_cnt=50;//14
        double[] peakspos = new double[max_peak_cnt];       //返回峰位
        double[] peakwidth = new double[max_peak_cnt];   //返回峰宽
        double[] peakh = new double[max_peak_cnt];
        int[] pid = new int[max_peak_cnt];
        int[] _pid = new int[max_peak_cnt];

        int[] olen = new int[]{len};//返回峰高
        Log.d(TAG, "do_seekPeaks");
        int newCnt=0;
        int cnewCnt=0;
        try {


            newCnt = soLib.od_seekPeaks_idx(wave_num,
                    peak,             //光谱
                    snr,
                    olen,
                    pid,
                    max_peak_cnt
            );
            if (newCnt>0) {
                for (int i = 0; i<len; i++)
                {
                    if (maxV < peak[i]) {
                        maxV = peak[i];
                        maxID = i;
                    }
                }
                for (int i = 1; i<=newCnt; i++) {
                    if (peak[pid[i]] * snr < peak[maxID])
                    {
                        //result->num = i + 1;
                        continue;
                    }
                    peakspos[i-1] = wave_num[pid[i]];
                    peakh[i-1] = peak[pid[i]];
                    _pid[i-1]=pid[i];
                    cnewCnt++;
                }

            }
//            cnewCnt=newCnt;
            String peaks_str="peaks:\n";
            for(int i=0;i<cnewCnt;i++){
                if(peakh[i]!=0) {
                    peaks_str += peakspos[i] + "," + peakh[i] + "\n";
                }
                else
                {
                    cnewCnt--;
                }
            }
            Log.d(TAG,peaks_str);
        }
        catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG,"date_xunfen_idx",e);
        }


        //===========================
        double max_peak_h = 0;
        double mean_peak_h = 0;
        if (cnewCnt==0){
            return re_peak_list;
        }
        for (int i = 0; i < cnewCnt; i++) {
            if (peakh[i] > max_peak_h) {
                max_peak_h = peakh[i];
                //max_peak_id = i;
            }
            mean_peak_h+= peakh[i];
        }
        //mean_peak_h /= newCnt;
        mean_peak_h /= cnewCnt;
        //最大值和噪声比值小
        double noise_d = max_peak_h / noise;
        //最大值和平均值的比值
        max_peak_h /= mean_peak_h;

        if (max_peak_h < 2 && noise_d < 15 && cnewCnt == max_peak_cnt) {
            cnewCnt = 0;
            return re_peak_list;
        }

        //===========================
        //获取对应的值
        for (int i = 0; i < newCnt; i++) {
//            if (peakh[i] < 800.0) {
//                continue;
//            }
            if (peakh[i] == 0.0) {
                continue;
            }
            PeakInfo peakInfo = new PeakInfo();
            peakInfo.setWave(peakspos[i]);
            peakInfo.setIntensity(peakh[i]);
            peakInfo.setMust(1);
            peakInfo.setMatchLimit(10.0);
            peakInfo.setCalibration(0.0);
            peakInfo.setEnableCalibration(0);
            peakInfo.setFtId(0);
            peakInfo.setStartIntensity(0.0);
            peakInfo.setType(0);
            peakInfo.setLumbda(0.0);
            peakInfo.setPeakId(_pid[i]);
            peakInfo.setSendWave(InterFaceConst.SEND_WAVE);
            re_peak_list.add(peakInfo);
        }
        return re_peak_list;
    }


    public static List<PeakInfo> date_xunfen_old(double[] wave_num, double[] peak, double snr) {
        List<PeakInfo> re_peak_list = new ArrayList<>();
        double[] peakspos = new double[14];       //返回峰位
        double[] peakwidth = new double[14];   //返回峰宽
        double[] peakh = new double[14];       //返回峰高
        int newCnt = soLib.od_seekPeaks(wave_num,
                peak,             //光谱
                snr,
                peakspos,
                peakh
        );

        //===========================
        //获取对应的值
        for (int i = 0; i < newCnt; i++) {
            if (peakh[i] < 800.0) {
                continue;
            }
            PeakInfo peakInfo = new PeakInfo();
            peakInfo.setWave(peakspos[i]);
            peakInfo.setIntensity(peakh[i]);
            int peak_index = 0;
            for (int k = 0; k < wave_num.length; k++) {
                if (wave_num[k] == peakspos[i]) {
                    peak_index = k;
                    break;
                }
            }
            peakInfo.setPeakId(peak_index);
            peakInfo.setSendWave(InterFaceConst.SEND_WAVE);
            re_peak_list.add(peakInfo);
        }
        return re_peak_list;
    }


    public static SpecAndNoise PreAutoBaseline(double[] x, double[] y, double[] newx, double lowline) {
        SpecAndNoise returnSpecAndNoise = new SpecAndNoise();
        int len = newx.length;
        double[] newy = new double[len];
        int noise = soLib.od_newspecautobase(x, y, newx, newy, (int) lowline);
        returnSpecAndNoise.sepc = newy;
        returnSpecAndNoise.noise = noise;
        return returnSpecAndNoise;
    }

    public static SpecAndNoise PreSmooth(double[] x, double[] y, double[] newx, double lowline) {
        SpecAndNoise returnSpecAndNoise = new SpecAndNoise();
        int len = newx.length;
        double[] newy = new double[len];
        int noise = soLib.od_newspecsmooth(x, y, newx, newy, (int) lowline);
        returnSpecAndNoise.sepc = newy;
        returnSpecAndNoise.noise = noise;
        return returnSpecAndNoise;
    }

    public static SpecAndNoise PreInterpolate(double[] x, double[] y, double[] newx, double lowline) {
        SpecAndNoise returnSpecAndNoise = new SpecAndNoise();
        int len = newx.length;
        double[] newy = new double[len];
        int noise = soLib.od_newspecinterpolate(x, y, newx, newy, (int) lowline);
        returnSpecAndNoise.sepc = newy;
        returnSpecAndNoise.noise = noise;
        return returnSpecAndNoise;

    }

    public static double PreCOSD(double wave_unkown[], double signal_unkown[], int length_unkown, double wave_library[], double signal_library[], int length_library,int startIndex){

        double re_correlation = soLib.od_cosdspec(wave_unkown,signal_unkown,length_unkown,wave_library,signal_library,length_library,(int)startIndex);
        return re_correlation;
    }

    public static double PreHQI(double wave_unkown[], double signal_unkown[], int length_unkown, double wave_library[], double signal_library[], int length_library,int startIndex) {

        double re_correlation = soLib.od_hqispec(wave_unkown,signal_unkown,length_unkown,wave_library,signal_library,length_library,(int)startIndex);
        return re_correlation;
    }

    public static double PreWholeCOSD(double wave_unkown[], double signal_unkown[], int length_unkown, double wave_library[], double signal_library[], int length_library,int startIndex){

        double re_correlation = soLib.od_cosdwholespec(wave_unkown,signal_unkown,length_unkown,wave_library,signal_library,length_library,(int)startIndex);
        return re_correlation;
    }

}
