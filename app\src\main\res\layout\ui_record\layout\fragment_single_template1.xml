<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="mpresenter"
            type="com.ssraman.drugraman.ui.record.SingleTemplate1Fragment.MPresenter" />

        <variable
            name="singleTemplate1ViewModel"
            type="com.ssraman.drugraman.ui.vm.SingleTemplate1ViewModel" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/activity_activities1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="52dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/t1_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:paddingTop="10dp"
                        android:text="@string/title_raman_analysis_report"
                        android:textAlignment="center"
                        android:textColor="@color/report_txt_color"
                        android:textSize="11dp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/t1_name"
                        android:layout_marginTop="1dp"
                        android:padding="1dp"
                        android:textAlignment="center" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">


                        <TextView
                            android:id="@+id/title_mac_model"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_device_model"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />


                        <TextView
                            android:id="@+id/edit_mac_model"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="DrugRaman "
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_software_version"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_software_version"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_software_version"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="V1.0 "
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:padding="2dp"
                        android:textColor="@color/report_txt_color"
                        android:textSize="8dp" />

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_operator"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_operator_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_operator"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_inspector_one_text"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_operator_date"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_measurement_date_time"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_operator_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="2021-11-10 13:00:01 "
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_batch"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_batch_number_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />


                        <TextView
                            android:id="@+id/edit_batch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="123456789 "
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_sample_name"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_sample_name_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />


                        <TextView
                            android:id="@+id/edit_sample_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_test_sample_1"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_sample_descriptione"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_sample_description_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />


                        <TextView
                            android:id="@+id/edit_sample_descriptione"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_white_solid"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/t1_text1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:background="@color/nc_sub"
                        android:padding="5dp"
                        android:text="@string/title_quick_comparison"
                        android:textAlignment="center"
                        android:textColor="@color/white"
                        android:textSize="9dp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="2dp"
                        android:textColor="@color/white"
                        android:textSize="8dp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/txt_detection_result"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="10dp"
                            android:drawableLeft="@drawable/ic_pass_32"
                            android:gravity="center"
                            android:text="@string/title_accepted"
                            android:textColor="@color/result_green"
                            android:textSize="26sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_core_method"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_method_name_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />


                        <TextView
                            android:id="@+id/edit_core_method"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_pca"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_Material_name"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_material_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />


                        <TextView
                            android:id="@+id/edit_Material_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_tale_pe_bag"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_threshold"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_threshold_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />


                        <TextView
                            android:id="@+id/edit_threshold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="96 "
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_evaluation_mode"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_evaluation_mode_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_evaluation_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_material_identification"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_Material_type"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_material_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_Material_type"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_raw_material"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/title_describe"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_description_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_describe"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="1234567   "
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <com.ssraman.drugraman.sortableview.CorrelationExSorTableView
                        android:id="@+id/table_correlation"
                        android:layout_width="300dp"
                        android:layout_height="220dp"
                        android:layout_marginTop="10dp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/activity_activities2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/t1_text10"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:background="@color/nc_sub"
                        android:padding="5dp"
                        android:text="@string/title_spectrum_chart"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="9dp"
                        android:textStyle="bold" />

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:weightSum="1">

                        <com.ssraman.drugraman.custom.SpectrumLineChart
                            android:id="@+id/spec_detection_result"
                            android:layout_width="280dp"
                            android:layout_height="230dp"
                            android:layout_marginTop="5dp"
                            android:background="@color/white"
                            app:axisTextColor="@color/black"
                            app:mainSpecColor="@color/black" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="150dp"
                        android:layout_height="wrap_content"
                        android:padding="2dp"
                        android:textSize="32sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:weightSum="1">

                        <TextView
                            android:id="@+id/title_publisher"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_publisher_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_publisher"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_publisher_name"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/title_publisher_date"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_publish_date_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_publisher_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_date_format_slash"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_publisher_mac"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:layout_marginBottom="5dp"
                            android:padding="2dp"
                            android:text="@string/title_mac_address_format"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:weightSum="1">

                        <TextView
                            android:id="@+id/title_reviewer"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_reviewer_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_reviewer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_reviewer_name"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/title_reviewer_date"
                            android:layout_width="150dp"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:text="@string/title_review_date_with_colon"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_reviewer_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:padding="2dp"
                            android:text="@string/title_date_format_slash"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />

                        <TextView
                            android:id="@+id/edit_reviewer_mac"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="30dp"
                            android:layout_marginBottom="5dp"
                            android:padding="2dp"
                            android:text="@string/title_mac_address_format"
                            android:textColor="@color/report_txt_color"
                            android:textSize="8dp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <Button
                android:id="@+id/btn_creat_pdf"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/report_btn_bg_color"
                android:bottomLeftRadius="8dp"
                android:bottomRightRadius="118dp"
                android:onClick="@{(view)->mpresenter.CreatPdfClick(view)}"
                android:padding="10dp"
                android:text="@string/create_pdf"
                android:textColor="@color/report_btn_color"
                android:topLeftRadius="8dp"
                android:topRightRadius="8dp" />

            <Button
                android:id="@+id/btn_save_pdf"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                android:background="@color/report_btn_bg_color"
                android:bottomLeftRadius="8dp"
                android:bottomRightRadius="118dp"
                android:onClick="@{(view)->mpresenter.SavePdfClick(view)}"
                android:padding="10dp"
                android:text="@string/save_pdf"
                android:textColor="@color/report_btn_color"
                android:topLeftRadius="8dp"
                android:topRightRadius="8dp" />

        </LinearLayout>
    </ScrollView>
</layout>