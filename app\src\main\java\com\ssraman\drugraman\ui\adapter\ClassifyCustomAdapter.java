package com.ssraman.drugraman.ui.adapter;

import android.content.Context;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.databinding.ItemCustomClassifyBinding;
import com.ssraman.drugraman.db.entity.SampleTypeInfo;
import com.ssraman.drugraman.item.SampleItem;
import com.ssraman.lib_common.adapter.SingleRecyclerAdapter;

import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/7/13
 */
public class ClassifyCustomAdapter extends SingleRecyclerAdapter<SampleTypeInfo, ItemCustomClassifyBinding> {
    private final int layout;

    public ClassifyCustomAdapter(Context mContext, int layout) {
        super(mContext);
        this.layout = layout;
    }

    @Override
    protected int getLayoutResId(int viewType) {
        return this.layout;
    }

    @Override
    protected void onBindItem(ItemCustomClassifyBinding binding, SampleTypeInfo item, ViewHolder holder) {
        holder.setIvSelect(binding.ivSelect);
        SampleItem sampleItem=new SampleItem(item.getName(),item.getImageIndex());
        binding.setSampleItem(sampleItem);
        binding.ivSelect.bringToFront();
    }
}
