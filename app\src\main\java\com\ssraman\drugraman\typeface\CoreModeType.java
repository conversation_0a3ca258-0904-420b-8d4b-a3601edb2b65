package com.ssraman.drugraman.typeface;

/**
 * @author: Administrator
 * @date: 2021/11/2
 */
public enum CoreModeType {
    光谱COSD算法(0), //
    光谱HQI算法(1), //
    光谱AI算法(2), //
    光谱PCA算法(3), //

    全光谱COSD算法(5);//

    private int value = 0;

    private CoreModeType(int value) { // 必须是private的，否则编译错误
        this.value = value;
    }

    public static CoreModeType valueOf(int value) { // 手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return 光谱COSD算法;
            case 1:
                return 光谱HQI算法;
            case 2:
                return 光谱AI算法;
            case 3:
                return 光谱PCA算法;
            case 5:
                return 全光谱COSD算法;

            default:
                return 光谱COSD算法;
        }
    }

    public int value() {
        return this.value;
    }
}
