package com.ssraman.drugraman.db.repository;

import com.ssraman.drugraman.db.entity.SampleTypeInfo;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.gen.SampleTypeInfoDao;
import com.ssraman.drugraman.db.gen.User_infoDao;
import com.ssraman.lib_common.room.EmptyResultSetException;
import com.ssraman.lib_common.room.RxRoom;

import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Completable;
import io.reactivex.Single;

/**
 * @author: Administrator
 * @date: 2021/6/20
 */
public class SysSampleTypeRepository {

    private DaoSession mDaoSession;
    private SampleTypeInfoDao sampleTypeDao;

    public SysSampleTypeRepository(DaoSession daoSession) {
        this.mDaoSession = daoSession;
        sampleTypeDao = this.mDaoSession.getSampleTypeInfoDao();
    }

    public Completable insertSampleType(SampleTypeInfo sampleTypeInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                sampleTypeDao.insert(sampleTypeInfo);
                return null;
            }
        });
    }

    public long insertSampleTypeByRe(SampleTypeInfo sampleTypeInfo)
    {
        return sampleTypeDao.insert(sampleTypeInfo);
    }

    public Completable deleteSampleType(SampleTypeInfo sampleTypeInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                sampleTypeDao.delete(sampleTypeInfo);
                return null;
            }
        });
    }

    public Completable updateSampleType(SampleTypeInfo sampleTypeInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                sampleTypeDao.update(sampleTypeInfo);
                return null;
            }
        });
    }

    public void updateSampleTypeByRe(SampleTypeInfo sampleTypeInfo) {
        sampleTypeDao.update(sampleTypeInfo);
    }

    public Single<List<SampleTypeInfo>> getDirectSubSampleType(int parentid) {
        return RxRoom.createSingle(new Callable<List<SampleTypeInfo>>() {
            @Override
            public List<SampleTypeInfo> call() throws Exception {
                List<SampleTypeInfo> sampleTypeInfoList = sampleTypeDao.queryBuilder().where(SampleTypeInfoDao.Properties.ParentId.eq(parentid)).list();
                return sampleTypeInfoList;
            }
        });
    }

    public SampleTypeInfo getSampleTypeById(Long id) {
        List<SampleTypeInfo> list = sampleTypeDao.queryBuilder().where(SampleTypeInfoDao.Properties.Id.eq(id)).list();
        if(list==null||list.size()==0)
            return null;
        SampleTypeInfo record = list.get(0);
        return record;
    }

    public List<SampleTypeInfo> getSampleTypeByParentId(Long parent_id)
    {
        List<SampleTypeInfo> list = sampleTypeDao.queryBuilder().where(SampleTypeInfoDao.Properties.ParentId.eq(parent_id)).list();
        return list;
    }

    public Single<List<SampleTypeInfo>> getAllSampleType() {
        return RxRoom.createSingle(new Callable<List<SampleTypeInfo>>() {
            @Override
            public List<SampleTypeInfo> call() throws Exception {
                List<SampleTypeInfo> sampleTypeInfoList = sampleTypeDao.queryBuilder().list();
                return sampleTypeInfoList;
            }
        });
    }

    public Single<List<SampleTypeInfo>> getAllSampleType(int directoryType) {
        return RxRoom.createSingle(new Callable<List<SampleTypeInfo>>() {
            @Override
            public List<SampleTypeInfo> call() throws Exception {
                List<SampleTypeInfo> sampleTypeInfoList = sampleTypeDao.queryBuilder().where(SampleTypeInfoDao.Properties.Type.eq(directoryType)).list();
                return sampleTypeInfoList;
            }
        });
    }

    //返回样品节点的SampleTypeInfo
    public SampleTypeInfo getNodeSampleTypeInfo(long id)
    {
        List<SampleTypeInfo> samtypeList = getSampleTypeByParentId(id);
        if (samtypeList.size() > 0)//在sampleType库中id作为父节点存在子节点，说明此id不是样品项节点；
            return null;
        SampleTypeInfo sampleType = getSampleTypeById(id);
        if (sampleType == null)
            return null;
        return sampleType;
    }

    public SampleTypeInfo existsSampleItem(long parent_id,String sample_name)
    {
        List<SampleTypeInfo> samtypeList = sampleTypeDao.queryBuilder().where(SampleTypeInfoDao.Properties.Type.eq(3),SampleTypeInfoDao.Properties.ParentId.eq(parent_id),SampleTypeInfoDao.Properties.Name.eq(sample_name.trim())).list();
        if(samtypeList.size()>0)
        {
            return samtypeList.get(0);
        }
        return null;
    }

}
