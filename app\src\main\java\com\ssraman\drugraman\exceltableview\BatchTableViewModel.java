package com.ssraman.drugraman.exceltableview;

import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;
import com.ssraman.drugraman.exceltableview.model.Cell;
import com.ssraman.drugraman.exceltableview.model.ColTitle;
import com.ssraman.drugraman.exceltableview.model.RowTitle;
import com.ssraman.lib_common.utils.TimeUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/11/24
 */
public class BatchTableViewModel {

    private List<ColTitle> mColumnHeaderModelList;
    private List<RowTitle> mRowHeaderModelList;
    private List<List<Cell>> mCellModelList;

    public BatchTableViewModel() {
        mColumnHeaderModelList = createColumnHeaderModelList();
    }

    private List<ColTitle> createColumnHeaderModelList() {
        List<ColTitle> list = new ArrayList<ColTitle>();

        // Create Column Headers
        list.add(new ColTitle("样品名称",80f));
        list.add(new ColTitle("批次号",90f));
        list.add(new ColTitle("检验时间",100f));
        list.add(new ColTitle("检验人员",60f));
        list.add(new ColTitle("检验结果",70f));
        list.add(new ColTitle("验证项目",60f));
        list.add(new ColTitle("相似度",50f));

        return list;
    }


    public List<RowTitle> createRowHeaderList(int start,int size) {
        List<RowTitle> list = new ArrayList<RowTitle>();
        int end=start+size;
        for (int i = start; i < end; i++) {
            // In this example, Row headers just shows the index of the TableView List.
            list.add(new RowTitle(String.valueOf(i + 1)));
        }
        return list;
    }

    public List<List<Cell>> createCellModelList(List<ArchiveRecordsInfo> recordList) {
        List<List<Cell>> lists = new ArrayList<>();

        // Creating cell model list from PeakInfo list for Cell Items

        for (int i = 0; i < recordList.size(); i++) {
            ArchiveRecordsInfo record_info = recordList.get(i);

            List<Cell> list = new ArrayList<>();

            // The order should be same with column header list;
            list.add(new Cell(i+1, record_info.getSampleName(),0));
            list.add(new Cell(i+1,record_info.getSeriesNumber(),0));
            String detection_time = TimeUtils.date2String(record_info.getDetectionTime());
            list.add(new Cell(i+1, detection_time,0));
            list.add(new Cell(i+1,  record_info.getDetectionMan(),0));
            list.add(new Cell(i+1, record_info.getDetectionResult()==1?"ACCEPTED":"REJECTED",0));
            list.add(new Cell(i+1, record_info.getMaterial(),0));
            list.add(new Cell(i+1, record_info.getCorrelation(),0));

            // Add
            lists.add(list);
        }

        return lists;
    }


    public List<ColTitle> getColumHeaderModeList() {
        return mColumnHeaderModelList;
    }

    public List<RowTitle> getRowHeaderModelList() {
        return mRowHeaderModelList;
    }

    public List<List<Cell>> getCellModelList() {
        return mCellModelList;
    }


    public void generateListForTableView(int start,List<ArchiveRecordsInfo> recordList) {

        mCellModelList = createCellModelList(recordList);
        mRowHeaderModelList = createRowHeaderList(start,recordList.size());
    }

}
