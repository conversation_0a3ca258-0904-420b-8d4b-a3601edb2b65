package com.ssraman.drugraman.rbac;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;

import com.ssraman.drugraman.db.entity.RbacUser;
import com.ssraman.drugraman.db.entity.RbacRole;
import com.ssraman.drugraman.db.entity.RbacPermission;
import com.ssraman.drugraman.rbac.security.SecurityContext;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 安全上下文测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class SecurityContextTest {
    
    private RbacUser testUser;
    private List<RbacRole> testRoles;
    private List<RbacPermission> testPermissions;
    private String testSessionToken;
    
    @Before
    public void setUp() {
        // 清除安全上下文
        SecurityContext.clear();
        
        // 创建测试数据
        testUser = new RbacUser();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setFullName("Test User");
        testUser.setEmail("<EMAIL>");
        
        RbacRole operatorRole = new RbacRole();
        operatorRole.setId(1L);
        operatorRole.setRoleCode("OPERATOR");
        operatorRole.setRoleName("操作员");
        operatorRole.setLevel(1);
        operatorRole.setEnabled(true);
        
        RbacRole reviewerRole = new RbacRole();
        reviewerRole.setId(2L);
        reviewerRole.setRoleCode("REVIEWER");
        reviewerRole.setRoleName("审核员");
        reviewerRole.setLevel(2);
        reviewerRole.setEnabled(true);
        
        testRoles = Arrays.asList(operatorRole, reviewerRole);
        
        RbacPermission detectPermission = new RbacPermission();
        detectPermission.setId(1L);
        detectPermission.setPermissionCode("DETECTION_EXECUTE");
        detectPermission.setPermissionName("执行检测");
        detectPermission.setEnabled(true);
        
        RbacPermission reportPermission = new RbacPermission();
        reportPermission.setId(2L);
        reportPermission.setPermissionCode("REPORT_VIEW");
        reportPermission.setPermissionName("查看报告");
        reportPermission.setEnabled(true);
        
        testPermissions = Arrays.asList(detectPermission, reportPermission);
        
        testSessionToken = "test-session-token-123";
    }
    
    @After
    public void tearDown() {
        // 清除安全上下文
        SecurityContext.clear();
    }
    
    @Test
    public void testSetAndGetCurrentUser() {
        // 测试设置和获取当前用户
        assertNull("初始状态下当前用户应该为空", SecurityContext.getCurrentUser());
        
        SecurityContext.setCurrentUser(testUser);
        
        RbacUser currentUser = SecurityContext.getCurrentUser();
        assertNotNull("设置后当前用户不应该为空", currentUser);
        assertEquals("用户ID应该匹配", testUser.getId(), currentUser.getId());
        assertEquals("用户名应该匹配", testUser.getUsername(), currentUser.getUsername());
    }
    
    @Test
    public void testGetCurrentUserId() {
        // 测试获取当前用户ID
        assertNull("初始状态下当前用户ID应该为空", SecurityContext.getCurrentUserId());
        
        SecurityContext.setCurrentUser(testUser);
        
        Long currentUserId = SecurityContext.getCurrentUserId();
        assertNotNull("设置用户后当前用户ID不应该为空", currentUserId);
        assertEquals("用户ID应该匹配", testUser.getId(), currentUserId);
    }
    
    @Test
    public void testGetCurrentUsername() {
        // 测试获取当前用户名
        assertNull("初始状态下当前用户名应该为空", SecurityContext.getCurrentUsername());
        
        SecurityContext.setCurrentUser(testUser);
        
        String currentUsername = SecurityContext.getCurrentUsername();
        assertNotNull("设置用户后当前用户名不应该为空", currentUsername);
        assertEquals("用户名应该匹配", testUser.getUsername(), currentUsername);
    }
    
    @Test
    public void testSetAndGetCurrentRoles() {
        // 测试设置和获取当前角色
        assertNull("初始状态下当前角色应该为空", SecurityContext.getCurrentRoles());
        
        SecurityContext.setCurrentRoles(testRoles);
        
        List<RbacRole> currentRoles = SecurityContext.getCurrentRoles();
        assertNotNull("设置后当前角色不应该为空", currentRoles);
        assertEquals("角色数量应该匹配", testRoles.size(), currentRoles.size());
        
        Set<String> roleCodes = SecurityContext.getCurrentRoleCodes();
        assertNotNull("角色代码集合不应该为空", roleCodes);
        assertTrue("应该包含操作员角色", roleCodes.contains("OPERATOR"));
        assertTrue("应该包含审核员角色", roleCodes.contains("REVIEWER"));
    }
    
    @Test
    public void testGetCurrentMaxRoleLevel() {
        // 测试获取当前最高角色级别
        assertEquals("初始状态下最高角色级别应该为0", 0, SecurityContext.getCurrentMaxRoleLevel());
        
        SecurityContext.setCurrentRoles(testRoles);
        
        int maxLevel = SecurityContext.getCurrentMaxRoleLevel();
        assertEquals("最高角色级别应该为2", 2, maxLevel);
    }
    
    @Test
    public void testSetAndGetCurrentPermissions() {
        // 测试设置和获取当前权限
        assertNull("初始状态下当前权限应该为空", SecurityContext.getCurrentPermissions());
        
        SecurityContext.setCurrentPermissions(testPermissions);
        
        List<RbacPermission> currentPermissions = SecurityContext.getCurrentPermissions();
        assertNotNull("设置后当前权限不应该为空", currentPermissions);
        assertEquals("权限数量应该匹配", testPermissions.size(), currentPermissions.size());
        
        Set<String> permissionCodes = SecurityContext.getCurrentPermissionCodes();
        assertNotNull("权限代码集合不应该为空", permissionCodes);
        assertTrue("应该包含检测执行权限", permissionCodes.contains("DETECTION_EXECUTE"));
        assertTrue("应该包含报告查看权限", permissionCodes.contains("REPORT_VIEW"));
    }
    
    @Test
    public void testSetAndGetSessionToken() {
        // 测试设置和获取会话令牌
        assertNull("初始状态下会话令牌应该为空", SecurityContext.getSessionToken());
        
        SecurityContext.setSessionToken(testSessionToken);
        
        String currentToken = SecurityContext.getSessionToken();
        assertNotNull("设置后会话令牌不应该为空", currentToken);
        assertEquals("会话令牌应该匹配", testSessionToken, currentToken);
    }
    
    @Test
    public void testHasPermission() {
        // 测试权限检查
        assertFalse("初始状态下不应该有任何权限", SecurityContext.hasPermission("DETECTION_EXECUTE"));
        
        SecurityContext.setCurrentPermissions(testPermissions);
        
        assertTrue("应该具有检测执行权限", SecurityContext.hasPermission("DETECTION_EXECUTE"));
        assertTrue("应该具有报告查看权限", SecurityContext.hasPermission("REPORT_VIEW"));
        assertFalse("不应该具有用户创建权限", SecurityContext.hasPermission("USER_CREATE"));
    }
    
    @Test
    public void testHasRole() {
        // 测试角色检查
        assertFalse("初始状态下不应该有任何角色", SecurityContext.hasRole("OPERATOR"));
        
        SecurityContext.setCurrentRoles(testRoles);
        
        assertTrue("应该具有操作员角色", SecurityContext.hasRole("OPERATOR"));
        assertTrue("应该具有审核员角色", SecurityContext.hasRole("REVIEWER"));
        assertFalse("不应该具有系统管理员角色", SecurityContext.hasRole("SYSTEM_ADMIN"));
    }
    
    @Test
    public void testHasRoleLevel() {
        // 测试角色级别检查
        assertFalse("初始状态下不应该满足任何级别要求", SecurityContext.hasRoleLevel(1));
        
        SecurityContext.setCurrentRoles(testRoles);
        
        assertTrue("应该满足级别1的要求", SecurityContext.hasRoleLevel(1));
        assertTrue("应该满足级别2的要求", SecurityContext.hasRoleLevel(2));
        assertFalse("不应该满足级别3的要求", SecurityContext.hasRoleLevel(3));
    }
    
    @Test
    public void testIsAuthenticated() {
        // 测试认证状态检查
        assertFalse("初始状态下不应该是已认证状态", SecurityContext.isAuthenticated());
        
        SecurityContext.setCurrentUser(testUser);
        
        assertTrue("设置用户后应该是已认证状态", SecurityContext.isAuthenticated());
    }
    
    @Test
    public void testClear() {
        // 测试清除安全上下文
        SecurityContext.setCurrentUser(testUser);
        SecurityContext.setCurrentRoles(testRoles);
        SecurityContext.setCurrentPermissions(testPermissions);
        SecurityContext.setSessionToken(testSessionToken);
        
        // 验证设置成功
        assertNotNull("清除前用户不应该为空", SecurityContext.getCurrentUser());
        assertNotNull("清除前角色不应该为空", SecurityContext.getCurrentRoles());
        assertNotNull("清除前权限不应该为空", SecurityContext.getCurrentPermissions());
        assertNotNull("清除前会话令牌不应该为空", SecurityContext.getSessionToken());
        
        SecurityContext.clear();
        
        // 验证清除成功
        assertNull("清除后用户应该为空", SecurityContext.getCurrentUser());
        assertNull("清除后角色应该为空", SecurityContext.getCurrentRoles());
        assertNull("清除后权限应该为空", SecurityContext.getCurrentPermissions());
        assertNull("清除后会话令牌应该为空", SecurityContext.getSessionToken());
        assertEquals("清除后最高角色级别应该为0", 0, SecurityContext.getCurrentMaxRoleLevel());
        assertFalse("清除后不应该是已认证状态", SecurityContext.isAuthenticated());
    }
    
    @Test
    public void testNullRolesHandling() {
        // 测试空角色列表的处理
        SecurityContext.setCurrentRoles(null);
        
        assertNull("设置空角色列表后角色应该为空", SecurityContext.getCurrentRoles());
        assertNull("设置空角色列表后角色代码集合应该为空", SecurityContext.getCurrentRoleCodes());
        assertEquals("设置空角色列表后最高角色级别应该为0", 0, SecurityContext.getCurrentMaxRoleLevel());
    }
    
    @Test
    public void testNullPermissionsHandling() {
        // 测试空权限列表的处理
        SecurityContext.setCurrentPermissions(null);
        
        assertNull("设置空权限列表后权限应该为空", SecurityContext.getCurrentPermissions());
        assertNull("设置空权限列表后权限代码集合应该为空", SecurityContext.getCurrentPermissionCodes());
    }
}
