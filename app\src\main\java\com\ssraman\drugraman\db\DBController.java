package com.ssraman.drugraman.db;

import android.content.Context;

import com.ssraman.drugraman.app.AppApplication;
import com.ssraman.drugraman.db.gen.DaoMaster;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.lib_common.constant.DbConst;

import org.greenrobot.greendao.database.Database;

/**
 * @author: Administrator
 * @date: 2021/6/20
 */
public class DBController {

    private static DaoMaster daoSysMaster;
    private static DaoMaster daoOperatorMaster;

    private static DaoSession daoSysSession;
    private static DaoSession daoOperatorSession;

    //数据库是否加密

    //public static boolean ENCRYPTED = true;

    //所有数据库入口 根据数据库名字
    private static DaoMaster obtainMaster(Context context, String dbName,boolean encrypted) {
        final boolean ENCRYPTED=encrypted;
        DaoMaster.DevOpenHelper devOpenHelper = new DaoMaster.DevOpenHelper(context, dbName, null);
        Database db = ENCRYPTED ? devOpenHelper.getEncryptedWritableDb("www.ss-raman.com") : devOpenHelper.getWritableDb();
        DaoMaster daoMaster = new DaoMaster(db);
        return daoMaster;
    }

    private static DaoMaster getSysDaoMaster(Context context, String dbName) {
        if (dbName == null)
            return null;
        if (daoSysMaster == null) {
            daoSysMaster = obtainMaster(context, dbName,true);
        }
        return daoSysMaster;
    }

    private static DaoMaster getOperatorDaoMaster(Context context, String dbName) {
        if (dbName == null)
            return null;
        if(daoOperatorMaster==null)
        {
            daoOperatorMaster = obtainMaster(context, dbName,false);
        }
        return daoOperatorMaster;
    }

    /**
     * 取得系统数据库DaoSession
     *
     * @return
     */
    public static DaoSession getSysDaoSession(String dbName) {
        if(daoSysSession==null) {
            daoSysSession = getSysDaoMaster(AppApplication.getInstance(), dbName).newSession();
        }
        return daoSysSession;
    }


    /**
     * 默认操作localdata登录用户数据库
     */
    public static DaoSession getDaoOperatorSession() {
        if(daoOperatorSession==null) {
            daoOperatorSession = getOperatorDaoMaster(AppApplication.getInstance(), DbConst.LOGIN_DATABASE_NAME).newSession();
        }
        return daoOperatorSession;
    }

}
