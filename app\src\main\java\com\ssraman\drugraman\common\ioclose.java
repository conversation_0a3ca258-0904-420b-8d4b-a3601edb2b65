package com.ssraman.drugraman.common;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * Created by Administrator on 2018/7/24.
 */

public class ioclose extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
      /*  String  iowritereply=intent.getExtras().getString("ramantimeflag");

        if (iowritereply.equals("false")){
            Log.d(TAG, "返回数据成功！！---false");
            SettingPre.setAccept("false");
        }*/
        //SettingPre.setAccept("false");
        if(intent.getAction().equals("RCLOSE")) {
            CommonParameter.BroadcastStatus = false;
            CommonParameter.OPENBroadcast = false;
            CommonParameter.BroadcastStatus2=1;
        }
    }
}
