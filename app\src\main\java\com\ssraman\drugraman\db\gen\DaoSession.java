package com.ssraman.drugraman.db.gen;

import java.util.Map;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.identityscope.IdentityScopeType;
import org.greenrobot.greendao.internal.DaoConfig;

import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;
import com.ssraman.drugraman.db.entity.CalibrationFtInfo;
import com.ssraman.drugraman.db.entity.FtInfo;
import com.ssraman.drugraman.db.entity.OperLogInfo;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.db.entity.SampleTypeInfo;
import com.ssraman.drugraman.db.entity.SpectralDataInfo;
import com.ssraman.drugraman.db.entity.User_info;

import com.ssraman.drugraman.db.gen.ArchiveRecordsInfoDao;
import com.ssraman.drugraman.db.gen.CalibrationFtInfoDao;
import com.ssraman.drugraman.db.gen.FtInfoDao;
import com.ssraman.drugraman.db.gen.OperLogInfoDao;
import com.ssraman.drugraman.db.gen.PeakInfoDao;
import com.ssraman.drugraman.db.gen.SampleTypeInfoDao;
import com.ssraman.drugraman.db.gen.SpectralDataInfoDao;
import com.ssraman.drugraman.db.gen.User_infoDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.

/**
 * {@inheritDoc}
 * 
 * @see org.greenrobot.greendao.AbstractDaoSession
 */
public class DaoSession extends AbstractDaoSession {

    private final DaoConfig archiveRecordsInfoDaoConfig;
    private final DaoConfig calibrationFtInfoDaoConfig;
    private final DaoConfig ftInfoDaoConfig;
    private final DaoConfig operLogInfoDaoConfig;
    private final DaoConfig peakInfoDaoConfig;
    private final DaoConfig sampleTypeInfoDaoConfig;
    private final DaoConfig spectralDataInfoDaoConfig;
    private final DaoConfig user_infoDaoConfig;

    private final ArchiveRecordsInfoDao archiveRecordsInfoDao;
    private final CalibrationFtInfoDao calibrationFtInfoDao;
    private final FtInfoDao ftInfoDao;
    private final OperLogInfoDao operLogInfoDao;
    private final PeakInfoDao peakInfoDao;
    private final SampleTypeInfoDao sampleTypeInfoDao;
    private final SpectralDataInfoDao spectralDataInfoDao;
    private final User_infoDao user_infoDao;

    public DaoSession(Database db, IdentityScopeType type, Map<Class<? extends AbstractDao<?, ?>>, DaoConfig>
            daoConfigMap) {
        super(db);

        archiveRecordsInfoDaoConfig = daoConfigMap.get(ArchiveRecordsInfoDao.class).clone();
        archiveRecordsInfoDaoConfig.initIdentityScope(type);

        calibrationFtInfoDaoConfig = daoConfigMap.get(CalibrationFtInfoDao.class).clone();
        calibrationFtInfoDaoConfig.initIdentityScope(type);

        ftInfoDaoConfig = daoConfigMap.get(FtInfoDao.class).clone();
        ftInfoDaoConfig.initIdentityScope(type);

        operLogInfoDaoConfig = daoConfigMap.get(OperLogInfoDao.class).clone();
        operLogInfoDaoConfig.initIdentityScope(type);

        peakInfoDaoConfig = daoConfigMap.get(PeakInfoDao.class).clone();
        peakInfoDaoConfig.initIdentityScope(type);

        sampleTypeInfoDaoConfig = daoConfigMap.get(SampleTypeInfoDao.class).clone();
        sampleTypeInfoDaoConfig.initIdentityScope(type);

        spectralDataInfoDaoConfig = daoConfigMap.get(SpectralDataInfoDao.class).clone();
        spectralDataInfoDaoConfig.initIdentityScope(type);

        user_infoDaoConfig = daoConfigMap.get(User_infoDao.class).clone();
        user_infoDaoConfig.initIdentityScope(type);

        archiveRecordsInfoDao = new ArchiveRecordsInfoDao(archiveRecordsInfoDaoConfig, this);
        calibrationFtInfoDao = new CalibrationFtInfoDao(calibrationFtInfoDaoConfig, this);
        ftInfoDao = new FtInfoDao(ftInfoDaoConfig, this);
        operLogInfoDao = new OperLogInfoDao(operLogInfoDaoConfig, this);
        peakInfoDao = new PeakInfoDao(peakInfoDaoConfig, this);
        sampleTypeInfoDao = new SampleTypeInfoDao(sampleTypeInfoDaoConfig, this);
        spectralDataInfoDao = new SpectralDataInfoDao(spectralDataInfoDaoConfig, this);
        user_infoDao = new User_infoDao(user_infoDaoConfig, this);

        registerDao(ArchiveRecordsInfo.class, archiveRecordsInfoDao);
        registerDao(CalibrationFtInfo.class, calibrationFtInfoDao);
        registerDao(FtInfo.class, ftInfoDao);
        registerDao(OperLogInfo.class, operLogInfoDao);
        registerDao(PeakInfo.class, peakInfoDao);
        registerDao(SampleTypeInfo.class, sampleTypeInfoDao);
        registerDao(SpectralDataInfo.class, spectralDataInfoDao);
        registerDao(User_info.class, user_infoDao);
    }
    
    public void clear() {
        archiveRecordsInfoDaoConfig.clearIdentityScope();
        calibrationFtInfoDaoConfig.clearIdentityScope();
        ftInfoDaoConfig.clearIdentityScope();
        operLogInfoDaoConfig.clearIdentityScope();
        peakInfoDaoConfig.clearIdentityScope();
        sampleTypeInfoDaoConfig.clearIdentityScope();
        spectralDataInfoDaoConfig.clearIdentityScope();
        user_infoDaoConfig.clearIdentityScope();
    }

    public ArchiveRecordsInfoDao getArchiveRecordsInfoDao() {
        return archiveRecordsInfoDao;
    }

    public CalibrationFtInfoDao getCalibrationFtInfoDao() {
        return calibrationFtInfoDao;
    }

    public FtInfoDao getFtInfoDao() {
        return ftInfoDao;
    }

    public OperLogInfoDao getOperLogInfoDao() {
        return operLogInfoDao;
    }

    public PeakInfoDao getPeakInfoDao() {
        return peakInfoDao;
    }

    public SampleTypeInfoDao getSampleTypeInfoDao() {
        return sampleTypeInfoDao;
    }

    public SpectralDataInfoDao getSpectralDataInfoDao() {
        return spectralDataInfoDao;
    }

    public User_infoDao getUser_infoDao() {
        return user_infoDao;
    }

}
