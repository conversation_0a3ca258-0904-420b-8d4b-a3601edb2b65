package com.ssraman.drugraman.sortableview;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;

import androidx.core.content.ContextCompat;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;

import de.codecrafters.tableview.SortableTableView;
import de.codecrafters.tableview.model.TableColumnWeightModel;
import de.codecrafters.tableview.toolkit.SimpleTableHeaderAdapter;
import de.codecrafters.tableview.toolkit.SortStateViewProviders;
import de.codecrafters.tableview.toolkit.TableDataRowBackgroundProviders;

/**
 * @author: Administrator
 * @date: 2021/7/9
 */
public class ArchiveRecordsSorTableView extends SortableTableView<ArchiveRecordsInfo> {
    public ArchiveRecordsSorTableView(Context context) {
        this(context, null);
    }

    public ArchiveRecordsSorTableView(Context context, AttributeSet attributes) {
        this(context, attributes, android.R.attr.listViewStyle);
    }

    public ArchiveRecordsSorTableView(Context context, AttributeSet attributes, int styleAttributes) {
        super(context, attributes, styleAttributes);

        final SimpleTableHeaderAdapter simpleTableHeaderAdapter = new SimpleTableHeaderAdapter(context, R.string.str_select, R.string.str_No, R.string.str_batch_name, R.string.str_verification_name, R.string.str_date,R.string.str_result);
        simpleTableHeaderAdapter.setTextColor(ContextCompat.getColor(context, R.color.table_header_text2));
        simpleTableHeaderAdapter.setTextSize(14);
        simpleTableHeaderAdapter.setPaddings(8,5,2,5);
        simpleTableHeaderAdapter.setGravity(Gravity.CENTER);
        setHeaderAdapter(simpleTableHeaderAdapter);

        //每一个item颜色差异化的操作
        final int rowColorEven = ContextCompat.getColor(context, R.color.table_data_row_even2);
        final int rowColorOdd = ContextCompat.getColor(context, R.color.table_data_row_odd2);
        final int rowColorSelected = ContextCompat.getColor(context, R.color.table_data_row_selected2);
        setDataRowBackgroundProvider(TableDataRowBackgroundProviders.alternatingRowColors(rowColorEven, rowColorOdd,rowColorSelected));
        final int cowHeaderColor = ContextCompat.getColor(context, R.color.table_data_cow_header2);
        setHeaderBackgroundColor(cowHeaderColor);
        setHeaderSortStateViewProvider(SortStateViewProviders.brightArrows());

        final TableColumnWeightModel tableColumnWeightModel = new TableColumnWeightModel(6);
        tableColumnWeightModel.setColumnWeight(0, 1);
        tableColumnWeightModel.setColumnWeight(1, 1);
        tableColumnWeightModel.setColumnWeight(2, 2);
        tableColumnWeightModel.setColumnWeight(3, 2);
        tableColumnWeightModel.setColumnWeight(4, 2);
        tableColumnWeightModel.setColumnWeight(5, 2);
        setColumnModel(tableColumnWeightModel);
    }
}
