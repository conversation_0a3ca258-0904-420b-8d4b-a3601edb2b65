package com.ssraman.drugraman.rbac.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 角色验证注解
 * 用于方法级别的角色控制
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireRole {
    
    /**
     * 角色代码
     * 可以指定多个角色代码
     */
    String[] value() default {};
    
    /**
     * 角色代码（别名）
     */
    String[] roles() default {};
    
    /**
     * 最低角色级别
     */
    int minLevel() default 0;
    
    /**
     * 逻辑关系
     * AND: 需要所有角色
     * OR: 需要任一角色
     */
    LogicalOperator logical() default LogicalOperator.OR;
    
    /**
     * 错误消息
     */
    String message() default "角色权限不足，无法访问该功能";
    
    /**
     * 逻辑操作符枚举
     */
    enum LogicalOperator {
        AND, OR
    }
}
