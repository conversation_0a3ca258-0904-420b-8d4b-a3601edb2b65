//#include <stdlib.h>
#include <string.h>
#include <malloc.h>
#include <list> 
#include <algorithm>   //sort
#include "basicfuc.h"
#include <iostream>
using namespace std;
//ʹ��nm -D libbasicfuncs.so��鵼�����ű�
//D:\specdlls\android\basicfuncs\x86\Release
//��Ҫ��C++������ĩβ����-fvisibility=hidden�������ڲ����� 
//����ͨ����ʱ�򣬿�����û�м�math�⣬��Ҫ����������������-lm����


/***************************************
��������seePeaks_calib
���أ�һ�鱻Ѱ����������
������spec  ԭʼ����
������SNR   ����ˮƽ
������inttime_ms����ʱ��
������innerData Ԥ������
˵����Ѱ�壨������ߵ�14�������壩����û��������������ʱ�����ؿ�ָ�룬��Ҫ���ǰ���������
�޶�������
***************************************/
int seePeaks_local(SPEC* spec, int boxwidth, PEAKS2* result) {
	int* peaksidx = new int[spec->len];
	double* peakshight = new double[spec->len];
	int cnt = 0;


	for (int i = boxwidth; i < spec->len - boxwidth; i++) {
		//������Ϊ�ٽ�3���е����ֵ������¼
		if (spec->dIntensity[i] >= spec->dIntensity[i - 1] && spec->dIntensity[i] >= spec->dIntensity[i + 1] && spec->dIntensity[i] >50) {
			peaksidx[cnt] = i;
			peakshight[cnt++] = spec->dIntensity[i];
		}
	}
	//�����boxwidth��Ϊ����򱻼�¼
	double localmax = peakshight[0];
	int localmaxidx = peaksidx[0];
	result->num = 0;


	for (int i = 1; i < cnt; i++) {
		if (peaksidx[i] - localmaxidx > boxwidth) {//�Ѿ��ҵ���һ���壬��¼

												   //��¼������
			result->peaks_hight[result->num] = localmax;
			result->peaks_wcnt[result->num] = spec->dWcnt[localmaxidx];
			//�˲���Ϊ��
			result->peaks_width[result->num++] = 15;

			//����
			localmax = peakshight[i];
			localmaxidx = peaksidx[i];
		}
		else if (peakshight[i] > localmax) {
			//����
			localmax = peakshight[i];
			localmaxidx = peaksidx[i];
		}
	}
	double min = 0;
	for (int i = 0; i < result->num; i++)
		min += result->peaks_hight[i];
	//ȥ������ƽ��ֵ�ķ�
	min = min / result->num;
	for (int i = result->num - 1; i >= 0; i--) {
		if (result->peaks_hight[i] < min) {
			for (int j = i; j < result->num - 1; j++) {
				result->peaks_hight[j] = result->peaks_hight[j + 1];
				result->peaks_wcnt[j] = result->peaks_wcnt[j + 1];
			}
			result->num -= 1;
		}
	}
	delete[]peaksidx;
	delete[]peakshight;
	return 1;
}
/***************************************
��������__getPeakHight
���أ��������������߶Ⱥ��Ҳ�߶�
������peak  ԭʼ����
������minV   ����[0]Ϊ��[1]ΪС���������ҽ�����
������Id ���Ĳ���
������nNum �������ݳ���
������int hw ���
˵����Ѱ������е��õĺ���
�޶�������
***************************************/
void __getPeakHight(double* peak, double* minV, int Id, int nNum, int hw/*���*/) {
	int f, i;
	double minVL = peak[Id], minVR = peak[Id];
	//double* minV;

	f = (Id - hw) > 0 ? Id - hw : 0;
	for (i = f; i < Id; i++) {
		minVL = minVL < peak[i] ? minVL : peak[i];
		if (i + hw < nNum) {
			minVR = minVR < peak[i + hw] ? minVR : peak[i + hw];

		}

	}

	minVL = peak[Id] - minVL;
	minVR = peak[Id] - minVR;

	//minV =(double*)malloc(sizeof(double)*2);
	minV[0] = (minVL > minVR) ? minVL : minVR;
	minV[1] = (minVL < minVR) ? minVL : minVR;

	return;
}
/***************************************
��������seePeaks_idx
���أ�һ�鱻Ѱ����������
������spec  ԭʼ����
������SNR   ����ˮƽ
������inttime_ms����ʱ��
������innerData Ԥ������
˵����Ѱ�壨������ߵ�14�������壩����û��������������ʱ�����ؿ�ָ�룬��Ҫ���ǰ���������
�޶�������
***************************************/
int seePeaks_idx(double* y, double SNR, int len, int* peak_idx, int peaks_size,int preserve) {
	int* pId = (int*)malloc(sizeof(int)*len);
	int* pId2 = (int*)malloc(sizeof(int)*peaks_size);
	double tempV[2];//=NULL;
	double noiseV;
	double maxV = 0;
	int maxID = 0, i, j, k;
	int newCnt = 0, leftId = 0;
	int greenLight = 0;
	double datamax = 0, datasum = 0;
	int cnt;
	int hw_peak_noise = 50;
	int hw_peak_hight = 30;


	if (!decodeSoft(y, len) && preserve != 3)
	{
		free(pId);
		return 0;
	}

	pId[0] = 0;
	pId[len - 1] = 1;

	for (i = 1; i < len - 1; i++) {
		datasum += y[i];
		if (y[i] > datamax) {
			datamax = y[i];
		}
		if (y[i] > y[i - 1])//�������
		{
			if (y[i] > y[i + 1])
				pId[i] = 4;
			else if (y[i] == y[i + 1])
				pId[i] = 1;//��߽�
			else
				pId[i] = 0;
		}
		else if (y[i] == y[i - 1])//�����ƽ��
		{
			if (y[i] == y[i + 1])
				pId[i] = 3; //��䲿��
			else if (y[i] > y[i + 1])
				pId[i] = 2; //�ұ߽�
			else
				pId[i] = 0;
		}
		else //�������
			pId[i] = 0;
		if (maxV < y[i]) {
			maxV = y[i];
			maxID = i;
		}

	}

	__getPeakHight(y, tempV, maxID, len, hw_peak_noise);
	noiseV = tempV[0] / SNR;
	//������ƽ��


	for (i = 1; i < len - 1; i++) {
		switch (pId[i]) {
		case 4: {
			__getPeakHight(y, tempV, i, len, hw_peak_hight);
			if (tempV[0] > noiseV && tempV[1] * 3 > noiseV) {
				pId[newCnt] = i;
				newCnt += 1;
			}
			if (greenLight)
				greenLight = 0;
			break;
		}
		case 1://�����ǰ����߽翪��
			greenLight = 1;
			leftId = i;
			break;
		case 2://�ұ߽�ص�
			__getPeakHight(y, tempV, i, len, hw_peak_hight);
			if (greenLight && tempV[0] > noiseV && tempV[1] * 3 > noiseV)//�з�ƽ��
			{
				pId[newCnt] = ((i + leftId) >> 1);
				newCnt += 1;
			}
			greenLight = 0;
			break;
		}
	}
	//�ж��Ƿ�Ϊ�ղ�
	//����źŷ���ź�ƽ��ǿ�ȸ�3��
	// ���ݲ��õĿղ�ʱ���ź�ǿ��2500
	//inttime_ms=inttime_ms / 1000;
	//inttime_ms= inttime_ms * 667 + 1834;
	//if ((int)datamax < inttime_ms) {
	//    peaks->num=0;
	//    peaks->peaks=NULL;
	//    free(pId);
	//    return peaks;
	//}

	if (newCnt >peaks_size) {
		//����ѡ����ߵ�15������
		peak_idx[0] = 0;//(spec.dIntensity[pId[peak_idx[0]]]>spec.dIntensity[pId[peak_idx[1]]]) ?0:1;
						 //peak_idx[1] =(peak_idx[0]==0)?1:0;
		cnt = 0;
		for (i = 1; i < newCnt; i++) {//Ͷι����������
			for (k = (i < peaks_size) ? i : peaks_size/*��*/; k > 0; k--) {
				/*if ((k == i ||k==14 )&& spec.dIntensity[pId[peak_idx[k]]] >=spec.dIntensity[pId[i]]) {
				continue;
				} else {*/
				if (i == newCnt - 1)
					i = newCnt - 1;
				if (y[pId[peak_idx[k - 1]]] < y[pId[i]]) {
					peak_idx[k] = peak_idx[k - 1];
					if (k == 1) {
						peak_idx[k - 1] = i;
						cnt++;
					}
				}
				else {
					//if(y[pId[peak_idx[k]]] < y[pId[i]] ) {
					peak_idx[k] = i;
					cnt++;
					k = 0;
					// }
				}
				// }

			}
		}
		k = 0;//���ս��λ��
		maxV = y[pId[peak_idx[k]]];
		//���źűȽ�����ʱ�򣬿��ܳ���һЩ�ӷ壬�����ܷ�������Щ����Ʒû�����
		/*if (newCnt > 60) {
		int[] x = new int[0];
		return x;
		}*/
		//�ж��Ƿ�Ϊ�ղ�
		//����źŷ���ź�ƽ��ǿ�ȸ�3��
		//�����ղ�Ŀ��
		for (i = 0; i < newCnt; i++) {
			for (j = 0; j < peaks_size - k; j++) {//�Ƚ�15���������
				if (peak_idx[j] == i) {//�ҵ�������(peak_idx��˳������ν)
					pId2[k] = pId[i]; //�����ձ���ѹջ
					peak_idx[j] = peak_idx[peaks_size-1 - k];//����
					k++;
					if (k == peaks_size) //���ҵ�����������
						i = newCnt;
					break;
				}
			}

		}
		newCnt = peaks_size;
	}
	else
	{
		memcpy(pId2,pId, newCnt * sizeof(int));
	}

	free(pId);
	peak_idx[0] = newCnt;
	memcpy(&peak_idx[1], pId2, newCnt * sizeof(int));
	free(pId2);
	return newCnt;
}
/***************************************
��������seePeaks
���أ�һ�鱻Ѱ����������
������spec  ԭʼ����
������SNR   ����ˮƽ
������inttime_ms����ʱ��
������innerData Ԥ������
˵����Ѱ�壨������ߵ�14�������壩����û��������������ʱ�����ؿ�ָ�룬��Ҫ���ǰ���������
�޶�������
***************************************/
int seePeaks(SPEC* spec, double SNR, int inttime_ms, PEAKS2* result,int preserve) {
	int* pId = (int*)malloc(sizeof(int)*spec->len);

	//double minV[2];
	double tempV[2];//=NULL;
	double noiseV;
	double maxV = 0;
	int maxID = 0, i, j, k;
	int newCnt = 0, leftId = 0;
	int greenLight = 0;
	double datamax = 0, datasum = 0;
	int max15list[15];//����������pId�е�λ��
	int pId2[14];//˳����pId[i]��ͬ
	int cnt;


	if (!decodeSoft(spec->dIntensity, spec->len) && preserve!=3)
	{
		free(pId);
		return 0;
	}

	pId[0] = 0;
	pId[spec->len - 1] = 1;



	for (i = 1; i < spec->len - 1; i++) {
		datasum += spec->dIntensity[i];
		if (spec->dIntensity[i] > datamax) {
			datamax = spec->dIntensity[i];
		}
		if (spec->dIntensity[i] > spec->dIntensity[i - 1])//�������
		{
			if (spec->dIntensity[i] > spec->dIntensity[i + 1])
				pId[i] = 4;
			else if (spec->dIntensity[i] == spec->dIntensity[i + 1])
				pId[i] = 1;//��߽�
			else
				pId[i] = 0;
		}
		else if (spec->dIntensity[i] == spec->dIntensity[i - 1])//�����ƽ��
		{
			if (spec->dIntensity[i] == spec->dIntensity[i + 1])
				pId[i] = 3; //��䲿��
			else if (spec->dIntensity[i] > spec->dIntensity[i + 1])
				pId[i] = 2; //�ұ߽�
			else
				pId[i] = 0;
		}
		else //�������
			pId[i] = 0;
		if (maxV < spec->dIntensity[i]) {
			maxV = spec->dIntensity[i];
			maxID = i;
		}

	}

	__getPeakHight(spec->dIntensity, tempV, maxID, spec->len, 50);
	noiseV = tempV[0] / SNR;
	//������ƽ��


	for (i = 1; i < spec->len - 1; i++) {
		switch (pId[i]) {
		case 4: {
			__getPeakHight(spec->dIntensity, tempV, i, spec->len, 30);
			if (tempV[0] > noiseV && tempV[1] * 3 > noiseV || tempV[0]>noiseV*10) {
				pId[newCnt] = i;
				newCnt += 1;
			}
			if (greenLight)
				greenLight = 0;
			break;
		}
		case 1://�����ǰ����߽翪��
			greenLight = 1;
			leftId = i;
			break;
		case 2://�ұ߽�ص�
			__getPeakHight(spec->dIntensity, tempV, i, spec->len, 30);
			if (greenLight && tempV[0] > noiseV && tempV[1] * 3 > noiseV)//�з�ƽ��
			{
				pId[newCnt] = ((i + leftId) >> 1);
				newCnt += 1;
			}
			greenLight = 0;
			break;
		}
	}
	//�ٽ�������ϲ�11/3
	//删除距离比较近的峰
	//20210311删除毛刺峰
	for (int i = 0; i < newCnt; ) 
	{
		int k = 1;
		while (true)
		{
			if (i + k >= newCnt)
			{
				i = i + k;
				break;
			}
			//20210311-------------------
			//搜索峰左右两个局部最小
			bool thron_key=true;
			//峰至少宽13个波数
			for (int j=0;j<6;j++){

				if (spec->dIntensity[pId[i]+j]<spec->dIntensity[pId[i]+j+ 1] || pId[i]+j+ 1<spec->len)
				{
					thron_key=false;
				}
				if (spec->dIntensity[pId[i ]- j]<spec->dIntensity[pId[i ]-j-1] || pId[i]-j-1>=0)
				{
					thron_key=false;
				}

			}
			if (thron_key==false) {
				if (spec->dWcnt[pId[i + k]] - spec->dWcnt[pId[i]] < 15) {//与下一个峰的距离不足15cm-1
					if (spec->dIntensity[pId[i + k]] > spec->dIntensity[pId[i]]) {//后面的峰更强
						//spec->dIntensity[pId[i]] = 0;
						pId[i] = -1;
						i = i + k;
						k=1;
						//break;
						//k++;
					} else {
						//spec->dIntensity[pId[i + k]] = 0;
						pId[i + k] = -1;
						k++;
					}

				} else {
					i += k;
					break;
				}
			}else{
				pId[i]=-1;
				i = i + k;
				k=1;
			}
		}
			
	}
	for (i = 0; i < newCnt; i++) {
		if (pId[i] == -1) {
			for (k = i; k < newCnt - 1; k++) {
				pId[k] = pId[k + 1];

			}
			newCnt -= 1;
			i--;
		}
	}
	//�ж��Ƿ�Ϊ�ղ�
	//����źŷ���ź�ƽ��ǿ�ȸ�3��
	// ���ݲ��õĿղ�ʱ���ź�ǿ��2500
	//inttime_ms=inttime_ms / 1000;
	//inttime_ms= inttime_ms * 667 + 1834;
	//if ((int)datamax < inttime_ms) {
	//    peaks->num=0;
	//    peaks->peaks=NULL;
	//    free(pId);
	//    return peaks;
	//}


	if (newCnt >14) {
		//����ѡ����ߵ�15������
		max15list[0] = 0;//(spec.dIntensity[pId[max15list[0]]]>spec.dIntensity[pId[max15list[1]]]) ?0:1;
						 //max15list[1] =(max15list[0]==0)?1:0;
		cnt = 0;
		for (i = 1; i < newCnt; i++) {//Ͷι����������
			
			for (k = (i < 14) ? i : 14/*��*/; k > 0; k--) {
				/*if ((k == i ||k==14 )&& spec.dIntensity[pId[max15list[k]]] >=spec.dIntensity[pId[i]]) {
				continue;
				} else {*/
				/*if (i == newCnt - 1)
					i = newCnt - 1;*/
				if (spec->dIntensity[pId[max15list[k - 1]]] < spec->dIntensity[pId[i]]) {
					max15list[k] = max15list[k - 1];
					if (k == 1) {
						max15list[k - 1] = i;
						cnt++;
					}
				}
				else {
					//if(spec->dIntensity[pId[max15list[k]]] < spec->dIntensity[pId[i]] ) {
					max15list[k] = i;
					cnt++;
					k = 0;
					// }
				}
				// }

			}
		}
		k = 0;//���ս��λ��
		maxV = spec->dIntensity[pId[max15list[k]]];
		//���źűȽ�����ʱ�򣬿��ܳ���һЩ�ӷ壬�����ܷ�������Щ����Ʒû�����
		/*if (newCnt > 60) {
		int[] x = new int[0];
		return x;
		}*/
		//�ж��Ƿ�Ϊ�ղ�
		//����źŷ���ź�ƽ��ǿ�ȸ�3��
		//�����ղ�Ŀ��
		for (i = 0; i < newCnt; i++) {
			for (j = 0; j < 14 - k; j++) {//�Ƚ�15���������
				if (max15list[j] == i) {//�ҵ�������(max15list��˳������ν)
					pId2[k] = pId[i]; //�����ձ���ѹջ
					max15list[j] = max15list[13 - k];//����
					k++;
					if (k == 14) //���ҵ�����������
						i = newCnt;
					break;
				}
			}

		}
		newCnt = 14;
	}
	else
	{
		for (i = 0; i < newCnt; i++)
		{
			pId2[i] = pId[i]; //�����ձ���ѹջ
		}
	}
	free(pId);
	if (newCnt>0) {
		result->num = newCnt;
		for (i = 0; i<newCnt; i++) {
			if (spec->dIntensity[pId2[i]] * SNR < spec->dIntensity[maxID])
			{
				result->num = i + 1;
				break;
			}
			result->peaks_wcnt[i] = spec->dWcnt[pId2[i]];
			result->peaks_hight[i] = spec->dIntensity[pId2[i]];
			//result->peaks_width[i] = 15;
			//cout << result->peaks_wcnt[i]<< result->peaks_hight[i] << endl;
		}
	}
	else {
		result->num = 0;
	}
	return 1;
}
/***************************************
��������calibration
���أ�ƫ��rsd
������acetonitrile  �������
������innerData Ԥ������
˵�������ڽ�ԭʼ���׸��ݶ���������׽��зֽ⣬������һ����������
�޶���
***************************************/
void calibration(double * stdpeaks, int size, double * coff, double* pixels) {
	//double x[5]={0,0,0,0,0},y[5]={379.435,920.7518,1374.652,2295.435,2946.435};//����������λ��;
	double *x = new double[size];
	double *y = new double[size];
	int j = 0;
	for (int i = 0; i < size; i++) {
		if (pixels[i] > 0) {
			y[j] = stdpeaks[i];
			x[j] = pixels[i];
			j++;
		}
	}
	CALICOEF result;
	result.pCoef = coff;
	polyfit(j, x, y, 3, &result);
	delete[]x;
	delete[]y;
	return;
}

/***************************************
��������makeshortSpec
���أ� �޳������б����󷵻صĹ���
������ spec ����
������innerData Ԥ������
˵�����������ɹ��ף��������ݸ���������ʾ�����ǲ��ܶ�ʧ����Ϣ
�޶���
***************************************/
int makeshortSpec(SPEC* spec, double* peaks, int peaks_num, SPEC* spec_s) {

	int i = 0, j = 0, k = 0, cnt = 0;
	double max = 0, left, right, tem;
	int len = spec->len / 3;

	double* x = spec_s->dWcnt;
	double* y = spec_s->dIntensity;
	spec_s->len = len;


	for (i = 0; i < len; i++) {
		x[i] = 0;
		y[i] = 0;
	}

	/**
	*������λ�ñ����ĳ�������
	*/

	for (i = 0; i < len; i++) {
		left = spec->dWcnt[i * 3];
		right = spec->dWcnt[i * 3 + 2];
		for (j = 0; j < peaks_num; j++) {
			//Logger.d(i+','+j);
			//��ֵ�㱣֤����
			if (peaks[j] >= left && peaks[j] <= right) {
				x[i] = peaks[j];
				left = spec->dIntensity[i * 3 + 1];
				right = spec->dIntensity[i * 3 + 2];
				y[i] = left > right ? left : right;
				y[i] = y[i] > spec->dIntensity[i * 3] ? y[i] : spec->dIntensity[i * 3];
				break;
			}
		}
		/* if (x[i] == 0) {//�������ص�ƽ��
		x[i] = (left + right + spec.dWcnt[i * 3]) / 3;
		left = spec.dIntensity[ i * 3 + 1];
		right = spec.dIntensity[ i * 3 + 2];
		y[i] = (left + right + spec.dIntensity[ i * 3]) / 3;
		}*/
		if (x[i] == 0) {//�������ص�ѡ��Сֵ����֤���Կ�����Сֵ
			y[i] = spec->dIntensity[i * 3];
			cnt = 0;
			for (k = 1; k < 3; k++) {
				tem = spec->dIntensity[i * 3 + k];
				if (tem < y[i]) {
					cnt = k;
					y[i] = tem;
				}
			}
			x[i] = spec->dWcnt[i * 3 + cnt];
		}
		max = max > y[i] ? max : y[i];
	}
	//��һ��
	/*if (peaks->num > 0) {
	for (i = 0; i < len; i++)
	y[i] = y[i] / max * 10000;
	}*/

	return 1;

}

//int getPos(SPEC* spec,double dWcnt){
//    int i;
//    for (i = 1; i < spec->len; i++)
//        {if(spec->dWcnt[i]>dWcnt)
//            {
//            if(spec->dWcnt[i]-dWcnt>dWcnt-spec->dWcnt[i-1]){
//                return i-1;
//                }
//            else
//                { return i;
//
//                }
//            }
//
//        }
//	return 0;
//}
////����������ֵλ��
////�Ѳ���
//double getval(SPEC* spec,int nidx){
//    return spec->dIntensity[nidx];
//}
//���ݲ���λ��ȷ��idx
int getPos(double * wCnts, int len, double dWcnt) {
	int i = 0;
	while (i<len && wCnts[i]<dWcnt)
		i++;
	if (wCnts[i] - dWcnt>dWcnt - wCnts[i - 1])
		return i - 1;
	else
		return i;
}


//�Ѳ���
int getMaxPos(SPEC* spec, double dWcnt, double width) {
	int i;
	int maxid = 0;
	for (i = 1; i < spec->len; i++)
	{
		if (spec->dWcnt[i]>dWcnt + width) {
			return maxid;
		}
		if (spec->dWcnt[i]<dWcnt - width) {
			maxid = i;
			continue;
		}
		if (spec->dIntensity[i]>spec->dIntensity[maxid])
		{
			maxid = i;
		}

	}
	return maxid;
}

//���ܲ���
int key(BACKUP* backup) {
	return 1;
}
//���


//���