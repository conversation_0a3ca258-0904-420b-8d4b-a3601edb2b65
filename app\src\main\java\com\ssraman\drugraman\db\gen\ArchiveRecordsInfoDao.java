package com.ssraman.drugraman.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.ssraman.drugraman.db.StringDateConverter;
import java.util.Date;

import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "ArchiveRecordsTable".
*/
public class ArchiveRecordsInfoDao extends AbstractDao<ArchiveRecordsInfo, Long> {

    public static final String TABLENAME = "ArchiveRecordsTable";

    /**
     * Properties of entity ArchiveRecordsInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "Id", true, "Id");
        public final static Property Material = new Property(1, String.class, "Material", false, "Material");
        public final static Property SampleName = new Property(2, String.class, "SampleName", false, "SampleName");
        public final static Property SampleMorphology = new Property(3, String.class, "SampleMorphology", false, "SampleMorphology");
        public final static Property SeriesNumber = new Property(4, String.class, "SeriesNumber", false, "SeriesNumber");
        public final static Property Batch = new Property(5, Integer.class, "Batch", false, "Batch");
        public final static Property Container = new Property(6, String.class, "Container", false, "Container");
        public final static Property InstrumentModel = new Property(7, String.class, "InstrumentModel", false, "InstrumentModel");
        public final static Property SoftwareVersion = new Property(8, String.class, "SoftwareVersion", false, "SoftwareVersion");
        public final static Property EvaluationMode = new Property(9, Integer.class, "EvaluationMode", false, "EvaluationMode");
        public final static Property Description = new Property(10, String.class, "Description", false, "Description");
        public final static Property DetectionResult = new Property(11, Integer.class, "DetectionResult", false, "DetectionResult");
        public final static Property DetectionMan = new Property(12, String.class, "DetectionMan", false, "DetectionMan");
        public final static Property CoreMethod = new Property(13, Integer.class, "CoreMethod", false, "CoreMethod");
        public final static Property Confidence = new Property(14, double.class, "Confidence", false, "Confidence");
        public final static Property SpecId = new Property(15, Long.class, "SpecId", false, "SpecId");
        public final static Property DetectionTime = new Property(16, String.class, "DetectionTime", false, "DetectionTime");
        public final static Property MatchReport = new Property(17, byte[].class, "MatchReport", false, "MatchReport");
        public final static Property CompoundName = new Property(18, String.class, "CompoundName", false, "CompoundName");
        public final static Property Correlation = new Property(19, String.class, "Correlation", false, "Correlation");
        public final static Property IsPost = new Property(20, Integer.class, "IsPost", false, "IsPost");
        public final static Property IsMQTT = new Property(21, Integer.class, "IsMQTT", false, "IsMQTT");
        public final static Property IsBatch = new Property(22, Integer.class, "IsBatch", false, "IsBatch");
        public final static Property BatchName = new Property(23, String.class, "BatchName", false, "BatchName");
        public final static Property SampleContainer = new Property(24, String.class, "SampleContainer", false, "SampleContainer");
        public final static Property SampleNumber = new Property(25, Integer.class, "SampleNumber", false, "SampleNumber");
        public final static Property Longitude = new Property(26, Double.class, "longitude", false, "Longitude");
        public final static Property Latitude = new Property(27, Double.class, "latitude", false, "Latitude");
        public final static Property Inspector = new Property(28, String.class, "Inspector", false, "Publisher");
        public final static Property InspectorTime = new Property(29, String.class, "InspectorTime", false, "PublisherTime");
        public final static Property InspectorPass = new Property(30, Integer.class, "InspectorPass", false, "PublisherPass");
        public final static Property PublisherMacAddress = new Property(31, String.class, "PublisherMacAddress", false, "PublisherMacAddress");
        public final static Property Reviewer = new Property(32, String.class, "Reviewer", false, "Reviewer");
        public final static Property ReviewerTime = new Property(33, String.class, "ReviewerTime", false, "ReviewerTime");
        public final static Property ReviewerPass = new Property(34, Integer.class, "ReviewerPass", false, "ReviewerPass");
        public final static Property ReviewerMacAddress = new Property(35, String.class, "ReviewerMacAddress", false, "ReviewerMacAddress");
        public final static Property CertificationResult = new Property(36, Integer.class, "CertificationResult", false, "CertificationResult");
        public final static Property RecordSource = new Property(37, String.class, "RecordSource", false, "RecordSource");
        public final static Property Reserve1 = new Property(38, byte[].class, "Reserve1", false, "Reserve");
    }

    private final StringDateConverter DetectionTimeConverter = new StringDateConverter();
    private final StringDateConverter InspectorTimeConverter = new StringDateConverter();
    private final StringDateConverter ReviewerTimeConverter = new StringDateConverter();

    public ArchiveRecordsInfoDao(DaoConfig config) {
        super(config);
    }
    
    public ArchiveRecordsInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, ArchiveRecordsInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        String Material = entity.getMaterial();
        if (Material != null) {
            stmt.bindString(2, Material);
        }
 
        String SampleName = entity.getSampleName();
        if (SampleName != null) {
            stmt.bindString(3, SampleName);
        }
 
        String SampleMorphology = entity.getSampleMorphology();
        if (SampleMorphology != null) {
            stmt.bindString(4, SampleMorphology);
        }
 
        String SeriesNumber = entity.getSeriesNumber();
        if (SeriesNumber != null) {
            stmt.bindString(5, SeriesNumber);
        }
 
        Integer Batch = entity.getBatch();
        if (Batch != null) {
            stmt.bindLong(6, Batch);
        }
 
        String Container = entity.getContainer();
        if (Container != null) {
            stmt.bindString(7, Container);
        }
 
        String InstrumentModel = entity.getInstrumentModel();
        if (InstrumentModel != null) {
            stmt.bindString(8, InstrumentModel);
        }
 
        String SoftwareVersion = entity.getSoftwareVersion();
        if (SoftwareVersion != null) {
            stmt.bindString(9, SoftwareVersion);
        }
 
        Integer EvaluationMode = entity.getEvaluationMode();
        if (EvaluationMode != null) {
            stmt.bindLong(10, EvaluationMode);
        }
 
        String Description = entity.getDescription();
        if (Description != null) {
            stmt.bindString(11, Description);
        }
 
        Integer DetectionResult = entity.getDetectionResult();
        if (DetectionResult != null) {
            stmt.bindLong(12, DetectionResult);
        }
 
        String DetectionMan = entity.getDetectionMan();
        if (DetectionMan != null) {
            stmt.bindString(13, DetectionMan);
        }
 
        Integer CoreMethod = entity.getCoreMethod();
        if (CoreMethod != null) {
            stmt.bindLong(14, CoreMethod);
        }
        stmt.bindDouble(15, entity.getConfidence());
 
        Long SpecId = entity.getSpecId();
        if (SpecId != null) {
            stmt.bindLong(16, SpecId);
        }
 
        Date DetectionTime = entity.getDetectionTime();
        if (DetectionTime != null) {
            stmt.bindString(17, DetectionTimeConverter.convertToDatabaseValue(DetectionTime));
        }
 
        byte[] MatchReport = entity.getMatchReport();
        if (MatchReport != null) {
            stmt.bindBlob(18, MatchReport);
        }
 
        String CompoundName = entity.getCompoundName();
        if (CompoundName != null) {
            stmt.bindString(19, CompoundName);
        }
 
        String Correlation = entity.getCorrelation();
        if (Correlation != null) {
            stmt.bindString(20, Correlation);
        }
 
        Integer IsPost = entity.getIsPost();
        if (IsPost != null) {
            stmt.bindLong(21, IsPost);
        }
 
        Integer IsMQTT = entity.getIsMQTT();
        if (IsMQTT != null) {
            stmt.bindLong(22, IsMQTT);
        }
 
        Integer IsBatch = entity.getIsBatch();
        if (IsBatch != null) {
            stmt.bindLong(23, IsBatch);
        }
 
        String BatchName = entity.getBatchName();
        if (BatchName != null) {
            stmt.bindString(24, BatchName);
        }
 
        String SampleContainer = entity.getSampleContainer();
        if (SampleContainer != null) {
            stmt.bindString(25, SampleContainer);
        }
 
        Integer SampleNumber = entity.getSampleNumber();
        if (SampleNumber != null) {
            stmt.bindLong(26, SampleNumber);
        }
 
        Double longitude = entity.getLongitude();
        if (longitude != null) {
            stmt.bindDouble(27, longitude);
        }
 
        Double latitude = entity.getLatitude();
        if (latitude != null) {
            stmt.bindDouble(28, latitude);
        }
 
        String Inspector = entity.getInspector();
        if (Inspector != null) {
            stmt.bindString(29, Inspector);
        }
 
        Date InspectorTime = entity.getInspectorTime();
        if (InspectorTime != null) {
            stmt.bindString(30, InspectorTimeConverter.convertToDatabaseValue(InspectorTime));
        }
 
        Integer InspectorPass = entity.getInspectorPass();
        if (InspectorPass != null) {
            stmt.bindLong(31, InspectorPass);
        }
 
        String PublisherMacAddress = entity.getPublisherMacAddress();
        if (PublisherMacAddress != null) {
            stmt.bindString(32, PublisherMacAddress);
        }
 
        String Reviewer = entity.getReviewer();
        if (Reviewer != null) {
            stmt.bindString(33, Reviewer);
        }
 
        Date ReviewerTime = entity.getReviewerTime();
        if (ReviewerTime != null) {
            stmt.bindString(34, ReviewerTimeConverter.convertToDatabaseValue(ReviewerTime));
        }
 
        Integer ReviewerPass = entity.getReviewerPass();
        if (ReviewerPass != null) {
            stmt.bindLong(35, ReviewerPass);
        }
 
        String ReviewerMacAddress = entity.getReviewerMacAddress();
        if (ReviewerMacAddress != null) {
            stmt.bindString(36, ReviewerMacAddress);
        }
 
        Integer CertificationResult = entity.getCertificationResult();
        if (CertificationResult != null) {
            stmt.bindLong(37, CertificationResult);
        }
 
        String RecordSource = entity.getRecordSource();
        if (RecordSource != null) {
            stmt.bindString(38, RecordSource);
        }
 
        byte[] Reserve1 = entity.getReserve1();
        if (Reserve1 != null) {
            stmt.bindBlob(39, Reserve1);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, ArchiveRecordsInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        String Material = entity.getMaterial();
        if (Material != null) {
            stmt.bindString(2, Material);
        }
 
        String SampleName = entity.getSampleName();
        if (SampleName != null) {
            stmt.bindString(3, SampleName);
        }
 
        String SampleMorphology = entity.getSampleMorphology();
        if (SampleMorphology != null) {
            stmt.bindString(4, SampleMorphology);
        }
 
        String SeriesNumber = entity.getSeriesNumber();
        if (SeriesNumber != null) {
            stmt.bindString(5, SeriesNumber);
        }
 
        Integer Batch = entity.getBatch();
        if (Batch != null) {
            stmt.bindLong(6, Batch);
        }
 
        String Container = entity.getContainer();
        if (Container != null) {
            stmt.bindString(7, Container);
        }
 
        String InstrumentModel = entity.getInstrumentModel();
        if (InstrumentModel != null) {
            stmt.bindString(8, InstrumentModel);
        }
 
        String SoftwareVersion = entity.getSoftwareVersion();
        if (SoftwareVersion != null) {
            stmt.bindString(9, SoftwareVersion);
        }
 
        Integer EvaluationMode = entity.getEvaluationMode();
        if (EvaluationMode != null) {
            stmt.bindLong(10, EvaluationMode);
        }
 
        String Description = entity.getDescription();
        if (Description != null) {
            stmt.bindString(11, Description);
        }
 
        Integer DetectionResult = entity.getDetectionResult();
        if (DetectionResult != null) {
            stmt.bindLong(12, DetectionResult);
        }
 
        String DetectionMan = entity.getDetectionMan();
        if (DetectionMan != null) {
            stmt.bindString(13, DetectionMan);
        }
 
        Integer CoreMethod = entity.getCoreMethod();
        if (CoreMethod != null) {
            stmt.bindLong(14, CoreMethod);
        }
        stmt.bindDouble(15, entity.getConfidence());
 
        Long SpecId = entity.getSpecId();
        if (SpecId != null) {
            stmt.bindLong(16, SpecId);
        }
 
        Date DetectionTime = entity.getDetectionTime();
        if (DetectionTime != null) {
            stmt.bindString(17, DetectionTimeConverter.convertToDatabaseValue(DetectionTime));
        }
 
        byte[] MatchReport = entity.getMatchReport();
        if (MatchReport != null) {
            stmt.bindBlob(18, MatchReport);
        }
 
        String CompoundName = entity.getCompoundName();
        if (CompoundName != null) {
            stmt.bindString(19, CompoundName);
        }
 
        String Correlation = entity.getCorrelation();
        if (Correlation != null) {
            stmt.bindString(20, Correlation);
        }
 
        Integer IsPost = entity.getIsPost();
        if (IsPost != null) {
            stmt.bindLong(21, IsPost);
        }
 
        Integer IsMQTT = entity.getIsMQTT();
        if (IsMQTT != null) {
            stmt.bindLong(22, IsMQTT);
        }
 
        Integer IsBatch = entity.getIsBatch();
        if (IsBatch != null) {
            stmt.bindLong(23, IsBatch);
        }
 
        String BatchName = entity.getBatchName();
        if (BatchName != null) {
            stmt.bindString(24, BatchName);
        }
 
        String SampleContainer = entity.getSampleContainer();
        if (SampleContainer != null) {
            stmt.bindString(25, SampleContainer);
        }
 
        Integer SampleNumber = entity.getSampleNumber();
        if (SampleNumber != null) {
            stmt.bindLong(26, SampleNumber);
        }
 
        Double longitude = entity.getLongitude();
        if (longitude != null) {
            stmt.bindDouble(27, longitude);
        }
 
        Double latitude = entity.getLatitude();
        if (latitude != null) {
            stmt.bindDouble(28, latitude);
        }
 
        String Inspector = entity.getInspector();
        if (Inspector != null) {
            stmt.bindString(29, Inspector);
        }
 
        Date InspectorTime = entity.getInspectorTime();
        if (InspectorTime != null) {
            stmt.bindString(30, InspectorTimeConverter.convertToDatabaseValue(InspectorTime));
        }
 
        Integer InspectorPass = entity.getInspectorPass();
        if (InspectorPass != null) {
            stmt.bindLong(31, InspectorPass);
        }
 
        String PublisherMacAddress = entity.getPublisherMacAddress();
        if (PublisherMacAddress != null) {
            stmt.bindString(32, PublisherMacAddress);
        }
 
        String Reviewer = entity.getReviewer();
        if (Reviewer != null) {
            stmt.bindString(33, Reviewer);
        }
 
        Date ReviewerTime = entity.getReviewerTime();
        if (ReviewerTime != null) {
            stmt.bindString(34, ReviewerTimeConverter.convertToDatabaseValue(ReviewerTime));
        }
 
        Integer ReviewerPass = entity.getReviewerPass();
        if (ReviewerPass != null) {
            stmt.bindLong(35, ReviewerPass);
        }
 
        String ReviewerMacAddress = entity.getReviewerMacAddress();
        if (ReviewerMacAddress != null) {
            stmt.bindString(36, ReviewerMacAddress);
        }
 
        Integer CertificationResult = entity.getCertificationResult();
        if (CertificationResult != null) {
            stmt.bindLong(37, CertificationResult);
        }
 
        String RecordSource = entity.getRecordSource();
        if (RecordSource != null) {
            stmt.bindString(38, RecordSource);
        }
 
        byte[] Reserve1 = entity.getReserve1();
        if (Reserve1 != null) {
            stmt.bindBlob(39, Reserve1);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public ArchiveRecordsInfo readEntity(Cursor cursor, int offset) {
        ArchiveRecordsInfo entity = new ArchiveRecordsInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // Id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // Material
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // SampleName
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // SampleMorphology
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // SeriesNumber
            cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5), // Batch
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // Container
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // InstrumentModel
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // SoftwareVersion
            cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9), // EvaluationMode
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // Description
            cursor.isNull(offset + 11) ? null : cursor.getInt(offset + 11), // DetectionResult
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // DetectionMan
            cursor.isNull(offset + 13) ? null : cursor.getInt(offset + 13), // CoreMethod
            cursor.getDouble(offset + 14), // Confidence
            cursor.isNull(offset + 15) ? null : cursor.getLong(offset + 15), // SpecId
            cursor.isNull(offset + 16) ? null : DetectionTimeConverter.convertToEntityProperty(cursor.getString(offset + 16)), // DetectionTime
            cursor.isNull(offset + 17) ? null : cursor.getBlob(offset + 17), // MatchReport
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // CompoundName
            cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19), // Correlation
            cursor.isNull(offset + 20) ? null : cursor.getInt(offset + 20), // IsPost
            cursor.isNull(offset + 21) ? null : cursor.getInt(offset + 21), // IsMQTT
            cursor.isNull(offset + 22) ? null : cursor.getInt(offset + 22), // IsBatch
            cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23), // BatchName
            cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24), // SampleContainer
            cursor.isNull(offset + 25) ? null : cursor.getInt(offset + 25), // SampleNumber
            cursor.isNull(offset + 26) ? null : cursor.getDouble(offset + 26), // longitude
            cursor.isNull(offset + 27) ? null : cursor.getDouble(offset + 27), // latitude
            cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28), // Inspector
            cursor.isNull(offset + 29) ? null : InspectorTimeConverter.convertToEntityProperty(cursor.getString(offset + 29)), // InspectorTime
            cursor.isNull(offset + 30) ? null : cursor.getInt(offset + 30), // InspectorPass
            cursor.isNull(offset + 31) ? null : cursor.getString(offset + 31), // PublisherMacAddress
            cursor.isNull(offset + 32) ? null : cursor.getString(offset + 32), // Reviewer
            cursor.isNull(offset + 33) ? null : ReviewerTimeConverter.convertToEntityProperty(cursor.getString(offset + 33)), // ReviewerTime
            cursor.isNull(offset + 34) ? null : cursor.getInt(offset + 34), // ReviewerPass
            cursor.isNull(offset + 35) ? null : cursor.getString(offset + 35), // ReviewerMacAddress
            cursor.isNull(offset + 36) ? null : cursor.getInt(offset + 36), // CertificationResult
            cursor.isNull(offset + 37) ? null : cursor.getString(offset + 37), // RecordSource
            cursor.isNull(offset + 38) ? null : cursor.getBlob(offset + 38) // Reserve1
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, ArchiveRecordsInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setMaterial(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setSampleName(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setSampleMorphology(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setSeriesNumber(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setBatch(cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5));
        entity.setContainer(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setInstrumentModel(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setSoftwareVersion(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setEvaluationMode(cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9));
        entity.setDescription(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setDetectionResult(cursor.isNull(offset + 11) ? null : cursor.getInt(offset + 11));
        entity.setDetectionMan(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setCoreMethod(cursor.isNull(offset + 13) ? null : cursor.getInt(offset + 13));
        entity.setConfidence(cursor.getDouble(offset + 14));
        entity.setSpecId(cursor.isNull(offset + 15) ? null : cursor.getLong(offset + 15));
        entity.setDetectionTime(cursor.isNull(offset + 16) ? null : DetectionTimeConverter.convertToEntityProperty(cursor.getString(offset + 16)));
        entity.setMatchReport(cursor.isNull(offset + 17) ? null : cursor.getBlob(offset + 17));
        entity.setCompoundName(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setCorrelation(cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19));
        entity.setIsPost(cursor.isNull(offset + 20) ? null : cursor.getInt(offset + 20));
        entity.setIsMQTT(cursor.isNull(offset + 21) ? null : cursor.getInt(offset + 21));
        entity.setIsBatch(cursor.isNull(offset + 22) ? null : cursor.getInt(offset + 22));
        entity.setBatchName(cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23));
        entity.setSampleContainer(cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24));
        entity.setSampleNumber(cursor.isNull(offset + 25) ? null : cursor.getInt(offset + 25));
        entity.setLongitude(cursor.isNull(offset + 26) ? null : cursor.getDouble(offset + 26));
        entity.setLatitude(cursor.isNull(offset + 27) ? null : cursor.getDouble(offset + 27));
        entity.setInspector(cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28));
        entity.setInspectorTime(cursor.isNull(offset + 29) ? null : InspectorTimeConverter.convertToEntityProperty(cursor.getString(offset + 29)));
        entity.setInspectorPass(cursor.isNull(offset + 30) ? null : cursor.getInt(offset + 30));
        entity.setPublisherMacAddress(cursor.isNull(offset + 31) ? null : cursor.getString(offset + 31));
        entity.setReviewer(cursor.isNull(offset + 32) ? null : cursor.getString(offset + 32));
        entity.setReviewerTime(cursor.isNull(offset + 33) ? null : ReviewerTimeConverter.convertToEntityProperty(cursor.getString(offset + 33)));
        entity.setReviewerPass(cursor.isNull(offset + 34) ? null : cursor.getInt(offset + 34));
        entity.setReviewerMacAddress(cursor.isNull(offset + 35) ? null : cursor.getString(offset + 35));
        entity.setCertificationResult(cursor.isNull(offset + 36) ? null : cursor.getInt(offset + 36));
        entity.setRecordSource(cursor.isNull(offset + 37) ? null : cursor.getString(offset + 37));
        entity.setReserve1(cursor.isNull(offset + 38) ? null : cursor.getBlob(offset + 38));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(ArchiveRecordsInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(ArchiveRecordsInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(ArchiveRecordsInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
