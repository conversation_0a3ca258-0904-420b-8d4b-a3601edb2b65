package com.ssraman.drugraman.newentiry;

import java.io.Serializable;

/**
 * @author: Administrator
 * @date: 2021/10/13
 */
public class SampleCollectionInfo implements Serializable {
    //聚合物
    private String polymer;

    //CAS Registry Number
    private String CAS;

    //物质名、化学名称 如：聚苯乙烯 polystyrene
    private String Substance;

    //商品名
    private String TradeName;

     //供应商
    private String Supplier;

    //填料 如：未填充 未填充
    private String Filler;

    //填料含量
    private String FillerContent;

    //颜色
    private String Color;

    //处理方法
    private String ProcessingMethod;

    //应用场合，用途
    private String Applications;

    //熔点
    private String MeltionPoint;

    public String getPolymer() {
        return this.polymer;
    }

    public void setPolymer(String polymer) {
        this.polymer = polymer;
    }

    public String getCAS() {
        return this.CAS;
    }

    public void setCAS(String CAS) {
        this.CAS = CAS;
    }

    public String getSubstance() {
        return this.Substance;
    }

    public void setSubstance(String Substance) {
        this.Substance = Substance;
    }

    public String getTradeName() {
        return this.TradeName;
    }

    public void setTradeName(String TradeName) {
        this.TradeName = TradeName;
    }

    public String getSupplier() {
        return this.Supplier;
    }

    public void setSupplier(String Supplier) {
        this.Supplier = Supplier;
    }

    public String getFiller() {
        return this.Filler;
    }

    public void setFiller(String Filler) {
        this.Filler = Filler;
    }

    public String getFillerContent() {
        return this.FillerContent;
    }

    public void setFillerContent(String FillerContent) {
        this.FillerContent = FillerContent;
    }

    public String getColor() {
        return this.Color;
    }

    public void setColor(String Color) {
        this.Color = Color;
    }

    public String getProcessingMethod() {
        return this.ProcessingMethod;
    }

    public void setProcessingMethod(String ProcessingMethod) {
        this.ProcessingMethod = ProcessingMethod;
    }

    public String getApplications() {
        return this.Applications;
    }

    public void setApplications(String Applications) {
        this.Applications = Applications;
    }

    public String getMeltionPoint() {
        return this.MeltionPoint;
    }

    public void setMeltionPoint(String MeltionPoint) {
        this.MeltionPoint = MeltionPoint;
    }

}
