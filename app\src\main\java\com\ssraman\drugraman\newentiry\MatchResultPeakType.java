package com.ssraman.drugraman.newentiry;

import java.io.Serializable;

/**
 * @author: Administrator
 * @date: 2021/6/28
 */
public class MatchResultPeakType implements Serializable {
    private double peakWave;
    private double Intensity;
    private int Must;

    private boolean isCalibration = false;
    private double CalibrationValue;

    public double getPeakWave() {
        return peakWave;
    }

    public void setPeakWave(double peakWave) {
        this.peakWave = peakWave;
    }

    public double getIntensity() {
        return Intensity;
    }

    public void setIntensity(double intensity) {
        Intensity = intensity;
    }

    public int getMust() {
        return Must;
    }

    public void setMust(int must) {
        Must = must;
    }

    public boolean isCalibration() {
        return isCalibration;
    }

    public void setCalibration(boolean calibration) {
        isCalibration = calibration;
    }

    public double getCalibrationValue() {
        return CalibrationValue;
    }

    public void setCalibrationValue(double calibrationValue) {
        CalibrationValue = calibrationValue;
    }
}
