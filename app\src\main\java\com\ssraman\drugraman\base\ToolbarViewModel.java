package com.ssraman.drugraman.base;

import android.app.Application;
import android.graphics.drawable.Drawable;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewModel;

import com.ssraman.drugraman.R;
import com.ssraman.lib_common.basemodel.IBaseViewModel;


public class ToolbarViewModel extends AndroidViewModel implements IBaseViewModel {
    //标题文字
    public ObservableField<String> titleText = new ObservableField<>("");

    //标题时间
    public ObservableField<String> titleTimeText = new ObservableField<>("");

    //标题logo
//    public ObservableField<int> titleLogo = new ObservableField<DrawableRes>();
  //  public ObservableInt titleLogo = new ObservableInt();
    public int titleLogo;

    //兼容databinding，去泛型化
    public ToolbarViewModel toolbarViewModel;

    public ToolbarViewModel(@NonNull Application application) {
        super(application);
        toolbarViewModel = this;
    }


//    public ToolbarViewModel() {
//        toolbarViewModel = this;
//    }

    /**
     * 设置标题
     *
     * @param text 标题文字
     */
    public void setTitleText(String text) {
        titleText.set(text);
    }

/**
 * @description
 * @param
 * @return 
 * <AUTHOR>
 * @time 2021/6/15 16:41
 */
    public void setTitleLogo(int titleLogo) {
        //this.titleLogo.set(titleLogo);

    }

    @Override
    public void onAny(LifecycleOwner owner, Lifecycle.Event event) {

    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void onStart() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }
}
