package com.ssraman.drugraman.db.entity;

import com.ssraman.drugraman.db.StringDateConverter;

import org.greenrobot.greendao.annotation.Convert;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;

import java.util.Date;
import org.greenrobot.greendao.annotation.Generated;

/**
 * @author: Administrator
 * @date: 2022/7/18
 */
@Entity(nameInDb = "CalibrationFt", createInDb = false)
public class CalibrationFtInfo {
    @org.greenrobot.greendao.annotation.Id(autoincrement = true)
    @Property(nameInDb = "Id")
    private Long Id;

    //波数
    @Property(nameInDb = "Wave")
    private byte[] ObWave;

    //强度
    @Property(nameInDb = "Intensity")
    private byte[] ObIntensity;

    //中心波长
    @Property(nameInDb = "SendWave")
    private Double SendWave;

    @Property(nameInDb = "SampleName")
    private String SampleName;

    @Property(nameInDb = "Ratio")
    private String Ratio;

    @Property(nameInDb = "Remark")
    private String Remark;


    //检验确认人员（发布人）
    @Property(nameInDb = "Publisher")
    private String Inspector;

    //检验确认时间
    @Property(nameInDb = "PublisherTime")
    @Convert(converter = StringDateConverter.class, columnType = String.class)
    private Date InspectorTime;

    //是否检验确认通过
    @Property(nameInDb = "PublisherPass")
    private Integer InspectorPass;

    //检验MAC地址（发布人）
    @Property(nameInDb = "PublisherMacAddress")
    private String PublisherMacAddress;

    //审核确认人员 （复核）
    @Property(nameInDb = "Reviewer")
    private String Reviewer;

    //审核确认时间
    @Property(nameInDb = "ReviewerTime")
    @Convert(converter = StringDateConverter.class, columnType = String.class)
    private Date ReviewerTime;

    //是否审核确认通过
    @Property(nameInDb = "ReviewerPass")
    private Integer ReviewerPass;

    //审核MAC地址（复核）
    @Property(nameInDb = "ReviewerMacAddress")
    private String ReviewerMacAddress;

    @Generated(hash = 642077253)
    public CalibrationFtInfo(Long Id, byte[] ObWave, byte[] ObIntensity,
            Double SendWave, String SampleName, String Ratio, String Remark,
            String Inspector, Date InspectorTime, Integer InspectorPass,
            String PublisherMacAddress, String Reviewer, Date ReviewerTime,
            Integer ReviewerPass, String ReviewerMacAddress) {
        this.Id = Id;
        this.ObWave = ObWave;
        this.ObIntensity = ObIntensity;
        this.SendWave = SendWave;
        this.SampleName = SampleName;
        this.Ratio = Ratio;
        this.Remark = Remark;
        this.Inspector = Inspector;
        this.InspectorTime = InspectorTime;
        this.InspectorPass = InspectorPass;
        this.PublisherMacAddress = PublisherMacAddress;
        this.Reviewer = Reviewer;
        this.ReviewerTime = ReviewerTime;
        this.ReviewerPass = ReviewerPass;
        this.ReviewerMacAddress = ReviewerMacAddress;
    }

    @Generated(hash = 480617272)
    public CalibrationFtInfo() {
    }

    public Long getId() {
        return this.Id;
    }

    public void setId(Long Id) {
        this.Id = Id;
    }

    public byte[] getObWave() {
        return this.ObWave;
    }

    public void setObWave(byte[] ObWave) {
        this.ObWave = ObWave;
    }

    public byte[] getObIntensity() {
        return this.ObIntensity;
    }

    public void setObIntensity(byte[] ObIntensity) {
        this.ObIntensity = ObIntensity;
    }

    public Double getSendWave() {
        return this.SendWave;
    }

    public void setSendWave(Double SendWave) {
        this.SendWave = SendWave;
    }

    public String getSampleName() {
        return this.SampleName;
    }

    public void setSampleName(String SampleName) {
        this.SampleName = SampleName;
    }

    public String getRatio() {
        return this.Ratio;
    }

    public void setRatio(String Ratio) {
        this.Ratio = Ratio;
    }

    public String getRemark() {
        return this.Remark;
    }

    public void setRemark(String Remark) {
        this.Remark = Remark;
    }

    public String getInspector() {
        return this.Inspector;
    }

    public void setInspector(String Inspector) {
        this.Inspector = Inspector;
    }

    public Date getInspectorTime() {
        return this.InspectorTime;
    }

    public void setInspectorTime(Date InspectorTime) {
        this.InspectorTime = InspectorTime;
    }

    public Integer getInspectorPass() {
        return this.InspectorPass;
    }

    public void setInspectorPass(Integer InspectorPass) {
        this.InspectorPass = InspectorPass;
    }

    public String getPublisherMacAddress() {
        return this.PublisherMacAddress;
    }

    public void setPublisherMacAddress(String PublisherMacAddress) {
        this.PublisherMacAddress = PublisherMacAddress;
    }

    public String getReviewer() {
        return this.Reviewer;
    }

    public void setReviewer(String Reviewer) {
        this.Reviewer = Reviewer;
    }

    public Date getReviewerTime() {
        return this.ReviewerTime;
    }

    public void setReviewerTime(Date ReviewerTime) {
        this.ReviewerTime = ReviewerTime;
    }

    public Integer getReviewerPass() {
        return this.ReviewerPass;
    }

    public void setReviewerPass(Integer ReviewerPass) {
        this.ReviewerPass = ReviewerPass;
    }

    public String getReviewerMacAddress() {
        return this.ReviewerMacAddress;
    }

    public void setReviewerMacAddress(String ReviewerMacAddress) {
        this.ReviewerMacAddress = ReviewerMacAddress;
    }



}
