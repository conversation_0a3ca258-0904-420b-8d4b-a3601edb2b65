package com.ssraman.drugraman.rbac.ui;

import android.content.Context;
import android.util.Log;

import com.ssraman.drugraman.db.entity.RbacUser;
import com.ssraman.drugraman.rbac.dto.AuthResult;
import com.ssraman.drugraman.rbac.manager.RbacManager;
import com.ssraman.drugraman.rbac.security.SecurityContext;
import com.ssraman.drugraman.rbac.service.IRbacUserService;

/**
 * RBAC登录管理器
 * 处理用户登录相关的RBAC逻辑
 */
public class RbacLoginManager {
    
    private static final String TAG = "RbacLoginManager";
    
    private final Context context;
    private final IRbacUserService userService;
    
    public RbacLoginManager(Context context) {
        this.context = context;
        this.userService = RbacManager.getInstance(context).getUserService();
    }
    
    /**
     * 执行用户登录
     * @param username 用户名
     * @param password 密码
     * @return 认证结果
     */
    public AuthResult login(String username, String password) {
        try {
            // 清除之前的安全上下文
            SecurityContext.clear();
            
            // 执行认证
            AuthResult authResult = userService.authenticate(username, password);
            
            if (authResult.isSuccess()) {
                // 设置安全上下文
                SecurityContext.setCurrentUser(authResult.getUser());
                SecurityContext.setCurrentRoles(authResult.getRoles());
                SecurityContext.setCurrentPermissions(authResult.getPermissions());
                SecurityContext.setSessionToken(authResult.getSessionToken());
                
                Log.i(TAG, "用户登录成功: " + username);
            } else {
                Log.w(TAG, "用户登录失败: " + username + ", 原因: " + authResult.getMessage());
            }
            
            return authResult;
            
        } catch (Exception e) {
            Log.e(TAG, "登录过程中发生错误", e);
            return AuthResult.failure("登录过程中发生错误");
        }
    }
    
    /**
     * 执行用户登出
     * @return 是否成功
     */
    public boolean logout() {
        try {
            String sessionToken = SecurityContext.getSessionToken();
            
            // 清除安全上下文
            SecurityContext.clear();
            
            // 失效会话
            if (sessionToken != null && userService != null) {
                userService.logout(sessionToken);
            }
            
            // 清除权限缓存
            RbacManager.getInstance().clearAllPermissionCache();
            
            Log.i(TAG, "用户登出成功");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "登出过程中发生错误", e);
            return false;
        }
    }
    
    /**
     * 验证会话是否有效
     * @param sessionToken 会话令牌
     * @return 是否有效
     */
    public boolean validateSession(String sessionToken) {
        try {
            if (sessionToken == null || sessionToken.isEmpty()) {
                return false;
            }
            
            RbacUser user = userService.validateSession(sessionToken);
            if (user != null) {
                // 重新设置安全上下文
                SecurityContext.setCurrentUser(user);
                SecurityContext.setSessionToken(sessionToken);
                
                // 重新加载角色和权限
                RbacManager rbacManager = RbacManager.getInstance();
                SecurityContext.setCurrentRoles(rbacManager.getRoleService().getUserRoles(user.getId()));
                SecurityContext.setCurrentPermissions(rbacManager.getPermissionService().getUserPermissions(user.getId()));
                
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            Log.e(TAG, "会话验证失败", e);
            return false;
        }
    }
    
    /**
     * 刷新会话
     * @return 新的会话令牌
     */
    public String refreshSession() {
        try {
            String currentToken = SecurityContext.getSessionToken();
            if (currentToken == null) {
                return null;
            }
            
            String newToken = userService.refreshSession(currentToken);
            if (newToken != null) {
                SecurityContext.setSessionToken(newToken);
                Log.i(TAG, "会话刷新成功");
            }
            
            return newToken;
            
        } catch (Exception e) {
            Log.e(TAG, "会话刷新失败", e);
            return null;
        }
    }
    
    /**
     * 获取当前登录用户信息
     * @return 用户信息
     */
    public RbacUser getCurrentUser() {
        return SecurityContext.getCurrentUser();
    }
    
    /**
     * 检查是否已登录
     * @return 是否已登录
     */
    public boolean isLoggedIn() {
        return SecurityContext.isAuthenticated();
    }
    
    /**
     * 获取当前用户的显示名称
     * @return 显示名称
     */
    public String getCurrentUserDisplayName() {
        RbacUser user = getCurrentUser();
        if (user == null) {
            return null;
        }
        
        String displayName = user.getFullName();
        if (displayName == null || displayName.trim().isEmpty()) {
            displayName = user.getUsername();
        }
        
        return displayName;
    }
    
    /**
     * 获取当前用户的角色描述
     * @return 角色描述
     */
    public String getCurrentUserRoleDescription() {
        try {
            RbacUser user = getCurrentUser();
            if (user == null) {
                return "未登录";
            }
            
            int maxLevel = SecurityContext.getCurrentMaxRoleLevel();
            switch (maxLevel) {
                case 1: return "操作员";
                case 2: return "审核员";
                case 3: return "谱图管理员";
                case 4: return "用户管理员";
                case 5: return "系统管理员";
                default: return "普通用户";
            }
            
        } catch (Exception e) {
            Log.e(TAG, "获取用户角色描述失败", e);
            return "未知角色";
        }
    }
}
