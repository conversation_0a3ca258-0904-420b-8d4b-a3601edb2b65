<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="mpresenter"
            type="com.ssraman.drugraman.ui.other.AddLibraryFragment.MPresenter" />

        <variable
            name="addLibraryViewModel"
            type="com.ssraman.drugraman.ui.vm.AddLibraryViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/nc_main"
        tools:context=".ui.other.AddLibraryFragment">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline11"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.08" />

        <TextView
            android:id="@+id/textView34"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:drawableTop="@drawable/ic_add_lib_title_24"
            android:text="@string/title_add_to_library"
            android:textColor="@color/nc_light"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/guideline20"
            app:layout_constraintEnd_toStartOf="@+id/guideline11"
            app:layout_constraintStart_toStartOf="@+id/guideline11"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="5dp"
            app:layout_constraintBottom_toTopOf="@+id/btn_add_standardlib"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/guideline20">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline19"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_begin="180dp" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline21"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.04" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline22"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.96" />


                <!--    这里如果选择大类下面就是项名称，选择项下面就是谱图名称-->
                <TextView
                    android:id="@+id/title_Material_type"
                    style="@style/CollectInfoTextStyle"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/title_material_type_with_colon"
                    app:layout_constraintStart_toStartOf="@+id/guideline21"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.ssraman.control.spinner.MaterialSpinner
                    android:id="@+id/tv_Material_type"
                    android:layout_width="200dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="1dp"
                    android:background="@drawable/input_border_bottom_border"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@+id/title_Material_type"
                    app:layout_constraintStart_toEndOf="@+id/title_Material_type"
                    app:layout_constraintTop_toTopOf="@+id/title_Material_type"
                    app:ms_background_color="@color/dark_gray"
                    app:ms_popupwindow_height="wrap_content"
                    app:ms_popupwindow_maxheight="200dp"
                    app:ms_text_color="@android:color/white" />

                <TextView
                    android:id="@+id/title_sample_name"
                    style="@style/CollectInfoTextStyle"
                    android:layout_marginTop="16dp"
                    android:text="@string/title_material_with_colon"
                    app:layout_constraintEnd_toEndOf="@+id/title_Material_type"
                    app:layout_constraintTop_toBottomOf="@+id/title_Material_type" />

                <EditText
                    android:id="@+id/edit_sample_name"
                    style="@style/CollectInfoEditTextStyle"
                    android:layout_marginStart="1dp"
                    app:layout_constraintBottom_toBottomOf="@+id/title_sample_name"
                    app:layout_constraintStart_toEndOf="@+id/title_sample_name"
                    app:layout_constraintTop_toTopOf="@+id/title_sample_name"
                    tools:text="@string/hint_enter_name" />

                <TextView
                    android:id="@+id/title_start_wave"
                    style="@style/CollectInfoTextStyle"
                    android:layout_marginTop="16dp"
                    android:text="@string/title_sensitivity_with_colon"
                    app:layout_constraintEnd_toEndOf="@+id/title_sample_name"
                    app:layout_constraintTop_toBottomOf="@+id/title_sample_name" />

                <EditText
                    android:id="@+id/edit_start_wave"
                    style="@style/CollectInfoEditTextStyle"
                    android:layout_marginStart="1dp"
                    android:inputType="number"
                    android:lines="1"
                    app:layout_constraintBottom_toBottomOf="@+id/title_start_wave"
                    app:layout_constraintStart_toEndOf="@+id/title_start_wave"
                    app:layout_constraintTop_toTopOf="@+id/title_start_wave"
                    tools:text="@string/hint_enter_sensitivity" />

                <TextView
                    android:id="@+id/title_rate"
                    style="@style/CollectInfoTextStyle"
                    android:layout_marginTop="16dp"
                    android:text="@string/title_match_ratio_with_colon"
                    app:layout_constraintEnd_toEndOf="@+id/title_start_wave"
                    app:layout_constraintTop_toBottomOf="@+id/title_start_wave" />

                <EditText
                    android:id="@+id/edit_rate"
                    style="@style/CollectInfoEditTextStyle"
                    android:layout_marginStart="1dp"
                    android:inputType="number"
                    android:lines="1"
                    app:layout_constraintBottom_toBottomOf="@+id/title_rate"
                    app:layout_constraintStart_toEndOf="@+id/title_rate"
                    app:layout_constraintTop_toTopOf="@+id/title_rate"
                    tools:text="@string/hint_enter_match_ratio" />

                <TextView
                    android:id="@+id/title_range"
                    style="@style/CollectInfoTextStyle"
                    android:layout_marginTop="16dp"
                    android:text="@string/title_tolerance_wave_number_with_colon"
                    app:layout_constraintEnd_toEndOf="@+id/title_rate"
                    app:layout_constraintTop_toBottomOf="@+id/title_rate" />

                <EditText
                    android:id="@+id/edit_range"
                    style="@style/CollectInfoEditTextStyle"
                    android:layout_marginStart="1dp"
                    android:inputType="number"
                    android:lines="1"
                    app:layout_constraintBottom_toBottomOf="@+id/title_range"
                    app:layout_constraintStart_toEndOf="@+id/title_range"
                    app:layout_constraintTop_toTopOf="@+id/title_range"
                    tools:text="@string/hint_enter_match_ratio" />

                <TextView
                    android:id="@+id/title_confidence"
                    style="@style/CollectInfoTextStyle"
                    android:layout_marginTop="16dp"
                    android:text="@string/title_confidence_with_colon"
                    app:layout_constraintEnd_toEndOf="@+id/title_range"
                    app:layout_constraintTop_toBottomOf="@+id/title_range" />

                <EditText
                    android:id="@+id/edit_confidence"
                    style="@style/CollectInfoEditTextStyle"
                    android:layout_marginStart="1dp"
                    android:inputType="number"
                    android:lines="1"
                    app:layout_constraintBottom_toBottomOf="@+id/title_confidence"
                    app:layout_constraintStart_toEndOf="@+id/title_confidence"
                    app:layout_constraintTop_toTopOf="@+id/title_confidence"
                    tools:text="@string/hint_enter_confidence" />

                <com.ssraman.drugraman.custom.SpectrumLineChart
                    android:id="@+id/spec_detection_result2"
                    android:layout_width="0dp"
                    android:layout_height="210dp"
                    android:layout_marginTop="8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/guideline21"
                    app:layout_constraintTop_toBottomOf="@+id/title_confidence" />


                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:minHeight="210dp"
                    android:layout_marginTop="6dp"
                    app:layout_constraintEnd_toStartOf="@+id/guideline22"
                    app:layout_constraintStart_toStartOf="@+id/guideline21"
                    app:layout_constraintTop_toBottomOf="@+id/spec_detection_result2">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_view_peak"
                        android:layout_width="319dp"
                        android:layout_height="match_parent"
                        tools:layout_editor_absoluteY="468dp" />
                </RelativeLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <TextView
            android:id="@+id/btn_description_info"
            style="@style/ButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginBottom="20dp"
            android:drawableTop="@drawable/ic_description_info_32"
            android:onClick="@{(view)->mpresenter.BtnDescriptionInfoClick(view)}"
            android:text="@string/btn_description_info"
            android:textColor="@color/nc_light"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/btn_add_standardlib"
            style="@style/ButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:drawableTop="@drawable/ic_add_lib_32"
            android:onClick="@{(view)->mpresenter.BtnAddLibClick(view)}"
            android:text="@string/btn_add_to_library"
            android:textColor="@color/nc_light"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.core.widget.ContentLoadingProgressBar
            android:id="@+id/add_new_record"
            style="?android:attr/progressBarStyleLarge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="@{addLibraryViewModel.isLoading ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>