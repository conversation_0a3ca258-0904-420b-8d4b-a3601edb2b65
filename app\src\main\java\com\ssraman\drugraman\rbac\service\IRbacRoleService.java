package com.ssraman.drugraman.rbac.service;

import com.ssraman.drugraman.db.entity.RbacRole;
import com.ssraman.drugraman.db.entity.RbacUser;

import java.util.List;

/**
 * RBAC角色服务接口
 * 提供角色管理的核心功能
 */
public interface IRbacRoleService {
    
    // ========== 角色管理 ==========
    
    /**
     * 创建角色
     * @param roleCode 角色代码
     * @param roleName 角色名称
     * @param description 描述
     * @param level 级别
     * @return 创建的角色
     */
    RbacRole createRole(String roleCode, String roleName, String description, Integer level);
    
    /**
     * 更新角色信息
     * @param roleId 角色ID
     * @param roleName 角色名称
     * @param description 描述
     * @param level 级别
     * @return 更新后的角色
     */
    RbacRole updateRole(Long roleId, String roleName, String description, Integer level);
    
    /**
     * 删除角色
     * @param roleId 角色ID
     * @return 操作结果
     */
    boolean deleteRole(Long roleId);
    
    /**
     * 根据ID获取角色
     * @param roleId 角色ID
     * @return 角色信息
     */
    RbacRole getRoleById(Long roleId);
    
    /**
     * 根据角色代码获取角色
     * @param roleCode 角色代码
     * @return 角色信息
     */
    RbacRole getRoleByCode(String roleCode);
    
    /**
     * 获取所有角色
     * @return 角色列表
     */
    List<RbacRole> getAllRoles();
    
    /**
     * 根据级别获取角色
     * @param level 角色级别
     * @return 角色列表
     */
    List<RbacRole> getRolesByLevel(Integer level);
    
    /**
     * 获取级别大于等于指定值的角色
     * @param minLevel 最小级别
     * @return 角色列表
     */
    List<RbacRole> getRolesByMinLevel(Integer minLevel);
    
    // ========== 用户角色关联管理 ==========
    
    /**
     * 为用户分配角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param assignedBy 分配者ID
     * @return 操作结果
     */
    boolean assignRoleToUser(Long userId, Long roleId, Long assignedBy);
    
    /**
     * 为用户分配角色（带过期时间）
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param assignedBy 分配者ID
     * @param expiresAt 过期时间（毫秒）
     * @return 操作结果
     */
    boolean assignRoleToUser(Long userId, Long roleId, Long assignedBy, Long expiresAt);
    
    /**
     * 撤销用户角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 操作结果
     */
    boolean revokeRoleFromUser(Long userId, Long roleId);
    
    /**
     * 获取用户的角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RbacRole> getUserRoles(Long userId);
    
    /**
     * 获取用户的有效角色列表（未过期且启用的）
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RbacRole> getUserActiveRoles(Long userId);
    
    /**
     * 获取角色的用户列表
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<RbacUser> getRoleUsers(Long roleId);
    
    /**
     * 检查用户是否具有指定角色
     * @param userId 用户ID
     * @param roleCode 角色代码
     * @return 是否具有角色
     */
    boolean userHasRole(Long userId, String roleCode);
    
    /**
     * 检查用户是否具有指定级别的角色
     * @param userId 用户ID
     * @param requiredLevel 要求的最低级别
     * @return 是否满足级别要求
     */
    boolean userHasRoleLevel(Long userId, Integer requiredLevel);
    
    /**
     * 获取用户的最高角色级别
     * @param userId 用户ID
     * @return 最高角色级别
     */
    Integer getUserMaxRoleLevel(Long userId);
    
    // ========== 角色状态管理 ==========
    
    /**
     * 启用角色
     * @param roleId 角色ID
     * @return 操作结果
     */
    boolean enableRole(Long roleId);
    
    /**
     * 禁用角色
     * @param roleId 角色ID
     * @return 操作结果
     */
    boolean disableRole(Long roleId);
    
    /**
     * 检查角色代码是否存在
     * @param roleCode 角色代码
     * @return 是否存在
     */
    boolean existsRoleCode(String roleCode);
    
    // ========== 角色统计 ==========
    
    /**
     * 获取角色总数
     * @return 角色总数
     */
    long getRoleCount();
    
    /**
     * 获取启用的角色数
     * @return 启用的角色数
     */
    long getActiveRoleCount();
    
    /**
     * 获取角色的用户数量
     * @param roleId 角色ID
     * @return 用户数量
     */
    long getRoleUserCount(Long roleId);
}
