package com.ssraman.drugraman.ui.other;

import android.os.Bundle;

import androidx.annotation.Nullable;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;

import com.jeremyliao.liveeventbus.LiveEventBus;
import com.kongzue.dialog.interfaces.OnDialogButtonClickListener;
import com.kongzue.dialog.util.BaseDialog;
import com.kongzue.dialog.v3.MessageDialog;
import com.ssraman.drugraman.BR;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.base.ExBaseFragment;
import com.ssraman.drugraman.databinding.FragmentValidationOptionsBinding;
import com.ssraman.drugraman.ui.vm.ValidationOptionsViewModel;
import com.ssraman.lib_common.mac.DrawerSwichBean;
import com.ssraman.lib_common.mac.SettingPre;
import com.ssraman.lib_common.mac.ToolBarSwichBean;


public class ValidationOptionsFragment extends ExBaseFragment<FragmentValidationOptionsBinding, ValidationOptionsViewModel> {


    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_validation_options;
    }

    @Override
    public int initVariableId() {
        return BR.validationOptionsViewModel;
    }

    @Override
    public void initData() {
        super.initData();
        binding.setMpresenter(new MPresenter());


        if (SettingPre.getValidationOption() == 0) { //验证模式
            binding.chkValidation.setChecked(false);
        } else {
            binding.chkValidation.setChecked(true);
        }
        binding.chkValidation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String msg = "开启后，工作在验证环境";
                String btn_msg = "开 启";
                if (SettingPre.getValidationOption() == 0) {
                    msg = "开启后，工作在验证环境。";
                    btn_msg = "开 启";
                } else {
                    msg = "关闭后，工作在普通环境。"; //省电模式
                    btn_msg = "关 闭";
                }
                MessageDialog.build(mAppCompatActivity)
                        .setTitle("提示信息")
                        .setMessage(msg)
                        .setOkButton(btn_msg, new OnDialogButtonClickListener() {
                            @Override
                            public boolean onClick(BaseDialog baseDialog, View v) {
                                //设置tupe
                                if (SettingPre.getValidationOption() == 0) {
                                    SettingPre.setValidationOption(1);

                                } else {
                                    SettingPre.setValidationOption(0);

                                }
                                return false;
                            }
                        })
                        .setCancelButton("取 消", new OnDialogButtonClickListener() {
                            @Override
                            public boolean onClick(BaseDialog baseDialog, View v) {
                                if (binding.chkValidation.isChecked()) {
                                    binding.chkValidation.setChecked(false);
                                    binding.chkValidation.setSelected(false);
                                } else {
                                    binding.chkValidation.setChecked(true);
                                    binding.chkValidation.setSelected(true);
                                }
                                return false;
                            }
                        })
                        .show();
            }
        });
        binding.chkValidation.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

            }
        });
    }


    @Override
    public void onResume() {
        super.onResume();
        LiveEventBus.get("toolbar_swich").postAcrossProcess(new ToolBarSwichBean(1, "主菜单"));
        LiveEventBus.get("drawer_swich").postAcrossProcess(new DrawerSwichBean(1, "不使能"));
    }

    public class MPresenter {

    }

}