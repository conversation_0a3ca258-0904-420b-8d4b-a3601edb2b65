package com.ssraman.drugraman.db.repository;

import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.gen.PeakInfoDao;

import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Completable;

/**
 * @author: Administrator
 * @date: 2021/6/21
 */
public class SysPeakRepository {
    private DaoSession mDaoSession;
    private PeakInfoDao peakDao;

    public SysPeakRepository(DaoSession daoSession) {
        this.mDaoSession = daoSession;
        peakDao = this.mDaoSession.getPeakInfoDao();
    }

    public Completable insertPeak(PeakInfo peakInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                peakDao.insert(peakInfo);
                return null;
            }
        });
    }

    public void insertPeakList(List<PeakInfo> peaklist)
    {
        peakDao.insertOrReplaceInTx(peaklist);
    }

    public Completable deletePeak(PeakInfo peakInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                peakDao.delete(peakInfo);
                return null;
            }
        });
    }

    public void deletePeaks(List<PeakInfo> peakInfoList)
    {
        peakDao.deleteInTx(peakInfoList);
    }

    public Completable updatePeak(PeakInfo peakInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                peakDao.update(peakInfo);
                return null;
            }
        });
    }

    public void updatePeaks(List<PeakInfo> peakInfoList)
    {
        peakDao.updateInTx(peakInfoList);
    }

    public List<PeakInfo> getPeakListByFtId(Long id)
    {
        return peakDao.queryBuilder().where(PeakInfoDao.Properties.FtId.eq(id)).list();
    }

}
