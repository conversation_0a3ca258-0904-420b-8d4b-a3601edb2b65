package com.ssraman.drugraman.db.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Generated;

/**
 * @author: Administrator
 * @date: 2021/10/13
 */
@Entity(nameInDb = "SpectralDataTable", createInDb = false)
public class SpectralDataInfo {
    @Id(autoincrement = true)
    @Property(nameInDb = "Id")
    private Long Id;

    //谱图名称
    @Property(nameInDb = "SpecName")
    private String SpecName;
    //波数
    @Property(nameInDb = "Wave")
    private byte[] ObWave;
    //强度
    @Property(nameInDb = "Intensity")
    private byte[] ObIntensity;
    //特征峰数据
    @Property(nameInDb = "PeakData")
    private byte[] PeakData;
    //中心波长
    @Property(nameInDb = "SendWave")
    private Double SendWave;
    //起始波数
    @Property(nameInDb = "StartWave")
    private Integer StartWave;


    @Property(nameInDb = "IntegratioTime")
    private Integer IntegratioTime;
    @Property(nameInDb = "LaserPower")
    private Double LaserPower;
    @Property(nameInDb = "AverageCount")
    private Integer AverageCount;

    //原始数据x
    @Property(nameInDb = "Raw_x")
    private byte[] Raw_x;
    //原始数据y
    @Property(nameInDb = "Raw_y")
    private byte[] Raw_y;
    //设备系数
    @Property(nameInDb = "mac_coefficient")
    private String mac_coefficient;



    @Generated(hash = 983966213)
    public SpectralDataInfo(Long Id, String SpecName, byte[] ObWave,
            byte[] ObIntensity, byte[] PeakData, Double SendWave, Integer StartWave,
            Integer IntegratioTime, Double LaserPower, Integer AverageCount,
            byte[] Raw_x, byte[] Raw_y, String mac_coefficient) {
        this.Id = Id;
        this.SpecName = SpecName;
        this.ObWave = ObWave;
        this.ObIntensity = ObIntensity;
        this.PeakData = PeakData;
        this.SendWave = SendWave;
        this.StartWave = StartWave;
        this.IntegratioTime = IntegratioTime;
        this.LaserPower = LaserPower;
        this.AverageCount = AverageCount;
        this.Raw_x = Raw_x;
        this.Raw_y = Raw_y;
        this.mac_coefficient = mac_coefficient;
    }
    @Generated(hash = 667782821)
    public SpectralDataInfo() {
    }



    public Long getId() {
        return this.Id;
    }
    public void setId(Long Id) {
        this.Id = Id;
    }
    public String getSpecName() {
        return this.SpecName;
    }
    public void setSpecName(String SpecName) {
        this.SpecName = SpecName;
    }
    public byte[] getObWave() {
        return this.ObWave;
    }
    public void setObWave(byte[] ObWave) {
        this.ObWave = ObWave;
    }
    public byte[] getObIntensity() {
        return this.ObIntensity;
    }
    public void setObIntensity(byte[] ObIntensity) {
        this.ObIntensity = ObIntensity;
    }
    public byte[] getPeakData() {
        return this.PeakData;
    }
    public void setPeakData(byte[] PeakData) {
        this.PeakData = PeakData;
    }
    public Double getSendWave() {
        return this.SendWave;
    }
    public void setSendWave(Double SendWave) {
        this.SendWave = SendWave;
    }
    public Integer getStartWave() {
        return this.StartWave;
    }
    public void setStartWave(Integer StartWave) {
        this.StartWave = StartWave;
    }
    public Integer getIntegratioTime() {
        return this.IntegratioTime;
    }
    public void setIntegratioTime(Integer IntegratioTime) {
        this.IntegratioTime = IntegratioTime;
    }
    public Double getLaserPower() {
        return this.LaserPower;
    }
    public void setLaserPower(Double LaserPower) {
        this.LaserPower = LaserPower;
    }
    public Integer getAverageCount() {
        return this.AverageCount;
    }
    public void setAverageCount(Integer AverageCount) {
        this.AverageCount = AverageCount;
    }
    public byte[] getRaw_x() {
        return this.Raw_x;
    }
    public void setRaw_x(byte[] Raw_x) {
        this.Raw_x = Raw_x;
    }
    public byte[] getRaw_y() {
        return this.Raw_y;
    }
    public void setRaw_y(byte[] Raw_y) {
        this.Raw_y = Raw_y;
    }
    public String getMac_coefficient() {
        return this.mac_coefficient;
    }
    public void setMac_coefficient(String mac_coefficient) {
        this.mac_coefficient = mac_coefficient;
    }

}
