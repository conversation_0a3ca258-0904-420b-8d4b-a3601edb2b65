package com.ssraman.drugraman.typeface;

/**
 * @author: Administrator
 * @date: 2021/7/8
 */
public enum SettingFuncType {
    Com_setting(0),
    Wifi_setting(1),
    Change_pwd(2),
    ServerParam(3),
    Calibration(4),
//    Verification(5),
    CreateStandardLibrary(5),
    Device_passthrough(6),
    Exchange_data(7),
    System_setting(8);

    private int value = 0;

    private SettingFuncType(int value) {    //    必须是private的，否则编译错误
        this.value = value;
    }

    public static SettingFuncType valueOf(int value) {    //    手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return Com_setting;
            case 1:
                return Wifi_setting;
            case 2:
                return Change_pwd;
            case 3:
                return ServerParam;
            case 4:
                return Calibration;
//            case 5:
//                return Verification;
            case 5:
                return CreateStandardLibrary;
            case 6:
                return Device_passthrough;
            case 7:
                return Exchange_data;
            case 8:
                return System_setting;
            default:
                return Com_setting;
        }
    }

    public int value() {
        return this.value;
    }
}
