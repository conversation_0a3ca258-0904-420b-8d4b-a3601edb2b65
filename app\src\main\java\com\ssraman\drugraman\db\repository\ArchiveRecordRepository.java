package com.ssraman.drugraman.db.repository;

import com.ssraman.drugraman.db.StringDateConverter;
import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;
import com.ssraman.drugraman.db.gen.ArchiveRecordsInfoDao;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.newentiry.FilterInfo;
import com.ssraman.drugraman.newentiry.PageInfo;
import com.ssraman.lib_common.room.RxRoom;

import org.greenrobot.greendao.database.DatabaseStatement;
import org.greenrobot.greendao.internal.SqlUtils;
import org.greenrobot.greendao.query.QueryBuilder;
import org.greenrobot.greendao.query.WhereCondition;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Completable;
import io.reactivex.Single;

/**
 * @author: Administrator
 * @date: 2021/10/20
 */
public class ArchiveRecordRepository {
    private DaoSession mDaoSession;
    private ArchiveRecordsInfoDao archiveRecordDao;

    public ArchiveRecordRepository(DaoSession daoSession) {
        this.mDaoSession = daoSession;
        archiveRecordDao = this.mDaoSession.getArchiveRecordsInfoDao();
    }


    public Completable insertRecord(ArchiveRecordsInfo archiveRecordsInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                archiveRecordDao.insert(archiveRecordsInfo);
                return null;
            }
        });
    }

    public long insertRecordByRe(ArchiveRecordsInfo archiveRecordsInfo) {
        return archiveRecordDao.insert(archiveRecordsInfo);
    }

    public Completable deleteRecord(ArchiveRecordsInfo archiveRecordsInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                archiveRecordDao.delete(archiveRecordsInfo);
                return null;
            }
        });
    }

    public Completable updateRecords(ArchiveRecordsInfo archiveRecordsInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                archiveRecordDao.update(archiveRecordsInfo);
                return null;
            }
        });
    }

    public long updateRecordByRe(ArchiveRecordsInfo archiveRecordsInfo) {
        archiveRecordDao.update(archiveRecordsInfo);
        return archiveRecordsInfo.getId();
    }

    public void updateRecordSimple(ArchiveRecordsInfo archiveRecordsInfo) {
        archiveRecordDao.update(archiveRecordsInfo);
    }

    public Single<List<ArchiveRecordsInfo>> getRecordListByFilter(PageInfo pageInfo, FilterInfo filter) {
        return RxRoom.createSingle(new Callable<List<ArchiveRecordsInfo>>() {
            @Override
            public List<ArchiveRecordsInfo> call() throws Exception {
                List<ArchiveRecordsInfo> recordList;
                long count;
                if (filter != null) {
                    StringDateConverter DetectionTimeConverter = new StringDateConverter();
                    QueryBuilder<ArchiveRecordsInfo> recordQueryBuilder = archiveRecordDao.queryBuilder();
                    List<WhereCondition> whereConditions = new ArrayList<>();
                    if (filter.IsDateTime) {
                        String DetectionStartTime = DetectionTimeConverter.convertToDatabaseValue(filter.DetectionStartTime);
                        String DetectionEndTime = DetectionTimeConverter.convertToDatabaseValue(filter.DetectionEndTime);
                        WhereCondition startWhere = ArchiveRecordsInfoDao.Properties.DetectionTime.ge(DetectionStartTime);
                        WhereCondition endWhere = ArchiveRecordsInfoDao.Properties.DetectionTime.le(DetectionEndTime);
                        whereConditions.add(startWhere);
                        whereConditions.add(endWhere);
                    }
                    if (filter.IsAndCheckMen) {
                        WhereCondition checkManWhere = ArchiveRecordsInfoDao.Properties.DetectionMan.like("%" + (String) filter.CheckMan + "%");
                        whereConditions.add(checkManWhere);
                    }
                    if (filter.IsAndMaterial) {
                        WhereCondition prjNameWhere = ArchiveRecordsInfoDao.Properties.Material.like("%" + (String) filter.Material + "%");
                        whereConditions.add(prjNameWhere);
                    }
                    if (filter.IsSeriesNumber) {
                        WhereCondition seriesNumberWhere = ArchiveRecordsInfoDao.Properties.SeriesNumber.like("%" + (String) filter.SeriesNumber + "%");
                        whereConditions.add(seriesNumberWhere);
                    }
                    if (whereConditions.size() > 0) {
                        for (int i = 0; i < whereConditions.size(); i++) {
                            recordQueryBuilder = recordQueryBuilder.where(whereConditions.get(i));
                        }
                    }
                    count = recordQueryBuilder.count();
                    if (pageInfo.getPageSize() != -1) {
                        //获取所有
                        recordList = recordQueryBuilder
                                .offset(pageInfo.getPageIndex() * pageInfo.getPageSize()).limit(pageInfo.getPageSize()).orderDesc(ArchiveRecordsInfoDao.Properties.DetectionTime).list();
                    } else {
                        recordList = recordQueryBuilder.orderDesc(ArchiveRecordsInfoDao.Properties.DetectionTime).list();
                    }
                } else {
                    count = archiveRecordDao.queryBuilder().count();
                    if (pageInfo.getPageSize() != -1) {
                        //获取所有
                        recordList = archiveRecordDao.queryBuilder()
                                .offset(pageInfo.getPageIndex() * pageInfo.getPageSize()).limit(pageInfo.getPageSize()).orderDesc(ArchiveRecordsInfoDao.Properties.DetectionTime).list();
                    } else {
                        recordList = archiveRecordDao.queryBuilder().orderDesc(ArchiveRecordsInfoDao.Properties.DetectionTime).list();
                    }

                }
                return recordList;
            }
        });
    }

    public Single<List<ArchiveRecordsInfo>> getRecordListBySimple(PageInfo pageInfo, Date start, Date end) {
        return RxRoom.createSingle(new Callable<List<ArchiveRecordsInfo>>() {
            @Override
            public List<ArchiveRecordsInfo> call() throws Exception {
                List<ArchiveRecordsInfo> recordList;
                long count;
                StringDateConverter DetectionTimeConverter = new StringDateConverter();
                QueryBuilder<ArchiveRecordsInfo> recordQueryBuilder = archiveRecordDao.queryBuilder();
                List<WhereCondition> whereConditions = new ArrayList<>();
                String str_start = DetectionTimeConverter.convertToDatabaseValue(start);
                String str_end = DetectionTimeConverter.convertToDatabaseValue(end);
                WhereCondition startWhere = ArchiveRecordsInfoDao.Properties.DetectionTime.ge(str_start);
                WhereCondition endWhere = ArchiveRecordsInfoDao.Properties.DetectionTime.le(str_end);
                whereConditions.add(startWhere);
                whereConditions.add(endWhere);

                if (whereConditions.size() > 0) {
                    for (int i = 0; i < whereConditions.size(); i++) {
                        recordQueryBuilder = recordQueryBuilder.where(whereConditions.get(i));
                    }
                }

                count = recordQueryBuilder.count();
                if (pageInfo.getPageSize() != -1) {
                    //获取所有
                    recordList = recordQueryBuilder
                            .offset(pageInfo.getPageIndex() * pageInfo.getPageSize()).limit(pageInfo.getPageSize()).orderDesc(ArchiveRecordsInfoDao.Properties.DetectionTime).list();
                } else {
                    recordList = recordQueryBuilder.orderDesc(ArchiveRecordsInfoDao.Properties.DetectionTime).list();
                }

                return recordList;
            }
        });
    }

    public Completable deleteRcordListByFilter(String delete_str) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                mDaoSession.getDatabase().execSQL(delete_str);
                return null;
            }
        });
    }

    public boolean updateUploadOkById(int record_id) {
        try {
            StringBuilder strSql = new StringBuilder("update ArchiveRecordsTable Set IsMQTT='1' where Id='" + record_id + "' ");
            mDaoSession.getDatabase().execSQL(strSql.toString().trim());
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }


}
