<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="mpresenter"
            type="com.ssraman.drugraman.ui.other.AddLibCollectInfoFragment.MPresenter" />

        <variable
            name="addLibCollectInfoViewModel"
            type="com.ssraman.drugraman.ui.vm.AddLibCollectInfoViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/nc_main"
        tools:context=".ui.other.AddLibCollectInfoFragment">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline9"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.50" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.09" />

        <TextView
            android:id="@+id/textView34"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="3dp"
            android:drawableTop="@drawable/ic_add_lib_title_24"
            android:text="@string/title_create_standard_spectrum"
            android:textColor="@color/nc_light"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/guideline5"
            app:layout_constraintEnd_toStartOf="@+id/guideline9"
            app:layout_constraintStart_toStartOf="@+id/guideline9" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline19"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.50" />


        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.04" />


        <TextView
            android:id="@+id/btn_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:drawableTop="@drawable/ic_setting_24"
            android:onClick="@{(view)->mpresenter.BtnSettingClick(view)}"
            android:text="@string/btn_settings"
            android:textColor="@color/nc_light"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/guideline5" />

        <TextView
            android:id="@+id/title_sample_name"
            style="@style/CollectInfoTextStyle"
            android:layout_marginTop="18dp"
            android:text="@string/title_material_name_with_colon"
            app:layout_constraintStart_toStartOf="@+id/guideline8"
            app:layout_constraintTop_toBottomOf="@+id/guideline5" />

        <EditText
            android:id="@+id/edit_sample_name"
            style="@style/CollectInfoEditTextStyle"
            android:layout_marginStart="1dp"
            app:layout_constraintBottom_toBottomOf="@+id/title_sample_name"
            app:layout_constraintStart_toEndOf="@+id/title_sample_name"
            app:layout_constraintTop_toTopOf="@+id/title_sample_name"
            app:layout_constraintVertical_bias="1.0"
            tools:text="@string/hint_please_enter" />

        <TextView
            android:id="@+id/title_samlpe_controller"
            style="@style/CollectInfoTextStyle"
            android:layout_marginTop="18dp"
            android:text="@string/title_probe_position_with_colon"
            app:layout_constraintEnd_toEndOf="@+id/title_sample_name"
            app:layout_constraintTop_toBottomOf="@+id/title_sample_name" />

        <com.ssraman.control.spinner.MaterialSpinner
            android:id="@+id/tv_samlpe_controller"
            android:layout_width="200dp"
            android:layout_height="32dp"
            android:layout_marginStart="1dp"
            android:background="@drawable/input_border_bottom_border"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/title_samlpe_controller"
            app:layout_constraintStart_toEndOf="@+id/title_samlpe_controller"
            app:layout_constraintTop_toTopOf="@+id/title_samlpe_controller"
            app:ms_background_color="@color/dark_gray"
            app:ms_popupwindow_height="wrap_content"
            app:ms_popupwindow_maxheight="200dp"
            app:ms_text_color="@android:color/white" />

        <TextView
            android:id="@+id/title_integration_time"
            style="@style/CollectInfoTextStyle"
            android:layout_marginTop="18dp"
            android:text="@string/title_integration_time_with_colon"
            app:layout_constraintEnd_toEndOf="@+id/title_samlpe_controller"
            app:layout_constraintTop_toBottomOf="@+id/title_samlpe_controller" />

        <com.ssraman.control.spinner.MaterialSpinner
            android:id="@+id/tv_integration_time"
            android:layout_width="120dp"
            android:layout_height="32dp"
            android:layout_marginStart="1dp"
            android:background="@drawable/input_border_bottom_border"
            android:textColor="@color/txtblackgray"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/title_integration_time"
            app:layout_constraintStart_toEndOf="@+id/title_integration_time"
            app:layout_constraintTop_toTopOf="@+id/title_integration_time"
            app:ms_background_color="@color/dark_gray"
            app:ms_popupwindow_height="wrap_content"
            app:ms_popupwindow_maxheight="200dp"
            app:ms_text_color="@android:color/white" />

        <com.ssraman.control.spinner.MaterialSpinner
            android:id="@+id/tv_integration_unit"
            android:layout_width="80dp"
            android:layout_height="32dp"
            android:layout_marginStart="1dp"
            android:background="@drawable/input_border_bottom_border"
            android:textColor="@color/txtblackgray"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_integration_time"
            app:layout_constraintStart_toEndOf="@+id/tv_integration_time"
            app:layout_constraintTop_toTopOf="@+id/tv_integration_time"
            app:ms_background_color="@color/dark_gray"
            app:ms_popupwindow_height="wrap_content"
            app:ms_popupwindow_maxheight="200dp"
            app:ms_text_color="@android:color/white" />

        <TextView
            android:id="@+id/title_laser_power"
            style="@style/CollectInfoTextStyle"
            android:layout_marginTop="18dp"
            android:text="@string/title_laser_power_with_colon"
            app:layout_constraintEnd_toEndOf="@+id/title_integration_time"
            app:layout_constraintTop_toBottomOf="@+id/title_integration_time" />

        <com.ssraman.control.spinner.MaterialEditSpinner
            android:id="@+id/cb_laser_power"
            android:layout_width="200dp"
            android:layout_height="32dp"
            android:layout_marginStart="1dp"
            android:background="@drawable/input_border_bottom_border"
            android:textColor="@color/txtblackgray"
            android:textSize="16sp"
            android:inputType="number"
            app:layout_constraintBottom_toBottomOf="@+id/title_laser_power"
            app:layout_constraintStart_toEndOf="@+id/title_laser_power"
            app:layout_constraintTop_toTopOf="@+id/title_laser_power"
            app:ms_background_color="@color/dark_gray"
            app:ms_popupwindow_height="wrap_content"
            app:ms_popupwindow_maxheight="200dp"
            app:ms_text_color="@android:color/white"
            tools:layout_editor_absoluteY="89dp" />

        <TextView
            android:id="@+id/title_core_method"
            style="@style/CollectInfoTextStyle"
            android:layout_marginTop="18dp"
            android:text="@string/title_comparison_method_with_colon"
            app:layout_constraintEnd_toEndOf="@+id/title_laser_power"
            app:layout_constraintTop_toBottomOf="@+id/title_laser_power" />

        <com.ssraman.control.spinner.MaterialSpinner
            android:id="@+id/tv_core_method"
            android:layout_width="200dp"
            android:layout_height="32dp"
            android:layout_marginStart="1dp"
            android:background="@drawable/input_border_bottom_border"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/title_core_method"
            app:layout_constraintStart_toEndOf="@+id/title_core_method"
            app:layout_constraintTop_toTopOf="@+id/title_core_method"
            app:ms_background_color="@color/dark_gray"
            app:ms_popupwindow_height="wrap_content"
            app:ms_popupwindow_maxheight="200dp"
            app:ms_text_color="@android:color/white" />

        <TextView
            android:id="@+id/title_batch"
            style="@style/CollectInfoTextStyle"
            android:layout_marginTop="18dp"
            android:text="@string/title_collection_times_with_colon"
            app:layout_constraintEnd_toEndOf="@+id/title_core_method"
            app:layout_constraintTop_toBottomOf="@+id/title_core_method" />

        <EditText
            android:id="@+id/edit_batch"
            style="@style/CollectInfoEditTextStyle"
            android:layout_marginStart="1dp"
            android:inputType="number"
            app:layout_constraintBottom_toBottomOf="@+id/title_batch"
            app:layout_constraintStart_toEndOf="@+id/title_batch"
            app:layout_constraintTop_toTopOf="@+id/title_batch"
            tools:text="@string/hint_please_enter" />

        <TextView
            android:id="@+id/btn_motor_reset"
            style="@style/ButtonStyle2"
            android:text="@string/btn_reset"
            android:textColor="@color/nc_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginBottom="20dp"
            android:drawableTop="@drawable/ic_back_32"
            android:onClick="@{(view)->mpresenter.BtnMotorReSetClick(view)}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/btn_start"
            style="@style/ButtonStyle2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="20dp"
            android:drawableTop="@drawable/ic_start_32"
            android:onClick="@{(view)->mpresenter.BtnStartClick(view)}"
            android:text="@string/btn_detect"
            android:textColor="@color/nc_light"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />


        <RelativeLayout
            android:layout_width="200dp"
            android:layout_height="150dp"
            android:background="@color/nc_sub"
            android:gravity="center_vertical|center_horizontal"
            android:orientation="vertical"
            android:visibility="@{addLibCollectInfoViewModel.isLoading ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.core.widget.ContentLoadingProgressBar
                android:id="@+id/cout_down_bar"
                style="?android:attr/progressBarStyleLarge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true" />

            <TextView
                android:id="@+id/tv_count_down"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/cout_down_bar"
                android:layout_centerInParent="true"
                android:layout_marginTop="5dp"
                android:text="倒计时0S"
                android:textColor="@color/black" />

        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>