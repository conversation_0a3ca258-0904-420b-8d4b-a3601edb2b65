package com.ssraman.drugraman.ui.other;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;

import android.text.InputType;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.jeremyliao.liveeventbus.LiveEventBus;
import com.kongzue.dialog.interfaces.OnInputDialogButtonClickListener;
import com.kongzue.dialog.util.BaseDialog;
import com.kongzue.dialog.util.InputInfo;
import com.kongzue.dialog.v3.InputDialog;
import com.kongzue.dialog.v3.TipDialog;
import com.ssraman.drugraman.BR;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.base.ExBaseFragment;
import com.ssraman.drugraman.databinding.FragmentServerParamBinding;
import com.ssraman.drugraman.ui.vm.ServerParamViewModel;
import com.ssraman.lib_common.mac.DrawerSwichBean;
import com.ssraman.lib_common.mac.SettingPre;
import com.ssraman.lib_common.mac.ToolBarSwichBean;
import com.ssraman.lib_common.utils.StringUtils;
import com.ssraman.lib_common.utils.ToastUtils;


public class ServerParamFragment extends ExBaseFragment<FragmentServerParamBinding, ServerParamViewModel> {
    private AppCompatActivity me = null;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        me = (AppCompatActivity) this.getActivity();
    }

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_server_param;
    }

    @Override
    public int initVariableId() {
        return BR.serverParamViewModel;
    }

    @Override
    public void initData() {
        super.initData();
        binding.setMpresenter(new MPresenter());

    }

    @Override
    public void onResume() {
        super.onResume();
        LiveEventBus.get("toolbar_swich").postAcrossProcess(new ToolBarSwichBean(1, "主菜单"));
        LiveEventBus.get("drawer_swich").postAcrossProcess(new DrawerSwichBean(1, "不使能"));
    }


    public class MPresenter {
        public void BtnMQTTSeverAdressClick(View view) {
            String str_mqtt_adress = SettingPre.getMQTTSeverAdress();
            InputDialog.build(me)
                    .setTitle("MQTT服务器地址设置")
                    .setMessage("")
                    .setInputText(str_mqtt_adress)
                    .setOkButton("确定", new OnInputDialogButtonClickListener() {
                        @Override
                        public boolean onClick(BaseDialog baseDialog, View v, String inputStr) {
                            if (StringUtils.isEmpty(inputStr)) {
                                ToastUtils.showShortSafe("MQTT服务器地址不能为空");
                                return true;
                            } else {
                                SettingPre.setMQTTSeverAdress(inputStr);
                                return false;
                            }
                        }
                    })
                    .setCancelButton("取消")
                    .setHintText("请输入tcp://192.168.1.102:20604")
                    .setInputInfo(new InputInfo()
                            .setMAX_LENGTH(-1)
                            .setInputType(InputType.TYPE_CLASS_TEXT)
                    )
                    .setCancelable(true)
                    .show();
        }

        public void BtnMQTTLoginNameClick(View view) {
            String str_mqtt_loginname = SettingPre.getMQTTLoginName();
            InputDialog.build(me)
                    .setTitle("MQTT登录用户名设置")
                    .setMessage("")
                    .setInputText(str_mqtt_loginname)
                    .setOkButton("确定", new OnInputDialogButtonClickListener() {
                        @Override
                        public boolean onClick(BaseDialog baseDialog, View v, String inputStr) {
                            if (StringUtils.isEmpty(inputStr)) {
                                ToastUtils.showShortSafe("MQTT登录用户名不能为空");
                                return true;
                            } else {
                                SettingPre.setMQTTLoginName(inputStr);
                                return false;
                            }
                        }
                    })
                    .setCancelButton("取消")
                    .setHintText("请输入mqtt登录用户名")
                    .setInputInfo(new InputInfo()
                            .setMAX_LENGTH(-1)
                            .setInputType(InputType.TYPE_CLASS_TEXT)
                    )
                    .setCancelable(true)
                    .show();
        }

        public void BtnMQTTLoginPassWordClick(View view) {
            String str_mqtt_loginpassword = SettingPre.getMQTTLoginPassWord();
            InputDialog.build(me)
                    .setTitle("MQTT登录密码设置")
                    .setMessage("")
                    .setInputText(str_mqtt_loginpassword)
                    .setOkButton("确定", new OnInputDialogButtonClickListener() {
                        @Override
                        public boolean onClick(BaseDialog baseDialog, View v, String inputStr) {
                            if (StringUtils.isEmpty(inputStr)) {
                                ToastUtils.showShortSafe("MQTT登录密码不能为空");
                                return true;
                            } else {
                                SettingPre.setMQTTLoginPassWord(inputStr);
                                return false;
                            }
                        }
                    })
                    .setCancelButton("取消")
                    .setHintText("请输入mqtt登录密码")
                    .setInputInfo(new InputInfo()
                            .setMAX_LENGTH(-1)
                            .setInputType(InputType.TYPE_TEXT_VARIATION_PASSWORD)
                    )
                    .setCancelable(true)
                    .show();
        }
    }
}