package com.ssraman.drugraman.rbac.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限验证注解
 * 用于方法级别的权限控制
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermission {
    
    /**
     * 权限代码
     * 可以指定多个权限代码
     */
    String[] value() default {};
    
    /**
     * 权限代码（别名）
     */
    String[] permissions() default {};
    
    /**
     * 资源类型
     */
    String resource() default "";
    
    /**
     * 操作类型
     */
    String action() default "";
    
    /**
     * 逻辑关系
     * AND: 需要所有权限
     * OR: 需要任一权限
     */
    LogicalOperator logical() default LogicalOperator.AND;
    
    /**
     * 错误消息
     */
    String message() default "权限不足，无法访问该功能";
    
    /**
     * 逻辑操作符枚举
     */
    enum LogicalOperator {
        AND, OR
    }
}
