package com.ssraman.drugraman.newentiry;

import java.io.Serializable;

/**
 * @author: Administrator
 * @date: 2021/11/4
 */
public class AddLibCollectInfo implements Serializable{

    //样品名称
    private String sample_name;
    //PCA采集次数
    private int batch;
    //PCA采集序号
    private int batch_no;
    //容器类别 厚0，薄1
    private String samlpe_controller;
    //积分时间
    private int integration_time;
    //激光功率
    private LaserInfo laser_power;
    //比对方法
    private int core_method;
    //起始波数
    private int start_wave;
    //PCA采集模式
    private boolean PCA_mode;

    private int samlpeController_index=0;
    private int integrationTime_index=9;
    private int integrationUnit_index=1;
    private int laserPower_index=4;
    private int coreMethod_index=0;

    public String getSample_name() {
        return sample_name;
    }

    public void setSample_name(String sample_name) {
        this.sample_name = sample_name;
    }

    public int getBatch() {
        return batch;
    }

    public void setBatch(int batch) {
        this.batch = batch;
    }

    public int getBatch_no() {
        return batch_no;
    }

    public void setBatch_no(int batch_no) {
        this.batch_no = batch_no;
    }

    public String getSamlpe_controller() {
        return samlpe_controller;
    }

    public void setSamlpe_controller(String samlpe_controller) {
        this.samlpe_controller = samlpe_controller;
    }

    public int getIntegration_time() {
        return integration_time;
    }

    public void setIntegration_time(int integration_time) {
        this.integration_time = integration_time;
    }

    public LaserInfo getLaser_power() {
        return laser_power;
    }

    public void setLaser_power(LaserInfo laser_power) {
        this.laser_power = laser_power;
    }

    public int getCore_method() {
        return core_method;
    }

    public void setCore_method(int core_method) {
        this.core_method = core_method;
    }

    public int getStart_wave() {
        return start_wave;
    }

    public void setStart_wave(int start_wave) {
        this.start_wave = start_wave;
    }

    public boolean isPCA_mode() {
        return PCA_mode;
    }

    public void setPCA_mode(boolean PCA_mode) {
        this.PCA_mode = PCA_mode;
    }

    public int getSamlpeController_index() {
        return samlpeController_index;
    }

    public void setSamlpeController_index(int samlpeController_index) {
        this.samlpeController_index = samlpeController_index;
    }

    public int getIntegrationTime_index() {
        return integrationTime_index;
    }

    public void setIntegrationTime_index(int integrationTime_index) {
        this.integrationTime_index = integrationTime_index;
    }

    public int getIntegrationUnit_index() {
        return integrationUnit_index;
    }

    public void setIntegrationUnit_index(int integrationUnit_index) {
        this.integrationUnit_index = integrationUnit_index;
    }

    public int getLaserPower_index() {
        return laserPower_index;
    }

    public void setLaserPower_index(int laserPower_index) {
        this.laserPower_index = laserPower_index;
    }

    public int getCoreMethod_index() {
            return coreMethod_index;
    }

    public void setCoreMethod_index(int coreMethod_index) {
        this.coreMethod_index = coreMethod_index;
    }
}
