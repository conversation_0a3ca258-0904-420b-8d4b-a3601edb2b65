package com.ssraman.drugraman.rbac;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;

import com.ssraman.drugraman.rbac.security.PermissionCache;

import java.util.HashSet;
import java.util.Set;

/**
 * 权限缓存测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class PermissionCacheTest {
    
    private PermissionCache permissionCache;
    private static final Long TEST_USER_ID = 1L;
    private static final Long ANOTHER_USER_ID = 2L;
    
    @Before
    public void setUp() {
        // 使用较短的缓存过期时间进行测试（1分钟）
        permissionCache = new PermissionCache(1);
    }
    
    @Test
    public void testSetAndGetUserPermissions() {
        // 测试设置和获取用户权限
        Set<String> permissions = new HashSet<>();
        permissions.add("DETECTION_EXECUTE");
        permissions.add("REPORT_VIEW");
        
        permissionCache.setUserPermissions(TEST_USER_ID, permissions);
        Set<String> retrievedPermissions = permissionCache.getUserPermissions(TEST_USER_ID);
        
        assertNotNull("获取的权限不应该为空", retrievedPermissions);
        assertEquals("权限数量应该匹配", permissions.size(), retrievedPermissions.size());
        assertTrue("应该包含检测执行权限", retrievedPermissions.contains("DETECTION_EXECUTE"));
        assertTrue("应该包含报告查看权限", retrievedPermissions.contains("REPORT_VIEW"));
    }
    
    @Test
    public void testGetNonExistentUserPermissions() {
        // 测试获取不存在的用户权限
        Set<String> permissions = permissionCache.getUserPermissions(999L);
        assertNull("不存在的用户权限应该返回null", permissions);
    }
    
    @Test
    public void testRemoveUserPermissions() {
        // 测试移除用户权限
        Set<String> permissions = new HashSet<>();
        permissions.add("USER_CREATE");
        
        permissionCache.setUserPermissions(TEST_USER_ID, permissions);
        assertNotNull("设置后应该能获取到权限", permissionCache.getUserPermissions(TEST_USER_ID));
        
        permissionCache.removeUserPermissions(TEST_USER_ID);
        assertNull("移除后应该获取不到权限", permissionCache.getUserPermissions(TEST_USER_ID));
    }
    
    @Test
    public void testClearAllPermissions() {
        // 测试清空所有权限
        Set<String> permissions1 = new HashSet<>();
        permissions1.add("DETECTION_EXECUTE");
        
        Set<String> permissions2 = new HashSet<>();
        permissions2.add("USER_MANAGE");
        
        permissionCache.setUserPermissions(TEST_USER_ID, permissions1);
        permissionCache.setUserPermissions(ANOTHER_USER_ID, permissions2);
        
        assertEquals("缓存大小应该为2", 2, permissionCache.size());
        
        permissionCache.clear();
        
        assertEquals("清空后缓存大小应该为0", 0, permissionCache.size());
        assertNull("清空后应该获取不到第一个用户的权限", permissionCache.getUserPermissions(TEST_USER_ID));
        assertNull("清空后应该获取不到第二个用户的权限", permissionCache.getUserPermissions(ANOTHER_USER_ID));
    }
    
    @Test
    public void testCacheSize() {
        // 测试缓存大小
        assertEquals("初始缓存大小应该为0", 0, permissionCache.size());
        
        Set<String> permissions = new HashSet<>();
        permissions.add("TEST_PERMISSION");
        
        permissionCache.setUserPermissions(TEST_USER_ID, permissions);
        assertEquals("添加一个用户后缓存大小应该为1", 1, permissionCache.size());
        
        permissionCache.setUserPermissions(ANOTHER_USER_ID, permissions);
        assertEquals("添加两个用户后缓存大小应该为2", 2, permissionCache.size());
    }
    
    @Test
    public void testNullUserIdHandling() {
        // 测试空用户ID的处理
        Set<String> permissions = new HashSet<>();
        permissions.add("TEST_PERMISSION");
        
        permissionCache.setUserPermissions(null, permissions);
        assertEquals("设置空用户ID不应该增加缓存大小", 0, permissionCache.size());
        
        Set<String> retrievedPermissions = permissionCache.getUserPermissions(null);
        assertNull("获取空用户ID的权限应该返回null", retrievedPermissions);
        
        permissionCache.removeUserPermissions(null);
        // 不应该抛出异常
    }
    
    @Test
    public void testNullPermissionsHandling() {
        // 测试空权限集合的处理
        permissionCache.setUserPermissions(TEST_USER_ID, null);
        assertEquals("设置空权限集合不应该增加缓存大小", 0, permissionCache.size());
    }
    
    @Test
    public void testCacheStats() {
        // 测试缓存统计信息
        PermissionCache.CacheStats initialStats = permissionCache.getStats();
        assertEquals("初始总条目数应该为0", 0, initialStats.getTotalEntries());
        assertEquals("初始有效条目数应该为0", 0, initialStats.getValidEntries());
        assertEquals("初始过期条目数应该为0", 0, initialStats.getExpiredEntries());
        
        Set<String> permissions = new HashSet<>();
        permissions.add("TEST_PERMISSION");
        
        permissionCache.setUserPermissions(TEST_USER_ID, permissions);
        permissionCache.setUserPermissions(ANOTHER_USER_ID, permissions);
        
        PermissionCache.CacheStats stats = permissionCache.getStats();
        assertEquals("总条目数应该为2", 2, stats.getTotalEntries());
        assertEquals("有效条目数应该为2", 2, stats.getValidEntries());
        assertEquals("过期条目数应该为0", 0, stats.getExpiredEntries());
        assertEquals("命中率应该为100%", 1.0, stats.getHitRate(), 0.01);
    }
    
    @Test
    public void testCacheExpiration() throws InterruptedException {
        // 测试缓存过期（需要等待时间，在实际测试中可能需要调整）
        Set<String> permissions = new HashSet<>();
        permissions.add("TEST_PERMISSION");
        
        permissionCache.setUserPermissions(TEST_USER_ID, permissions);
        assertNotNull("设置后应该能获取到权限", permissionCache.getUserPermissions(TEST_USER_ID));
        
        // 等待缓存过期（这里使用较短的时间进行测试）
        Thread.sleep(61000); // 等待61秒，超过1分钟的缓存时间
        
        Set<String> expiredPermissions = permissionCache.getUserPermissions(TEST_USER_ID);
        assertNull("过期后应该获取不到权限", expiredPermissions);
    }
    
    @Test
    public void testCacheStatsToString() {
        // 测试缓存统计信息的字符串表示
        Set<String> permissions = new HashSet<>();
        permissions.add("TEST_PERMISSION");
        
        permissionCache.setUserPermissions(TEST_USER_ID, permissions);
        
        PermissionCache.CacheStats stats = permissionCache.getStats();
        String statsString = stats.toString();
        
        assertNotNull("统计信息字符串不应该为空", statsString);
        assertTrue("统计信息字符串应该包含总条目数", statsString.contains("total=1"));
        assertTrue("统计信息字符串应该包含有效条目数", statsString.contains("valid=1"));
        assertTrue("统计信息字符串应该包含过期条目数", statsString.contains("expired=0"));
        assertTrue("统计信息字符串应该包含命中率", statsString.contains("hitRate=100.00%"));
    }
    
    @Test
    public void testCacheShutdown() {
        // 测试缓存关闭
        Set<String> permissions = new HashSet<>();
        permissions.add("TEST_PERMISSION");
        
        permissionCache.setUserPermissions(TEST_USER_ID, permissions);
        assertEquals("关闭前缓存大小应该为1", 1, permissionCache.size());
        
        permissionCache.shutdown();
        assertEquals("关闭后缓存大小应该为0", 0, permissionCache.size());
    }
}
