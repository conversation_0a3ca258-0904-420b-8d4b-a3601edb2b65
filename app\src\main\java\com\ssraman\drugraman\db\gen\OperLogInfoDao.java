package com.ssraman.drugraman.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.ssraman.drugraman.db.StringDateConverter;
import java.util.Date;

import com.ssraman.drugraman.db.entity.OperLogInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "OperLogTable".
*/
public class OperLogInfoDao extends AbstractDao<OperLogInfo, Long> {

    public static final String TABLENAME = "OperLogTable";

    /**
     * Properties of entity OperLogInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "Id", true, "Id");
        public final static Property Operator = new Property(1, String.class, "Operator", false, "Operator");
        public final static Property OperatorType = new Property(2, Integer.class, "OperatorType", false, "OperatorType");
        public final static Property OperatorTime = new Property(3, String.class, "OperatorTime", false, "OperatorTime");
        public final static Property OperatorDescription = new Property(4, String.class, "OperatorDescription", false, "OperatorDescription");
        public final static Property Reserve1 = new Property(5, byte[].class, "Reserve1", false, "Reserve");
    }

    private final StringDateConverter OperatorTimeConverter = new StringDateConverter();

    public OperLogInfoDao(DaoConfig config) {
        super(config);
    }
    
    public OperLogInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, OperLogInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        String Operator = entity.getOperator();
        if (Operator != null) {
            stmt.bindString(2, Operator);
        }
 
        Integer OperatorType = entity.getOperatorType();
        if (OperatorType != null) {
            stmt.bindLong(3, OperatorType);
        }
 
        Date OperatorTime = entity.getOperatorTime();
        if (OperatorTime != null) {
            stmt.bindString(4, OperatorTimeConverter.convertToDatabaseValue(OperatorTime));
        }
 
        String OperatorDescription = entity.getOperatorDescription();
        if (OperatorDescription != null) {
            stmt.bindString(5, OperatorDescription);
        }
 
        byte[] Reserve1 = entity.getReserve1();
        if (Reserve1 != null) {
            stmt.bindBlob(6, Reserve1);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, OperLogInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        String Operator = entity.getOperator();
        if (Operator != null) {
            stmt.bindString(2, Operator);
        }
 
        Integer OperatorType = entity.getOperatorType();
        if (OperatorType != null) {
            stmt.bindLong(3, OperatorType);
        }
 
        Date OperatorTime = entity.getOperatorTime();
        if (OperatorTime != null) {
            stmt.bindString(4, OperatorTimeConverter.convertToDatabaseValue(OperatorTime));
        }
 
        String OperatorDescription = entity.getOperatorDescription();
        if (OperatorDescription != null) {
            stmt.bindString(5, OperatorDescription);
        }
 
        byte[] Reserve1 = entity.getReserve1();
        if (Reserve1 != null) {
            stmt.bindBlob(6, Reserve1);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public OperLogInfo readEntity(Cursor cursor, int offset) {
        OperLogInfo entity = new OperLogInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // Id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // Operator
            cursor.isNull(offset + 2) ? null : cursor.getInt(offset + 2), // OperatorType
            cursor.isNull(offset + 3) ? null : OperatorTimeConverter.convertToEntityProperty(cursor.getString(offset + 3)), // OperatorTime
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // OperatorDescription
            cursor.isNull(offset + 5) ? null : cursor.getBlob(offset + 5) // Reserve1
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, OperLogInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setOperator(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setOperatorType(cursor.isNull(offset + 2) ? null : cursor.getInt(offset + 2));
        entity.setOperatorTime(cursor.isNull(offset + 3) ? null : OperatorTimeConverter.convertToEntityProperty(cursor.getString(offset + 3)));
        entity.setOperatorDescription(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setReserve1(cursor.isNull(offset + 5) ? null : cursor.getBlob(offset + 5));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(OperLogInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(OperLogInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(OperLogInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
