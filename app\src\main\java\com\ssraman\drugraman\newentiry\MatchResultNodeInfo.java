package com.ssraman.drugraman.newentiry;

import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/6/28
 */
public class MatchResultNodeInfo extends SampleNode  implements Comparable<MatchResultNodeInfo>  {

    /// <summary>
    /// 匹配成功率
    /// </summary>
    private Double compareRate = 0.0;
    /// <summary>
    /// 匹配成功率
    /// </summary>
    private Double ExRatio = 0.0;
    /// <summary>
    /// 评价率
    /// </summary>
    private Double evaluationRate = 0.0;  //a+b/A+B 根据最大颗粒度的指标，可划分颗粒度越大可能性越大
    /// <summary>
    /// 匹配成功率
    /// </summary>
    private String strRatio ="";
    /// <summary>
    /// 匹配一致的特征峰列表
    /// </summary>
    private List<MatchResultPeakType> comparePointList;
    /// <summary>
    /// 匹配成功的峰点数
    /// </summary>
    private Integer compareCount = 0;
    /// <summary>
    /// 匹配结果
    /// </summary>
    private int matchResult ;
    /// <summary>
    /// 标准置信度
    /// </summary>
    private double Confidence;

    private boolean isNeiBiao;

    private double neibiaoValue;

    private String sampleName="";

    private int FtId;

    private String FtSampleName="";

    public Double getCompareRate() {
        return compareRate;
    }

    public void setCompareRate(double compareRate) {
        this.compareRate = compareRate;
    }

    public Double getExRatio() {
        return ExRatio;
    }

    public void setExRatio(double exRatio) {
        ExRatio = exRatio;
    }

    public Double getEvaluationRate() {
        return evaluationRate;
    }

    public void setEvaluationRate(Double evaluationRate) {
        this.evaluationRate = evaluationRate;
    }

    public String getStrRatio() {
        return strRatio;
    }

    public void setStrRatio(String strRatio) {
        this.strRatio = strRatio;
    }

    public List<MatchResultPeakType> getComparePointList() {
        return comparePointList;
    }

    public void setComparePointList(List<MatchResultPeakType> comparePointList) {
        this.comparePointList = comparePointList;
    }

    public Integer getCompareCount() {
        return compareCount;
    }

    public void setCompareCount(int compareCount) {
        this.compareCount = compareCount;
    }

    public int getMatchResult() {
        return matchResult;
    }

    public void setMatchResult(int matchResult) {
        this.matchResult = matchResult;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public int getFtId() {
        return FtId;
    }

    public void setFtId(int ftId) {
        FtId = ftId;
    }

    public String getFtSampleName() {
        return FtSampleName;
    }

    public void setFtSampleName(String ftSampleName) {
        FtSampleName = ftSampleName;
    }

    public boolean isNeiBiao() {
        return isNeiBiao;
    }

    public void setNeiBiao(boolean neiBiao) {
        isNeiBiao = neiBiao;
    }

    public double getNeibiaoValue() {
        return neibiaoValue;
    }

    public void setNeibiaoValue(double neibiaoValue) {
        this.neibiaoValue = neibiaoValue;
    }

    public double getConfidence() {
        return Confidence;
    }

    public void setConfidence(double confidence) {
        Confidence = confidence;
    }

    @Override
    public int compareTo(MatchResultNodeInfo matchResultNode) {
        int V1 =this.getCompareCount()-matchResultNode.getCompareCount();
        if(V1==0) {
            double V = this.getExRatio() - matchResultNode.getExRatio();
            if (V == 0) {
                return matchResultNode.getCompareRate().compareTo(this.getCompareRate());
            }
            return matchResultNode.getExRatio().compareTo(this.getExRatio());
        }
        return matchResultNode.getCompareCount().compareTo(this.getCompareCount());
    }
}
