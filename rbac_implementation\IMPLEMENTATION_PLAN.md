# RBAC权限系统重构实施方案

## 1. 项目概述

### 1.1 重构目标
- 将现有的简单权限系统重构为完整的RBAC（基于角色的访问控制）系统
- 实现5种标准角色：操作员、审核员、谱图管理员、用户管理员、系统管理员
- 提供统一的权限验证机制和安全的权限管理
- 确保系统的可扩展性和安全性

### 1.2 重构范围
- 数据库表结构重新设计
- 权限验证逻辑重构
- 用户管理功能增强
- API接口重新设计
- 安全机制加强

## 2. 实施阶段规划

### 阶段一：数据库重构（第1-2周）

#### 2.1 新建权限系统表
```sql
-- 创建新的权限系统表
CREATE TABLE tb_user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    salt TEXT NOT NULL,
    email TEXT,
    full_name TEXT,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    login_attempts INTEGER DEFAULT 0,
    locked_until DATETIME
);

CREATE TABLE tb_role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    role_code TEXT UNIQUE NOT NULL,
    role_name TEXT NOT NULL,
    description TEXT,
    level INTEGER NOT NULL,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tb_permission (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    permission_code TEXT UNIQUE NOT NULL,
    permission_name TEXT NOT NULL,
    resource TEXT NOT NULL,
    action TEXT NOT NULL,
    description TEXT,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tb_user_role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    assigned_by INTEGER,
    expires_at DATETIME,
    status INTEGER DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES tb_user(id),
    FOREIGN KEY (role_id) REFERENCES tb_role(id)
);

CREATE TABLE tb_role_permission (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    role_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    assigned_by INTEGER,
    status INTEGER DEFAULT 1,
    FOREIGN KEY (role_id) REFERENCES tb_role(id),
    FOREIGN KEY (permission_id) REFERENCES tb_permission(id)
);

CREATE TABLE tb_user_session (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    device_info TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    status INTEGER DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES tb_user(id)
);

CREATE TABLE tb_audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    resource TEXT,
    details TEXT,
    ip_address TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES tb_user(id)
);
```

#### 2.2 数据迁移脚本
```sql
-- 迁移现有用户数据
INSERT INTO tb_user (username, password_hash, salt, full_name, status, created_at)
SELECT 
    login_name,
    login_pwd, -- 需要重新加密
    '', -- 生成新的salt
    login_name,
    CASE WHEN status = 'active' THEN 1 ELSE 0 END,
    CURRENT_TIMESTAMP
FROM tb_uesr; -- 原表名

-- 创建默认角色
INSERT INTO tb_role (role_code, role_name, description, level) VALUES
('OPERATOR', '操作员', '仅可执行检测操作', 1),
('REVIEWER', '审核员', '检测 + 报告管理', 2),
('SPECTRUM_MANAGER', '谱图管理员', '检测 + 谱图库管理', 3),
('USER_ADMIN', '用户管理员', '仅用户账号管理', 4),
('SYSTEM_ADMIN', '系统管理员', '所有系统权限', 5);

-- 创建权限数据
-- (详细的权限插入脚本...)
```

### 阶段二：核心组件开发（第3-4周）

#### 2.3 实体类和DAO层
- 实现新的实体类（User, Role, Permission等）
- 更新GreenDAO配置生成新的DAO类
- 实现数据访问层接口

#### 2.4 服务层开发
- 实现IUserService接口
- 实现IRoleService接口
- 实现IPermissionService接口
- 实现密码加密和会话管理

#### 2.5 权限验证组件
- 实现PermissionChecker权限检查器
- 实现PermissionCache权限缓存
- 实现SecurityContext安全上下文
- 实现权限拦截器

### 阶段三：API接口重构（第5周）

#### 2.6 API接口开发
- 实现UserManagementApi
- 实现RoleManagementApi
- 实现PermissionManagementApi
- 更新现有业务API的权限验证

#### 2.7 权限注解应用
- 为现有方法添加@RequirePermission注解
- 为现有方法添加@RequireRole注解
- 配置权限拦截器

### 阶段四：UI层重构（第6周）

#### 2.8 登录界面更新
- 增强登录安全性
- 添加会话管理
- 实现自动登出功能

#### 2.9 权限控制界面更新
- 更新菜单权限控制逻辑
- 实现动态权限检查
- 优化用户体验

### 阶段五：测试和部署（第7-8周）

#### 2.10 测试执行
- 单元测试
- 集成测试
- 安全测试
- 性能测试

#### 2.11 部署准备
- 数据备份
- 部署脚本准备
- 回滚方案准备

## 3. 迁移策略

### 3.1 数据迁移策略

#### 3.1.1 用户数据迁移
1. **密码重新加密**：现有明文密码需要使用新的加密算法重新加密
2. **角色映射**：将现有的Priority字段映射到新的角色系统
3. **数据清理**：清理无效和重复的用户数据

#### 3.1.2 权限数据初始化
1. **角色创建**：创建5种标准角色
2. **权限创建**：创建所有系统权限
3. **角色权限关联**：建立角色和权限的关联关系
4. **用户角色分配**：根据现有Priority为用户分配角色

### 3.2 代码迁移策略

#### 3.2.1 渐进式迁移
1. **并行开发**：新旧系统并行运行一段时间
2. **逐步替换**：逐个模块替换权限验证逻辑
3. **兼容性保持**：保持API接口的向后兼容性

#### 3.2.2 权限验证迁移
```java
// 旧的权限验证方式
if (shareViewModel.get_Login_data().getValue().getPriority() > OperatingAuthority.审核员.getValue()) {
    // 允许访问
}

// 新的权限验证方式
@RequirePermission("SPECTRUM_MANAGE")
public void manageSpectrum() {
    // 业务逻辑
}

// 或者程序化验证
if (permissionChecker.hasPermission(userId, PermissionType.SPECTRUM_MANAGE)) {
    // 允许访问
}
```

## 4. 部署步骤

### 4.1 预部署准备

#### 4.1.1 环境准备
1. **备份现有数据库**
2. **准备测试环境**
3. **准备回滚脚本**
4. **通知用户系统维护**

#### 4.1.2 依赖检查
1. **检查数据库版本兼容性**
2. **检查第三方库依赖**
3. **验证配置文件**

### 4.2 部署执行

#### 4.2.1 数据库升级
```bash
# 1. 备份数据库
adb shell "cp /data/data/com.ssraman.drugraman/databases/uesr_database.db /sdcard/backup/"

# 2. 执行数据库升级脚本
# 在应用中执行数据库迁移代码

# 3. 验证数据完整性
# 运行数据验证脚本
```

#### 4.2.2 应用部署
```bash
# 1. 停止现有应用
adb shell am force-stop com.ssraman.drugraman

# 2. 安装新版本
adb install -r DrugRaman_v2.0.apk

# 3. 启动应用
adb shell am start -n com.ssraman.drugraman/.ui.MainActivity

# 4. 验证功能
# 执行自动化测试脚本
```

### 4.3 部署后验证

#### 4.3.1 功能验证
1. **登录功能测试**
2. **权限验证测试**
3. **业务功能测试**
4. **性能测试**

#### 4.3.2 数据验证
1. **用户数据完整性检查**
2. **权限数据正确性检查**
3. **业务数据一致性检查**

## 5. 风险控制

### 5.1 技术风险

#### 5.1.1 数据丢失风险
- **风险描述**：数据迁移过程中可能出现数据丢失
- **控制措施**：
  - 多重备份策略
  - 分步迁移验证
  - 完整的回滚方案

#### 5.1.2 性能风险
- **风险描述**：新权限系统可能影响性能
- **控制措施**：
  - 权限缓存机制
  - 数据库索引优化
  - 性能监控和调优

### 5.2 业务风险

#### 5.2.1 用户体验风险
- **风险描述**：权限变更可能影响用户正常使用
- **控制措施**：
  - 详细的用户培训
  - 渐进式权限迁移
  - 用户反馈收集机制

#### 5.2.2 安全风险
- **风险描述**：权限系统重构可能引入安全漏洞
- **控制措施**：
  - 全面的安全测试
  - 代码安全审查
  - 渗透测试

## 6. 回滚方案

### 6.1 数据回滚
```sql
-- 恢复原始用户表
DROP TABLE tb_user;
ALTER TABLE tb_uesr_backup RENAME TO tb_uesr;

-- 清理新增的权限表
DROP TABLE tb_role;
DROP TABLE tb_permission;
DROP TABLE tb_user_role;
DROP TABLE tb_role_permission;
DROP TABLE tb_user_session;
DROP TABLE tb_audit_log;
```

### 6.2 代码回滚
1. **恢复原始APK版本**
2. **恢复原始配置文件**
3. **重启应用服务**

## 7. 成功标准

### 7.1 功能标准
- [ ] 5种角色权限体系正常工作
- [ ] 所有权限验证功能正常
- [ ] 用户管理功能完整
- [ ] API接口功能正常

### 7.2 性能标准
- [ ] 权限检查响应时间 < 100ms
- [ ] 登录响应时间 < 2s
- [ ] 系统整体性能无明显下降

### 7.3 安全标准
- [ ] 通过安全测试
- [ ] 无权限绕过漏洞
- [ ] 密码安全存储
- [ ] 会话安全管理

## 8. 后续维护

### 8.1 监控机制
- 权限系统性能监控
- 安全事件监控
- 用户行为审计

### 8.2 优化计划
- 权限缓存优化
- 数据库性能优化
- 用户体验优化

### 8.3 扩展计划
- 细粒度权限控制
- 动态权限配置
- 多租户支持
