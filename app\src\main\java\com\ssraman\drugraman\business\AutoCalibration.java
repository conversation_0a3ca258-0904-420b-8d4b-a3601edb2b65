package com.ssraman.drugraman.business;

import com.bestvike.linq.Linq;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.util.MathUtils;
import com.ssraman.drugraman.util.SpecUtils;
import com.ssraman.lib_common.utils.TxtUtil;

import java.util.List;
import java.lang.Exception;

/**
 * Created by Administrator on 2021/8/5.
 */

public class AutoCalibration {

    private boolean autoAdjustOk = false;

    public double[] getAutoAdjust(List<PeakInfo> detectionPeaks) throws Exception
    {
        double[] X = null;
        double[] Y = null;
        int count = 0;
        boolean GetDataEnable = false;

        double[] Lumbdax = new double[detectionPeaks.size()];
        double[] Intensity = new double[detectionPeaks.size()];
        for (int i = 0; i < detectionPeaks.size(); i++)
        {
            //Lumbdax[i] = SpecUtils.ChangeLumbdaToWave((double)(detectionPeaks.get(i).getPeakId()),detectionPeaks.get(i).getSendWave());
            Lumbdax[i] = detectionPeaks.get(i).getPeakId();
            Intensity[i] = detectionPeaks.get(i).getIntensity();
        }
        TxtUtil.saveSpecTxt("原始峰", Lumbdax,Intensity);

            if (detectionPeaks.size() == 0) {
                throw new MyCustomException("检测到任何峰等于0 !",505);
                // showLongToast("检测到任何峰等于0 !");
            }
            if (detectionPeaks.size() > 28 || detectionPeaks.size() < 4) {
                throw new MyCustomException("校验样品值显示不是标准校准液，无法完成校验 !",506);
            }

        ///////////////////////////////////////////////////////////////OTO
        if (!GetDataEnable)
        {
            GetDataEnable = true;
            //double MaxPeak = 1410.0;
            //double MatchLimit = 61.0;
            double[] MatchLimit = new double[] { 46.0, 60.0, 60.0, 60.0, 80.0 };
            double[] MatchLimit2 = new double[] { 30.0, 60.0, 60.0, 60.0 };
            double[] empiricValue = new double[] { 145.0, 360.0, 510.0, 940.0, 1410.0 };
            double[] empiricValue2 = new double[] { 150.0, 400.0, 650.0, 1280.0 };
            boolean type = true;
            PeakInfo findpeak = Linq.of(detectionPeaks).firstOrDefault(p -> Math.abs(p.getPeakId() - empiricValue[4]) <= MatchLimit[4]);
            if (findpeak != null)
            {
                type = true;
            }
            else
            {
                type = false;
            }

            if (type)
            {
                X = new double[5];
                Y = new double[] { 376.0, 915.0, 1374.0, 2249.0, 2940.0 };
                // Y = new double[5] { 379.5, 917.9, 1371.8, 2251.8, 2941.5 };
                count = 5;
            }
            else
            {
                X = new double[4];
                Y = new double[] { 376.0, 915.0, 1374.0, 2249.0 };
                // Y = new double[4] { 379.5, 917.9, 1371.8, 2251.8 };
                count = 4;
            }
            int j = count;
            for (int i = (detectionPeaks.size() - 1); i >= (detectionPeaks.size() - count); i--)
            {
                final int index_j=j-1;
                if (count != 4)
                {
                    PeakInfo findpeak1 = Linq.of(detectionPeaks).firstOrDefault(p -> Math.abs(p.getPeakId() - empiricValue[index_j]) <= MatchLimit[index_j]);
                    if (findpeak1 == null)
                    {
                        //throw new ArgumentOutOfRangeException("校验样品特征峰值显示不是校准液，无法完成校验311 !");
                        GetDataEnable = false;
                        break;
                    }
                    else
                    {
                        X[j - 1] = findpeak1.getPeakId();
                    }
                }
                else
                {
                    PeakInfo findpeak1 = Linq.of(detectionPeaks).firstOrDefault(p -> Math.abs(p.getPeakId() - empiricValue2[index_j]) <= empiricValue2[index_j]);
                    if (findpeak1 == null)
                    {
                        //throw new ArgumentOutOfRangeException("校验样品特征峰值显示不是校准液，无法完成校验312 !");
                        GetDataEnable = false;
                        break;
                    }
                    else
                    {
                        X[j - 1] = findpeak1.getPeakId();
                    }
                }
                // X[j - 1] = adjustPeak[i].Lumbda;
                j--;
                if (j == 0)
                    break;
            }

        }
        /////////USB
        if (!GetDataEnable)
        {
            GetDataEnable = true;
            // double MaxPeak = 1510.0;
            // double MatchLimit = 51.0;
            double[] MatchLimit = new double[] { 51.0, 51.0, 51.0, 51.0, 80.0 };
            double[] MatchLimit2 = new double[] { 51.0, 51.0, 51.0, 51.0 };
            double[] empiricValue = new double[] { 200.0, 450.0, 670.0, 1200.0, 1750.0 };//1817
            double[] empiricValue2 = new double[] { 200.0, 450.0, 670.0, 1200.0 };
            boolean type = true;

            PeakInfo findpeak = Linq.of(detectionPeaks).first(p -> Math.abs(p.getWave() - empiricValue[4]) <= MatchLimit[4]);
            if (findpeak != null)
            {
                type = true;
            }
            else
            {
                type = false;
            }

            if (type)
            {
                X = new double[5];
                Y = new double[] { 376.0, 915.0, 1374.0, 2249.0, 2940.0 };
                // Y = new double[5] { 379.5, 917.9, 1371.8, 2251.8, 2941.5 };
                count = 5;
            }
            else
            {
                X = new double[4];
                Y = new double[] { 376.0, 915.0, 1374.0, 2249.0 };
                // Y = new double[4] { 379.5, 917.9, 1371.8, 2251.8 };
                count = 4;
            }
            int j = count;
            for (int i = (detectionPeaks.size() - 1); i >= (detectionPeaks.size() - count); i--)
            {
                final int index_j=j - 1;
                if (count != 4)
                {
                    PeakInfo findpeak1 = Linq.of(detectionPeaks).first(p -> Math.abs(p.getWave() - empiricValue[index_j]) <= MatchLimit[index_j]);
                    if (findpeak1 == null)
                    {
                        //throw new ArgumentOutOfRangeException("校验样品特征峰值显示不是校准液，无法完成校验321 !");
                        GetDataEnable = false;
                        break;
                    }
                    else
                    {
                        X[j - 1] = SpecUtils.ChangeLumbdaToWave(findpeak1.getLumbda(),findpeak1.getSendWave());
                    }
                }
                else
                {
                    PeakInfo findpeak1 = Linq.of(detectionPeaks).first(p -> Math.abs(p.getWave() - empiricValue2[index_j]) <= MatchLimit2[index_j]);
                    if (findpeak1 == null)
                    {
                        //throw new ArgumentOutOfRangeException("校验样品特征峰值显示不是校准液，无法完成校验322 !");
                        GetDataEnable = false;
                        break;
                    }
                    else
                    {
                        X[j - 1] =  SpecUtils.ChangeLumbdaToWave(findpeak1.getLumbda(),findpeak1.getSendWave());
                    }
                }
                // X[j - 1] = adjustPeak[i].Lumbda;
                j--;
                if (j == 0)
                    break;
            }
        }
        ///////OTO 乙醇(光谱仪范围0-3000  2030)
        if (!GetDataEnable)
        {
            GetDataEnable = true;
            double MaxPeak = 708.0;
            double MatchLimit = 45.0;
            double[] empiricValue = new double[] { 133.0, 300.0, 460.0, 538.0 };
            boolean type = true;
            X = new double[4];
            Y = new double[] { 443.0, 883.0, 1276.0, 1455.0 };
            // Y = new double[4] { 379.5, 917.9, 1371.8, 2251.8 };
            count = 4;

            int j = count;
            for (int i = (detectionPeaks.size() - 1); i >= 0; i--)
            {
                final int index_j=j - 1;
                PeakInfo findpeak1 = Linq.of(detectionPeaks).first(p -> Math.abs(p.getWave() - empiricValue[index_j]) <= MatchLimit);
                if (findpeak1 == null)
                {
                    //throw new ArgumentOutOfRangeException("校验样品特征峰值显示不是校准液，无法完成校验312 !");
                    GetDataEnable = false;
                    break;
                }
                else
                {
                    X[j - 1] =  SpecUtils.ChangeLumbdaToWave(findpeak1.getLumbda(),findpeak1.getSendWave());
                }

                // X[j - 1] = adjustPeak[i].Lumbda;
                j--;
                if (j == 0)
                    break;
            }

        }

        ///////OTO 乙醇(光谱仪范围0-2700（2800）  2050老型号)
        if (!GetDataEnable)
        {
            GetDataEnable = true;
            double MaxPeak = 708.0;
            double MatchLimit = 45.0;
            double[] empiricValue = new double[] { 143.0, 383.0, 602.0, 709.0 };
            boolean type = true;
            X = new double[4];
            Y = new double[] { 443.0, 883.0, 1276.0, 1455.0 };
            // Y = new double[4] { 379.5, 917.9, 1371.8, 2251.8 };
            count = 4;

            int j = count;
            for (int i = (detectionPeaks.size() - 1); i >= 0; i--)
            {
                final int index_j=j - 1;
                PeakInfo findpeak1 = Linq.of(detectionPeaks).first(p -> Math.abs(p.getWave() - empiricValue[index_j]) <= MatchLimit);
                if (findpeak1 == null)
                {
                    //throw new ArgumentOutOfRangeException("校验样品特征峰值显示不是校准液，无法完成校验312 !");
                    GetDataEnable = false;
                    break;
                }
                else
                {
                    X[j - 1] =  SpecUtils.ChangeLumbdaToWave(findpeak1.getLumbda(),findpeak1.getSendWave());
                }

                // X[j - 1] = adjustPeak[i].Lumbda;
                j--;
                if (j == 0)
                    break;
            }

        }
        ///////////
             if (!GetDataEnable)
                throw new MyCustomException("校验样品特征峰值显示不是校准液，无法完成校验5 !",507);


        autoAdjustOk = false;

        double[]  ratio = MathUtils.leastSquareMethod(X, Y, 3);

        TxtUtil.saveSpecTxt("校准峰", X,Y);
        return ratio;

    }

}
