<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="mpresenter"
            type="com.ssraman.drugraman.ui.record.ArchiveRecordsFragment.MPresenter" />

        <variable
            name="archiveRecordsViewModel"
            type="com.ssraman.drugraman.ui.vm.ArchiveRecordsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/nc_main"
        tools:context=".ui.record.ArchiveRecordsFragment">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline13"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.02" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.95" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline16"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.02" />


        <LinearLayout
            android:id="@+id/linearLayout3"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/textView31"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/title_time_with_colon"
                android:textColor="@color/black" />

            <RelativeLayout
                android:id="@+id/relativeLayout"
                android:layout_width="140dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:background="@drawable/shape_stroke_white"
                android:gravity="center"
                app:layout_constraintStart_toEndOf="@+id/textView31"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/txt_cal_start"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingLeft="2dp"
                    android:textColor="@color/black"
                    tools:text="@string/title_date_format" />

                <ImageView
                    android:id="@+id/imv_cal_start"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="6dp"
                    android:onClick="@{(view)->mpresenter.ImvCalStartClick(view)}"
                    android:src="@drawable/ic_calendar_28" />
            </RelativeLayout>

            <TextView
                android:id="@+id/btn_refresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:drawableStart="@drawable/ic_refresh_32"
                android:onClick="@{(view)->mpresenter.BtnRefreshClick(view)}" />

            <ImageView
                android:layout_width="1px"
                android:layout_height="28dp"
                android:layout_gravity="center"
                android:background="@color/dialogSplitIOSLight" />

            <TextView
                android:id="@+id/btn_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:drawableStart="@drawable/ic_filter_32"
                android:onClick="@{(view)->mpresenter.BtnFilterClick(view)}" />

            <ImageView
                android:layout_width="1px"
                android:layout_height="28dp"
                android:layout_gravity="center"
                android:background="@color/nc_light" />

<!--            <TextView-->
<!--                android:id="@+id/btn_rematch"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_gravity="center"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:drawableStart="@drawable/ic_lib_search_32"-->
<!--                android:onClick="@{(view)->mpresenter.BtnRematchClick(view)}" />-->

<!--            <ImageView-->
<!--                android:layout_width="1px"-->
<!--                android:layout_height="28dp"-->
<!--                android:layout_gravity="center"-->
<!--                android:background="@color/dialogSplitIOSLight" />-->

            <TextView
                android:id="@+id/btn_share"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:drawableStart="@drawable/ic_share_32"
                android:onClick="@{(view)->mpresenter.BtnShareClick(view)}" />
        </LinearLayout>


        <com.ssraman.drugraman.sortableview.ArchiveRecordsSorTableView
            android:id="@+id/table_archive_record"
            android:layout_width="355dp"
            android:layout_height="0dp"
            android:descendantFocusability="blocksDescendants"
            app:layout_constraintBottom_toTopOf="@+id/linearLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout3" />

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline14"
            app:layout_constraintStart_toStartOf="@+id/guideline13">

            <CheckBox
                android:id="@+id/chk_select_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="24dp"
                android:paddingLeft="8dp"
                android:text="@string/btn_select_all"
                android:textColor="@color/nc_light"
                android:textSize="24sp"
                android:theme="@style/MyCheckBox"
                tools:text="@string/btn_select_all" />

        </LinearLayout>

        <androidx.core.widget.ContentLoadingProgressBar
            android:id="@+id/add_new_record"
            style="?android:attr/progressBarStyleLarge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="@{archiveRecordsViewModel.isLoading ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>