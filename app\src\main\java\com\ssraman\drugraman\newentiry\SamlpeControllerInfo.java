package com.ssraman.drugraman.newentiry;

import androidx.annotation.NonNull;

/**
 * @author: Administrator
 * @date: 2021/10/15
 */
public class SamlpeControllerInfo {
    private String samlpe_controller;
    private int controller_value;

    public SamlpeControllerInfo(String samlpe_controller, int controller_value) {
        this.samlpe_controller = samlpe_controller;
        this.controller_value = controller_value;
    }

    public String getSamlpe_controller() {
        return samlpe_controller;
    }

    public void setSamlpe_controller(String samlpe_controller) {
        this.samlpe_controller = samlpe_controller;
    }

    public int getController_value() {
        return controller_value;
    }

    public void setController_value(int controller_value) {
        this.controller_value = controller_value;
    }

    @NonNull
    @Override
    public String toString() {
        return samlpe_controller.toString();
    }
}
