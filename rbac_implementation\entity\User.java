package com.ssraman.drugraman.rbac.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.ToMany;
import org.greenrobot.greendao.annotation.Unique;
import org.greenrobot.greendao.annotation.Generated;

import java.util.Date;
import java.util.List;

/**
 * 用户实体类
 * 重构后的用户表，支持完整的RBAC权限系统
 */
@Entity(nameInDb = "tb_user")
public class User {
    
    @Id(autoincrement = true)
    @Property(nameInDb = "id")
    private Long id;
    
    @Property(nameInDb = "username")
    @Unique
    private String username;
    
    @Property(nameInDb = "password_hash")
    private String passwordHash;
    
    @Property(nameInDb = "salt")
    private String salt;
    
    @Property(nameInDb = "email")
    private String email;
    
    @Property(nameInDb = "full_name")
    private String fullName;
    
    @Property(nameInDb = "status")
    private Integer status; // 0:未激活, 1:激活, -1:锁定, -2:已删除
    
    @Property(nameInDb = "created_at")
    private Date createdAt;
    
    @Property(nameInDb = "updated_at")
    private Date updatedAt;
    
    @Property(nameInDb = "last_login")
    private Date lastLogin;
    
    @Property(nameInDb = "login_attempts")
    private Integer loginAttempts; // 登录尝试次数
    
    @Property(nameInDb = "locked_until")
    private Date lockedUntil; // 锁定到期时间
    
    @ToMany(referencedJoinProperty = "userId")
    private List<UserRole> userRoles;
    
    @ToMany(referencedJoinProperty = "userId")
    private List<UserSession> userSessions;

    @Generated(hash = 1909946240)
    public User(Long id, String username, String passwordHash, String salt,
            String email, String fullName, Integer status, Date createdAt,
            Date updatedAt, Date lastLogin, Integer loginAttempts,
            Date lockedUntil) {
        this.id = id;
        this.username = username;
        this.passwordHash = passwordHash;
        this.salt = salt;
        this.email = email;
        this.fullName = fullName;
        this.status = status;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.lastLogin = lastLogin;
        this.loginAttempts = loginAttempts;
        this.lockedUntil = lockedUntil;
    }

    @Generated(hash = 586692638)
    public User() {
    }
    
    // 构造函数
    public User(String username, String passwordHash, String salt, String email, String fullName) {
        this.username = username;
        this.passwordHash = passwordHash;
        this.salt = salt;
        this.email = email;
        this.fullName = fullName;
        this.status = 1; // 默认激活状态
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.loginAttempts = 0;
    }
    
    // 业务方法
    public boolean isActive() {
        return status != null && status == 1;
    }
    
    public boolean isLocked() {
        return status != null && status == -1 || 
               (lockedUntil != null && lockedUntil.after(new Date()));
    }
    
    public boolean isDeleted() {
        return status != null && status == -2;
    }
    
    public void incrementLoginAttempts() {
        this.loginAttempts = (this.loginAttempts == null ? 0 : this.loginAttempts) + 1;
        this.updatedAt = new Date();
    }
    
    public void resetLoginAttempts() {
        this.loginAttempts = 0;
        this.lockedUntil = null;
        this.updatedAt = new Date();
    }
    
    public void lockUser(int lockDurationMinutes) {
        this.status = -1;
        this.lockedUntil = new Date(System.currentTimeMillis() + lockDurationMinutes * 60 * 1000);
        this.updatedAt = new Date();
    }
    
    public void updateLastLogin() {
        this.lastLogin = new Date();
        this.updatedAt = new Date();
    }

    // Getters and Setters
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPasswordHash() {
        return this.passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getSalt() {
        return this.salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFullName() {
        return this.fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getLastLogin() {
        return this.lastLogin;
    }

    public void setLastLogin(Date lastLogin) {
        this.lastLogin = lastLogin;
    }

    public Integer getLoginAttempts() {
        return this.loginAttempts;
    }

    public void setLoginAttempts(Integer loginAttempts) {
        this.loginAttempts = loginAttempts;
    }

    public Date getLockedUntil() {
        return this.lockedUntil;
    }

    public void setLockedUntil(Date lockedUntil) {
        this.lockedUntil = lockedUntil;
    }

    public List<UserRole> getUserRoles() {
        return this.userRoles;
    }

    public void setUserRoles(List<UserRole> userRoles) {
        this.userRoles = userRoles;
    }

    public List<UserSession> getUserSessions() {
        return this.userSessions;
    }

    public void setUserSessions(List<UserSession> userSessions) {
        this.userSessions = userSessions;
    }
}
