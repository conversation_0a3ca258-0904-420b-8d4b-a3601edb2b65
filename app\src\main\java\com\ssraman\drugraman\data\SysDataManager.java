package com.ssraman.drugraman.data;

import com.ssraman.drugraman.db.entity.FtInfo;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.db.entity.SampleTypeInfo;
import com.ssraman.drugraman.newentiry.FtNodeInfo;
import com.ssraman.drugraman.newentiry.MatchNodeInfo;
import com.ssraman.drugraman.newentiry.SampleNode;
import com.ssraman.drugraman.newentiry.SampleNodeInfo;

import java.util.List;

import io.reactivex.Observable;

public interface SysDataManager {
    public Observable<List<SampleTypeInfo>> getMenuList(int ParentId, int type);

    public List<MatchNodeInfo> getMatchListByClassify(Long classifyId);

    public List<MatchNodeInfo> getMatchedListByWave(double[] wave, Long classifyId);

    public Observable<List<SampleTypeInfo>> getAllList();

    public List<SampleNode> getNodeGroupFt(long id);

    public SampleTypeInfo getNodeSampleTypeInfo(long id);

    public List<SampleTypeInfo> getSampleTypeByParentId(long id);

    public SampleTypeInfo getSampleTypeById(long id);

    public long insetSampleType(SampleTypeInfo info);

    public void updateSampleType(SampleTypeInfo info);

    public long saveFtDataToLib(SampleNode info);

    // public Observable<SampleHelpInfo> getSampleHelpInfoBySampleId(long
    // sampleInfoId);

    public Observable<Boolean> deleteFtData(SampleNode deleteData);

    public Observable<Boolean> deletePeakData(List<PeakInfo> deleteData);

    public Observable<Boolean> updateFtData(SampleNode updateData);

    public SampleNodeInfo getSampleNodeData(SampleNodeInfo nodeInfo);

    public Observable<List<SampleTypeInfo>> getAllSampleItem(int directoryType);

    public Observable<FtNodeInfo> getSampleDataBySampleId(int sampleId);

    public Observable<List<SampleTypeInfo>> getSampleTypeByEvaluationMode(int evaluation_mode);

    public FtInfo getFtInfoById(long ft_id);

    public boolean updateFtData(FtInfo update_ft, List<PeakInfo> update_peak_list, List<PeakInfo> delete_peak_list);

    public SampleTypeInfo existsSampleItem(long parent_id, String sample_name);

    public long insertFtInfo(FtInfo add_ftInfo);

    public boolean insertPeakList(List<PeakInfo> peakList);

    public List<FtInfo> getFtListBySampleTypeId(Long id);

    public boolean deleteFtInfo(long ft_id);
}
