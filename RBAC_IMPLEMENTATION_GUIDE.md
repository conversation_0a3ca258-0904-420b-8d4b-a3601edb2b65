# RBAC权限系统实施指南

## 概述

本文档提供了DrugRaman项目中RBAC（基于角色的访问控制）权限系统的完整实施指南。该系统旨在替换现有的简单权限控制机制，提供更加灵活、安全和可扩展的权限管理功能。

## 系统架构

### 核心组件

1. **数据层**
   - `RbacUser` - 用户实体
   - `RbacRole` - 角色实体
   - `RbacPermission` - 权限实体
   - `RbacUserRole` - 用户角色关联
   - `RbacRolePermission` - 角色权限关联
   - `RbacUserSession` - 用户会话

2. **服务层**
   - `IRbacUserService` - 用户服务接口
   - `IRbacRoleService` - 角色服务接口
   - `IRbacPermissionService` - 权限服务接口

3. **安全层**
   - `SecurityContext` - 安全上下文管理
   - `PermissionChecker` - 权限检查器
   - `PermissionCache` - 权限缓存

4. **管理层**
   - `RbacManager` - RBAC管理器
   - `RbacLoginManager` - 登录管理器
   - `UiPermissionController` - UI权限控制器

## 实施步骤

### 第一阶段：数据库重构

1. **创建新的数据库表**
   ```sql
   -- 执行 RbacDatabaseMigration.migrateToRbac() 方法
   ```

2. **数据迁移**
   - 将现有用户数据迁移到新的RBAC表结构
   - 创建默认角色和权限
   - 为现有用户分配相应角色

3. **更新数据库助手**
   - 使用 `RbacDatabaseHelper` 替换默认的数据库助手
   - 在 `DBController` 中集成新的助手

### 第二阶段：核心组件开发

1. **实现服务层**
   - 完成 `RbacUserServiceImpl`、`RbacRoleServiceImpl`、`RbacPermissionServiceImpl`
   - 实现所有接口方法
   - 添加事务支持和错误处理

2. **配置权限检查器**
   - 初始化 `PermissionChecker`
   - 配置权限缓存策略
   - 设置缓存过期时间

3. **集成安全上下文**
   - 在应用启动时初始化 `SecurityContext`
   - 确保线程安全性
   - 实现自动清理机制

### 第三阶段：API接口重构

1. **更新权限检查逻辑**
   - 使用 `PermissionUtils` 替换现有的权限检查
   - 添加权限注解支持
   - 实现统一的错误处理

2. **重构现有界面**
   - 更新 `UserManageFragment`
   - 更新 `AddLibraryFragment`
   - 更新 `MainActivity` 的菜单权限控制

3. **添加新的权限管理API**
   - 角色分配接口
   - 权限查询接口
   - 用户权限管理接口

### 第四阶段：UI层重构

1. **更新登录逻辑**
   - 使用 `RbacLoginManager` 处理用户认证
   - 实现会话管理
   - 添加自动登出功能

2. **实现UI权限控制**
   - 使用 `UiPermissionController` 控制界面元素
   - 根据权限动态显示/隐藏功能
   - 实现权限相关的用户提示

3. **优化用户体验**
   - 提供清晰的权限错误提示
   - 实现权限状态的实时更新
   - 添加权限帮助信息

### 第五阶段：测试和验证

1. **单元测试**
   - 权限检查器测试
   - 权限缓存测试
   - 安全上下文测试

2. **集成测试**
   - 完整登录流程测试
   - 权限验证流程测试
   - 角色层次结构测试

3. **安全测试**
   - 权限绕过测试
   - 会话安全测试
   - 数据访问控制测试

## 配置说明

### 角色配置

```java
// 在应用启动时创建默认角色
RoleType.OPERATOR.getLevel() = 1;        // 操作员
RoleType.REVIEWER.getLevel() = 2;        // 审核员
RoleType.LIBRARY_ADMIN.getLevel() = 3;   // 谱图管理员
RoleType.USER_ADMIN.getLevel() = 4;      // 用户管理员
RoleType.SYSTEM_ADMIN.getLevel() = 5;    // 系统管理员
```

### 权限配置

```java
// 检测相关权限
PermissionType.DETECTION_EXECUTE;       // 执行检测
PermissionType.DETECTION_VIEW;          // 查看检测结果

// 报告相关权限
PermissionType.REPORT_VIEW;             // 查看报告
PermissionType.REPORT_EXPORT;           // 导出报告
PermissionType.REPORT_SIGN;             // 签名报告

// 用户管理权限
PermissionType.USER_VIEW;               // 查看用户
PermissionType.USER_CREATE;             // 创建用户
PermissionType.USER_EDIT;               // 编辑用户
PermissionType.USER_DELETE;             // 删除用户

// 谱图库管理权限
PermissionType.LIBRARY_VIEW;            // 查看谱图库
PermissionType.LIBRARY_MANAGE;          // 管理谱图库

// 系统管理权限
PermissionType.SYSTEM_SETTING;          // 系统设置
PermissionType.LOG_VIEW;                // 查看日志
PermissionType.ROLE_ASSIGN;             // 分配角色
```

## 使用示例

### 权限检查

```java
// 检查权限并显示错误消息
if (PermissionUtils.checkPermissionWithMessage(context, PermissionType.USER_CREATE, "您没有创建用户的权限")) {
    // 执行创建用户操作
    createUser();
}

// 检查角色级别
if (PermissionUtils.checkRoleLevelWithMessage(context, RoleType.SYSTEM_ADMIN)) {
    // 执行系统管理员操作
    performAdminOperation();
}
```

### UI权限控制

```java
// 根据权限控制视图可见性
PermissionUtils.setViewVisibilityByPermission(createUserButton, PermissionType.USER_CREATE);

// 根据角色级别控制视图启用状态
PermissionUtils.setViewEnabledByRoleLevel(adminSettingsButton, RoleType.SYSTEM_ADMIN.getLevel());
```

### 登录管理

```java
// 用户登录
AuthResult authResult = rbacLoginManager.login(username, password);
if (authResult.isSuccess()) {
    // 登录成功，更新UI
    updateUserInterface();
} else {
    // 显示错误信息
    showErrorMessage(authResult.getMessage());
}

// 用户登出
rbacLoginManager.logout();
```

## 注意事项

1. **数据迁移**
   - 在生产环境部署前，务必备份现有数据
   - 测试数据迁移脚本的完整性
   - 验证迁移后的数据一致性

2. **性能优化**
   - 合理配置权限缓存过期时间
   - 避免频繁的数据库查询
   - 使用批量操作提高效率

3. **安全考虑**
   - 定期清理过期会话
   - 实现密码强度验证
   - 记录重要的权限操作日志

4. **兼容性**
   - 保持与现有代码的兼容性
   - 逐步迁移现有功能
   - 提供回退机制

## 故障排除

### 常见问题

1. **权限检查失败**
   - 检查用户是否已登录
   - 验证角色和权限配置
   - 查看权限缓存状态

2. **数据库迁移失败**
   - 检查数据库连接
   - 验证表结构
   - 查看迁移日志

3. **UI权限控制不生效**
   - 确认权限控制器已初始化
   - 检查权限规则配置
   - 验证权限检查逻辑

### 调试技巧

1. **启用详细日志**
   ```java
   Log.setLevel(Log.DEBUG);
   ```

2. **检查安全上下文**
   ```java
   Log.d(TAG, "Current user: " + SecurityContext.getCurrentUser());
   Log.d(TAG, "Current roles: " + SecurityContext.getCurrentRoleCodes());
   Log.d(TAG, "Current permissions: " + SecurityContext.getCurrentPermissionCodes());
   ```

3. **验证权限缓存**
   ```java
   PermissionCache.CacheStats stats = permissionCache.getStats();
   Log.d(TAG, "Cache stats: " + stats.toString());
   ```

## 总结

RBAC权限系统的实施将显著提升DrugRaman项目的安全性和可维护性。通过分阶段的实施方式，可以确保系统的稳定性和用户体验的连续性。

在实施过程中，请严格按照本指南的步骤进行，并在每个阶段完成后进行充分的测试验证。如遇到问题，请参考故障排除部分或联系开发团队。
