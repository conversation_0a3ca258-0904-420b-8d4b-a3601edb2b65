<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="sampleTestItem"
            type="com.ssraman.drugraman.item.SampleTestItem" />
    </data>

    <LinearLayout
        android:layout_width="320dp"
        android:layout_height="52dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="6dp"
        android:background="@color/nc_main"
        android:backgroundResource="@{sampleTestItem.imageRes}"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="16dp"
            android:gravity="center|left"
            android:text="@{sampleTestItem.getName()}"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="16sp"
            tools:text="农药残留" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/icon_lib"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center|right"
            android:layout_marginRight="10dp"
            android:drawableStart="@drawable/ic_item_lib_24"
            android:text="@{sampleTestItem.libType}"
            android:textColor="@color/black"
            tools:text="自定义" />


    </LinearLayout>
</layout>