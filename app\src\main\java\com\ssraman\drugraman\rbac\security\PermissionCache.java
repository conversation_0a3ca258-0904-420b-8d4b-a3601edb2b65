package com.ssraman.drugraman.rbac.security;

import android.os.Handler;
import android.os.Looper;

import java.util.Set;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 权限缓存管理器
 * 提供用户权限的内存缓存功能，提高权限检查性能
 */
public class PermissionCache {
    
    private static final long DEFAULT_CACHE_EXPIRE_MINUTES = 30; // 默认缓存过期时间30分钟
    private static final long CLEANUP_INTERVAL_MINUTES = 10; // 清理间隔10分钟
    
    private final Map<Long, CacheEntry> userPermissionsCache;
    private final Handler cleanupHandler;
    private final Runnable cleanupRunnable;
    private final long cacheExpireMillis;
    
    public PermissionCache() {
        this(DEFAULT_CACHE_EXPIRE_MINUTES);
    }
    
    public PermissionCache(long cacheExpireMinutes) {
        this.userPermissionsCache = new ConcurrentHashMap<>();
        this.cleanupHandler = new Handler(Looper.getMainLooper());
        this.cacheExpireMillis = cacheExpireMinutes * 60 * 1000;
        
        // 创建清理任务
        this.cleanupRunnable = new Runnable() {
            @Override
            public void run() {
                cleanupExpiredEntries();
                // 安排下次清理
                cleanupHandler.postDelayed(this, CLEANUP_INTERVAL_MINUTES * 60 * 1000);
            }
        };
        
        // 启动定期清理任务
        startCleanupTask();
    }
    
    /**
     * 获取用户权限
     * @param userId 用户ID
     * @return 权限代码集合，如果缓存未命中或已过期返回null
     */
    public Set<String> getUserPermissions(Long userId) {
        if (userId == null) {
            return null;
        }
        
        CacheEntry entry = userPermissionsCache.get(userId);
        if (entry == null) {
            return null;
        }
        
        // 检查是否过期
        if (isExpired(entry)) {
            userPermissionsCache.remove(userId);
            return null;
        }
        
        return entry.permissions;
    }
    
    /**
     * 设置用户权限缓存
     * @param userId 用户ID
     * @param permissions 权限代码集合
     */
    public void setUserPermissions(Long userId, Set<String> permissions) {
        if (userId == null || permissions == null) {
            return;
        }
        
        CacheEntry entry = new CacheEntry(permissions, System.currentTimeMillis());
        userPermissionsCache.put(userId, entry);
    }
    
    /**
     * 移除用户权限缓存
     * @param userId 用户ID
     */
    public void removeUserPermissions(Long userId) {
        if (userId != null) {
            userPermissionsCache.remove(userId);
        }
    }
    
    /**
     * 清空所有缓存
     */
    public void clear() {
        userPermissionsCache.clear();
    }
    
    /**
     * 获取缓存大小
     * @return 缓存条目数量
     */
    public int size() {
        return userPermissionsCache.size();
    }
    
    /**
     * 检查缓存条目是否过期
     * @param entry 缓存条目
     * @return 是否过期
     */
    private boolean isExpired(CacheEntry entry) {
        return System.currentTimeMillis() - entry.timestamp > cacheExpireMillis;
    }
    
    /**
     * 启动定期清理任务
     */
    private void startCleanupTask() {
        cleanupHandler.postDelayed(cleanupRunnable, CLEANUP_INTERVAL_MINUTES * 60 * 1000);
    }
    
    /**
     * 清理过期的缓存条目
     */
    private void cleanupExpiredEntries() {
        long currentTime = System.currentTimeMillis();
        userPermissionsCache.entrySet().removeIf(entry -> 
            currentTime - entry.getValue().timestamp > cacheExpireMillis
        );
    }
    
    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    public CacheStats getStats() {
        int totalEntries = userPermissionsCache.size();
        int expiredEntries = 0;
        long currentTime = System.currentTimeMillis();
        
        for (CacheEntry entry : userPermissionsCache.values()) {
            if (currentTime - entry.timestamp > cacheExpireMillis) {
                expiredEntries++;
            }
        }
        
        return new CacheStats(totalEntries, expiredEntries, totalEntries - expiredEntries);
    }
    
    /**
     * 停止缓存管理器
     */
    public void shutdown() {
        cleanupHandler.removeCallbacks(cleanupRunnable);
        clear();
    }
    
    /**
     * 缓存条目内部类
     */
    private static class CacheEntry {
        final Set<String> permissions;
        final long timestamp;
        
        CacheEntry(Set<String> permissions, long timestamp) {
            this.permissions = permissions;
            this.timestamp = timestamp;
        }
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private final int totalEntries;
        private final int expiredEntries;
        private final int validEntries;
        
        public CacheStats(int totalEntries, int expiredEntries, int validEntries) {
            this.totalEntries = totalEntries;
            this.expiredEntries = expiredEntries;
            this.validEntries = validEntries;
        }
        
        public int getTotalEntries() {
            return totalEntries;
        }
        
        public int getExpiredEntries() {
            return expiredEntries;
        }
        
        public int getValidEntries() {
            return validEntries;
        }
        
        public double getHitRate() {
            return totalEntries > 0 ? (double) validEntries / totalEntries : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("CacheStats{total=%d, expired=%d, valid=%d, hitRate=%.2f%%}", 
                totalEntries, expiredEntries, validEntries, getHitRate() * 100);
        }
    }
}
