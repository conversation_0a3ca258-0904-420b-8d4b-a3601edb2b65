<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/nc_main">

    <TextView
        android:id="@+id/textView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:drawableTop="@drawable/ic_filter_32"
        android:text="@string/title_advanced_filtering"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/guideline18"
        app:layout_constraintStart_toStartOf="@+id/guideline18"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.25" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline18"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <LinearLayout
        android:id="@+id/lv1"
        android:layout_width="345dp"
        android:layout_height="60dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guideline5">

        <CheckBox
            android:id="@+id/chk_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="5dp"
            android:text=""
            android:theme="@style/MyCheckBox" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/title_from_with_colon"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <RelativeLayout
            android:layout_width="110dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:background="@drawable/shape_stroke_white"
            android:gravity="center"
            app:layout_constraintStart_toEndOf="@+id/textView31"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/txt_cal_start"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingLeft="2dp"
                android:textColor="@color/black"
                android:textSize="14sp"
                tools:text="@string/title_date_format" />

            <ImageView
                android:id="@+id/imv_cal_start"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="6dp"
                android:src="@drawable/ic_calendar_28" />
        </RelativeLayout>

        <TextView
            android:id="@+id/textView32"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:paddingLeft="5dp"
            android:text="@string/title_to_with_colon"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <RelativeLayout
            android:layout_width="110dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:background="@drawable/shape_stroke_white"
            android:gravity="center"
            app:layout_constraintStart_toEndOf="@+id/textView31"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/txt_cal_end"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingLeft="2dp"
                android:textColor="@color/black"
                android:textSize="14sp"
                tools:text="@string/title_date_format" />

            <ImageView
                android:id="@+id/imv_cal_end"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="6dp"
                android:src="@drawable/ic_calendar_28" />
        </RelativeLayout>
    </LinearLayout>


    <LinearLayout
        android:id="@+id/lv2"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lv1">

        <CheckBox
            android:id="@+id/chk_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="5dp"
            android:text=""
            android:theme="@style/MyCheckBox" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="14dp"
            android:text="@string/title_category_with_colon"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <com.ssraman.control.spinner.MaterialSpinner
            android:id="@+id/ettype"
            android:layout_width="216dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:background="@drawable/input_border_bottom_border"
            android:textSize="20sp"
            app:ms_background_color="@color/dark_gray"
            app:ms_popupwindow_height="wrap_content"
            app:ms_popupwindow_maxheight="200dp"
            app:ms_text_color="@android:color/white"
            tools:layout_editor_absoluteX="129dp"
            tools:layout_editor_absoluteY="69dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lv3"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lv2">

        <CheckBox
            android:id="@+id/chk_operater"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="5dp"
            android:text=""
            android:theme="@style/MyCheckBox" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/title_operator_with_colon"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <com.ssraman.control.spinner.MaterialSpinner
            android:id="@+id/etuser"
            android:layout_width="216dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:background="@drawable/input_border_bottom_border"
            android:textSize="20sp"
            app:ms_background_color="@color/dark_gray"
            app:ms_popupwindow_height="wrap_content"
            app:ms_popupwindow_maxheight="200dp"
            app:ms_text_color="@android:color/white"
            tools:layout_editor_absoluteX="129dp"
            tools:layout_editor_absoluteY="69dp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/underline1"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="12dp"
        android:background="@color/nc_light"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lv3" />

    <LinearLayout
        android:id="@+id/box_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/underline1">

        <TextView
            android:id="@+id/btn_ofilter_cancel"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/button_dialog_ios_left_light"
            android:clickable="true"
            android:gravity="center"
            android:text="@string/btn_cancel"
            android:textColor="@color/nc_light"
            android:textSize="16sp" />

        <ImageView
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/nc_light" />

        <TextView
            android:id="@+id/btn_ofilter_ok"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/button_dialog_ios_right_light"
            android:clickable="true"
            android:gravity="center"
            android:text="@string/btn_confirm"
            android:textColor="@color/nc_light"
            android:textSize="16sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
