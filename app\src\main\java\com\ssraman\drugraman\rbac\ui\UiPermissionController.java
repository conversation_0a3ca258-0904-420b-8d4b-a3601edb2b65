package com.ssraman.drugraman.rbac.ui;

import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;

import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.manager.RbacManager;

import java.util.HashMap;
import java.util.Map;

/**
 * UI权限控制器
 * 根据用户权限动态控制界面元素的可见性和可用性
 */
public class UiPermissionController {
    
    private final RbacManager rbacManager;
    private final Map<View, PermissionRule> viewPermissionRules;
    private final Map<Integer, PermissionRule> menuPermissionRules;
    
    public UiPermissionController() {
        this.rbacManager = RbacManager.getInstance();
        this.viewPermissionRules = new HashMap<>();
        this.menuPermissionRules = new HashMap<>();
    }
    
    /**
     * 为视图设置权限规则
     * @param view 视图
     * @param permissionType 权限类型
     * @param controlType 控制类型
     */
    public void setViewPermission(View view, PermissionType permissionType, ControlType controlType) {
        PermissionRule rule = new PermissionRule(permissionType, null, 0, controlType);
        viewPermissionRules.put(view, rule);
        applyViewPermission(view, rule);
    }
    
    /**
     * 为视图设置角色级别规则
     * @param view 视图
     * @param roleType 角色类型
     * @param controlType 控制类型
     */
    public void setViewRoleLevel(View view, RoleType roleType, ControlType controlType) {
        PermissionRule rule = new PermissionRule(null, roleType, roleType.getLevel(), controlType);
        viewPermissionRules.put(view, rule);
        applyViewPermission(view, rule);
    }
    
    /**
     * 为视图设置最低角色级别规则
     * @param view 视图
     * @param minLevel 最低级别
     * @param controlType 控制类型
     */
    public void setViewMinRoleLevel(View view, int minLevel, ControlType controlType) {
        PermissionRule rule = new PermissionRule(null, null, minLevel, controlType);
        viewPermissionRules.put(view, rule);
        applyViewPermission(view, rule);
    }
    
    /**
     * 为菜单项设置权限规则
     * @param menuItemId 菜单项ID
     * @param permissionType 权限类型
     */
    public void setMenuPermission(int menuItemId, PermissionType permissionType) {
        PermissionRule rule = new PermissionRule(permissionType, null, 0, ControlType.VISIBILITY);
        menuPermissionRules.put(menuItemId, rule);
    }
    
    /**
     * 为菜单项设置角色级别规则
     * @param menuItemId 菜单项ID
     * @param roleType 角色类型
     */
    public void setMenuRoleLevel(int menuItemId, RoleType roleType) {
        PermissionRule rule = new PermissionRule(null, roleType, roleType.getLevel(), ControlType.VISIBILITY);
        menuPermissionRules.put(menuItemId, rule);
    }
    
    /**
     * 应用所有视图权限规则
     */
    public void applyAllViewPermissions() {
        for (Map.Entry<View, PermissionRule> entry : viewPermissionRules.entrySet()) {
            applyViewPermission(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 应用菜单权限规则
     * @param menu 菜单
     */
    public void applyMenuPermissions(Menu menu) {
        for (Map.Entry<Integer, PermissionRule> entry : menuPermissionRules.entrySet()) {
            MenuItem menuItem = menu.findItem(entry.getKey());
            if (menuItem != null) {
                applyMenuItemPermission(menuItem, entry.getValue());
            }
        }
    }
    
    /**
     * 应用视图权限
     * @param view 视图
     * @param rule 权限规则
     */
    private void applyViewPermission(View view, PermissionRule rule) {
        boolean hasPermission = checkPermission(rule);
        
        switch (rule.controlType) {
            case VISIBILITY:
                view.setVisibility(hasPermission ? View.VISIBLE : View.GONE);
                break;
            case ENABLED:
                view.setEnabled(hasPermission);
                break;
            case ALPHA:
                view.setAlpha(hasPermission ? 1.0f : 0.3f);
                break;
        }
    }
    
    /**
     * 应用菜单项权限
     * @param menuItem 菜单项
     * @param rule 权限规则
     */
    private void applyMenuItemPermission(MenuItem menuItem, PermissionRule rule) {
        boolean hasPermission = checkPermission(rule);
        menuItem.setVisible(hasPermission);
        menuItem.setEnabled(hasPermission);
    }
    
    /**
     * 检查权限
     * @param rule 权限规则
     * @return 是否有权限
     */
    private boolean checkPermission(PermissionRule rule) {
        if (rule.permissionType != null) {
            return rbacManager.hasPermission(rule.permissionType);
        } else if (rule.roleType != null) {
            return rbacManager.hasRole(rule.roleType);
        } else if (rule.minLevel > 0) {
            return rbacManager.hasRoleLevel(rule.minLevel);
        }
        return true; // 没有权限要求，默认允许
    }
    
    /**
     * 批量设置视图组的权限
     * @param viewGroup 视图组
     * @param permissionType 权限类型
     * @param controlType 控制类型
     * @param recursive 是否递归应用到子视图
     */
    public void setViewGroupPermission(ViewGroup viewGroup, PermissionType permissionType, 
                                     ControlType controlType, boolean recursive) {
        setViewPermission(viewGroup, permissionType, controlType);
        
        if (recursive) {
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                View child = viewGroup.getChildAt(i);
                if (child instanceof ViewGroup) {
                    setViewGroupPermission((ViewGroup) child, permissionType, controlType, true);
                } else {
                    setViewPermission(child, permissionType, controlType);
                }
            }
        }
    }
    
    /**
     * 移除视图的权限规则
     * @param view 视图
     */
    public void removeViewPermission(View view) {
        viewPermissionRules.remove(view);
        // 恢复默认状态
        view.setVisibility(View.VISIBLE);
        view.setEnabled(true);
        view.setAlpha(1.0f);
    }
    
    /**
     * 移除菜单项的权限规则
     * @param menuItemId 菜单项ID
     */
    public void removeMenuPermission(int menuItemId) {
        menuPermissionRules.remove(menuItemId);
    }
    
    /**
     * 清除所有权限规则
     */
    public void clearAllPermissions() {
        viewPermissionRules.clear();
        menuPermissionRules.clear();
    }
    
    /**
     * 权限规则内部类
     */
    private static class PermissionRule {
        final PermissionType permissionType;
        final RoleType roleType;
        final int minLevel;
        final ControlType controlType;
        
        PermissionRule(PermissionType permissionType, RoleType roleType, int minLevel, ControlType controlType) {
            this.permissionType = permissionType;
            this.roleType = roleType;
            this.minLevel = minLevel;
            this.controlType = controlType;
        }
    }
    
    /**
     * 控制类型枚举
     */
    public enum ControlType {
        VISIBILITY, // 控制可见性
        ENABLED,    // 控制启用状态
        ALPHA       // 控制透明度
    }
}
