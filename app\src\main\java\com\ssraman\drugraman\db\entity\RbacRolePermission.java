package com.ssraman.drugraman.db.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.ToOne;
import org.greenrobot.greendao.annotation.Generated;

import java.util.Date;

/**
 * RBAC角色权限关联实体类
 * 管理角色和权限的多对多关系
 */
@Entity(nameInDb = "tb_rbac_role_permission")
public class RbacRolePermission {
    
    @Id(autoincrement = true)
    @Property(nameInDb = "id")
    private Long id;
    
    @Property(nameInDb = "role_id")
    private Long roleId;
    
    @Property(nameInDb = "permission_id")
    private Long permissionId;
    
    @Property(nameInDb = "assigned_at")
    private Date assignedAt;
    
    @Property(nameInDb = "assigned_by")
    private Long assignedBy; // 分配者用户ID
    
    @Property(nameInDb = "status")
    private Integer status; // 0:禁用, 1:启用
    
    @ToOne(joinProperty = "roleId")
    private RbacRole role;
    
    @ToOne(joinProperty = "permissionId")
    private RbacPermission permission;

    @Generated(hash = 1378349073)
    public RbacRolePermission(Long id, Long roleId, Long permissionId,
            Date assignedAt, Long assignedBy, Integer status) {
        this.id = id;
        this.roleId = roleId;
        this.permissionId = permissionId;
        this.assignedAt = assignedAt;
        this.assignedBy = assignedBy;
        this.status = status;
    }

    @Generated(hash = 1169170269)
    public RbacRolePermission() {
    }
    
    // 构造函数
    public RbacRolePermission(Long roleId, Long permissionId, Long assignedBy) {
        this.roleId = roleId;
        this.permissionId = permissionId;
        this.assignedBy = assignedBy;
        this.assignedAt = new Date();
        this.status = 1; // 默认启用
    }
    
    // 业务方法
    public boolean isActive() {
        return status != null && status == 1;
    }
    
    public void activate() {
        this.status = 1;
    }
    
    public void deactivate() {
        this.status = 0;
    }

    // Getters and Setters
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRoleId() {
        return this.roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getPermissionId() {
        return this.permissionId;
    }

    public void setPermissionId(Long permissionId) {
        this.permissionId = permissionId;
    }

    public Date getAssignedAt() {
        return this.assignedAt;
    }

    public void setAssignedAt(Date assignedAt) {
        this.assignedAt = assignedAt;
    }

    public Long getAssignedBy() {
        return this.assignedBy;
    }

    public void setAssignedBy(Long assignedBy) {
        this.assignedBy = assignedBy;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public RbacRole getRole() {
        return this.role;
    }

    public void setRole(RbacRole role) {
        this.role = role;
    }

    public RbacPermission getPermission() {
        return this.permission;
    }

    public void setPermission(RbacPermission permission) {
        this.permission = permission;
    }
}
