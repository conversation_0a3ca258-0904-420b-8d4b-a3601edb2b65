package com.ssraman.drugraman.sortableview;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;

import androidx.core.content.ContextCompat;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.newentiry.MatchResultNodeInfo;

import de.codecrafters.tableview.SortableTableView;
import de.codecrafters.tableview.model.TableColumnWeightModel;
import de.codecrafters.tableview.toolkit.SimpleTableHeaderAdapter;
import de.codecrafters.tableview.toolkit.SortStateViewProviders;
import de.codecrafters.tableview.toolkit.TableDataRowBackgroundProviders;

/**
 * @author: Administrator
 * @date: 2021/6/29
 */
public class CorrelationStandardTableView extends SortableTableView<MatchResultNodeInfo> {
    public CorrelationStandardTableView(final Context context) {
        this(context, null);
    }

    public CorrelationStandardTableView(final Context context,final AttributeSet attributes) {
        this(context, attributes, android.R.attr.listViewStyle);
    }

    public CorrelationStandardTableView(final Context context,final AttributeSet attributes,final int styleAttributes) {
        super(context, attributes, styleAttributes);

        final SimpleTableHeaderAdapter simpleTableHeaderAdapter = new SimpleTableHeaderAdapter(context, R.string.str_No, R.string.str_correlation, R.string.str_compound_name, R.string.str_detail);
        simpleTableHeaderAdapter.setTextColor(ContextCompat.getColor(context, R.color.table_header_text2));
        simpleTableHeaderAdapter.setPaddings(5,5,5,5);
        simpleTableHeaderAdapter.setTextSize(16);
        simpleTableHeaderAdapter.setGravity(Gravity.CENTER);
        setHeaderAdapter(simpleTableHeaderAdapter);

        final int rowColorEven = ContextCompat.getColor(context, R.color.table_data_row_even2);
        final int rowColorOdd = ContextCompat.getColor(context, R.color.table_data_row_odd2);
        final int rowColorSelected = ContextCompat.getColor(context, R.color.table_data_row_selected2);
        setDataRowBackgroundProvider(TableDataRowBackgroundProviders.alternatingRowColors(rowColorEven, rowColorOdd,rowColorSelected));
        final int cowHeaderColor = ContextCompat.getColor(context, R.color.table_data_cow_header2);
        setHeaderBackgroundColor(cowHeaderColor);
        setHeaderSortStateViewProvider(SortStateViewProviders.brightArrows());

        final TableColumnWeightModel tableColumnWeightModel = new TableColumnWeightModel(4);
        tableColumnWeightModel.setColumnWeight(0, 1);
        tableColumnWeightModel.setColumnWeight(1, 2);
        tableColumnWeightModel.setColumnWeight(2, 3);
        tableColumnWeightModel.setColumnWeight(3, 2);
        setColumnModel(tableColumnWeightModel);

    }
}
