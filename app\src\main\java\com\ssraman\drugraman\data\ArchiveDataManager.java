package com.ssraman.drugraman.data;

import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;
import com.ssraman.drugraman.db.entity.CalibrationFtInfo;
import com.ssraman.drugraman.db.entity.OperLogInfo;
import com.ssraman.drugraman.db.entity.SpectralDataInfo;
import com.ssraman.drugraman.newentiry.FilterInfo;
import com.ssraman.drugraman.newentiry.LogFilterInfo;
import com.ssraman.drugraman.newentiry.PageInfo;

import java.util.Date;
import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Observable;

/**
 * @author: Administrator
 * @date: 2021/10/20
 */
public interface ArchiveDataManager {

    public Completable insertLog(OperLogInfo logInfo);

    public long insertLogByRe(OperLogInfo logInfo);

    public Completable deleteLog(OperLogInfo logInfo);

    public Completable updateLog(OperLogInfo logInfo);

    public Observable<List<OperLogInfo>> getLogRecordListBySimple(PageInfo pageInfo, Date start, Date end);

    public Observable<List<OperLogInfo>> getLogRecordListByFilter(PageInfo pageInfo, LogFilterInfo filter);

    public Observable<List<ArchiveRecordsInfo>> getRecordListBySimple(PageInfo pageInfo, Date start, Date end);

    public Observable<List<ArchiveRecordsInfo>> getRecordListByFilter(PageInfo pageInfo, FilterInfo filter);

    public Observable<SpectralDataInfo> getSpectralDataInfoById(long id);

    public Observable<Boolean> updateRecordData(ArchiveRecordsInfo archiveRecordsInfo);

    public SpectralDataInfo getSpectralDataById(long id);

    public void updateMqttRecordData(ArchiveRecordsInfo archiveRecordsInfo);

    ////////////////////////////
    public long DetectionResultSave(SpectralDataInfo spectralDataInfo, ArchiveRecordsInfo saveDetectionInfo);

    public long DetectionResultUpdate(SpectralDataInfo spectralDataInfo, ArchiveRecordsInfo saveDetectionInfo);

    public boolean updateUploadOkById(int record_id);

    /////////////////////////////////////////////////
    public CalibrationFtInfo getCalibrationFtInfo();

    public long insetCalibrationFtInfo(CalibrationFtInfo info);

    public void updateCalibrationFtInfo(CalibrationFtInfo info);

}
