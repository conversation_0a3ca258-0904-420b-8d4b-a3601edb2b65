package com.ssraman.drugraman.custom;

import android.graphics.drawable.Drawable;

import androidx.core.content.res.ResourcesCompat;

import com.bestvike.linq.Linq;
import com.github.mikephil.charting.data.Entry;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.business.SpectrumDataProcessUtil;
import com.ssraman.drugraman.db.entity.CalibrationFtInfo;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.newentiry.MatchFtInfo;
import com.ssraman.drugraman.newentiry.MatchResultNodeInfo;
import com.ssraman.drugraman.newentiry.SampleNode;
import com.ssraman.drugraman.util.MathUtils;
import com.ssraman.lib_common.utils.Utils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/10/22
 */
public class SpectrumUtils {
    // 是否只有一种谱图，默认为true
    private boolean isSingleSpectrum = true;

    /**
     * 设置是否为单一谱图模式
     * 
     * @param isSingle true表示只有一种谱图，false表示有多种谱图比较
     */
    public void setSingleSpectrum(boolean isSingle) {
        this.isSingleSpectrum = isSingle;
    }

    /**
     * 获取当前是否为单一谱图模式
     * 
     * @return true表示只有一种谱图，false表示有多种谱图比较
     */
    public boolean isSingleSpectrum() {
        return this.isSingleSpectrum;
    }

    public SpectrumLineData creatSpecLineData(double[] waves, double[] intensitys, List<PeakInfo> peakList,
            String title) {
        if (waves.length != intensitys.length) {
            // Throwable
        }
        SpectrumLineData spectrumLineData = new SpectrumLineData();
        double max_intensity = 0.0;
        List<Entry> entries = new ArrayList<>();
        for (int i = 0; i < waves.length; i++) {
            Entry entry = new Entry((float) waves[i], (float) intensitys[i]);
            entries.add(entry);
            if (max_intensity < intensitys[i]) {
                max_intensity = intensitys[i];
            }
        }

        try {
            // 根据是否是单一谱图选择合适的图标颜色
            int iconResId = isSingleSpectrum ? R.drawable.circle_shape_dark : R.drawable.circle_shape;
            Drawable icon = ResourcesCompat.getDrawable(Utils.getContext().getResources(), iconResId, null);
            for (PeakInfo peakinfo : peakList) {
                entries.get(peakinfo.getPeakId()).setIcon(icon);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            //
        }

        spectrumLineData.setLabel(title);
        spectrumLineData.setEntries(entries);
        spectrumLineData.setMaxIntensity(max_intensity);
        return spectrumLineData;
    }

    public List<SpectrumLineData> creatMatchSpecLineData(List<MatchResultNodeInfo> matchResultList,
            double max_intensity) {
        SpectrumDataProcessUtil dataProcessTool = new SpectrumDataProcessUtil();
        List<SpectrumLineData> matchSpecList = new ArrayList<>();
        for (int i = 0; i < matchResultList.size(); i++) {
            MatchResultNodeInfo matchResultNodeInfo = matchResultList.get(i);
            SampleNode t_currentSampleNode = dataProcessTool.LibDataProcess(matchResultNodeInfo);
            double[] nor_intensity = t_currentSampleNode.getSpcInfo().getAutoLineIntensity();
            double my_max_intensity = MathUtils.searchMax(nor_intensity);
            for (int j = 0; j < nor_intensity.length; j++) {
                // nor_intensity[j] = nor_intensity[j] / max_intensity;
                nor_intensity[j] = (nor_intensity[j] / my_max_intensity) * max_intensity + max_intensity * 0.2;
            }
            SpectrumLineData spec_data = creatLibSpecLineData(t_currentSampleNode.getSpcInfo().getAutoLineSourceWave(),
                    nor_intensity, t_currentSampleNode.getSpcInfo().getAutoLinePeakList(),
                    matchResultNodeInfo.getSampleName());
            matchSpecList.add(spec_data);
        }

        return matchSpecList;
    }

    public List<SpectrumLineData> creatNMatchSpecLineData(List<MatchResultNodeInfo> matchResultList,
            double max_intensity) {
        SpectrumDataProcessUtil dataProcessTool = new SpectrumDataProcessUtil();
        List<SpectrumLineData> matchSpecList = new ArrayList<>();
        for (int i = 0; i < matchResultList.size(); i++) {
            MatchResultNodeInfo matchResultNodeInfo = matchResultList.get(i);

            SampleNode t_currentSampleNode = dataProcessTool.LibDataProcess(matchResultNodeInfo);
            double[] nor_intensity = t_currentSampleNode.getSpcInfo().getAutoLineIntensity();
            double my_max_intensity = MathUtils.searchMax(nor_intensity);
            for (int j = 0; j < nor_intensity.length; j++) {
                nor_intensity[j] = (nor_intensity[j] / my_max_intensity) * max_intensity + max_intensity * 0.2;
            }
            SpectrumLineData spec_data = creatSpecSysLibData(t_currentSampleNode.getSpcInfo().getAutoLineSourceWave(),
                    nor_intensity, matchResultNodeInfo.getSampleName());
            matchSpecList.add(spec_data);
        }

        return matchSpecList;
    }

    public List<SpectrumLineData> creatMatchSpecLineDataTwo(List<MatchResultNodeInfo> matchResultList,
            double max_intensity) {
        SpectrumDataProcessUtil dataProcessTool = new SpectrumDataProcessUtil();
        List<SpectrumLineData> matchSpecList = new ArrayList<>();
        for (int i = 0; i < matchResultList.size(); i++) {
            MatchResultNodeInfo matchResultNodeInfo = matchResultList.get(i);
            SampleNode t_currentSampleNode = dataProcessTool.LibDataProcessNoline(matchResultNodeInfo);
            double[] nor_intensity = t_currentSampleNode.getSpcInfo().getAutoLineIntensity();
            double my_max_intensity = MathUtils.searchMax(nor_intensity);
            for (int j = 0; j < nor_intensity.length; j++) {
                // nor_intensity[j] = nor_intensity[j] / max_intensity;
                nor_intensity[j] = (nor_intensity[j] / my_max_intensity) * max_intensity + max_intensity * 0.2;
            }
            SpectrumLineData spec_data = creatLibSpecLineData(t_currentSampleNode.getSpcInfo().getAutoLineSourceWave(),
                    nor_intensity, t_currentSampleNode.getSpcInfo().getAutoLinePeakList(),
                    matchResultNodeInfo.getSampleName());
            matchSpecList.add(spec_data);
        }

        return matchSpecList;
    }

    public List<SpectrumLineData> creatNMatchSpecLineDataTwo(List<MatchResultNodeInfo> matchResultList,
            double max_intensity) {
        SpectrumDataProcessUtil dataProcessTool = new SpectrumDataProcessUtil();
        List<SpectrumLineData> matchSpecList = new ArrayList<>();
        for (int i = 0; i < matchResultList.size(); i++) {
            MatchResultNodeInfo matchResultNodeInfo = matchResultList.get(i);

            SampleNode t_currentSampleNode = dataProcessTool.LibDataProcessNoline(matchResultNodeInfo);
            double[] nor_intensity = t_currentSampleNode.getSpcInfo().getAutoLineIntensity();
            double my_max_intensity = MathUtils.searchMax(nor_intensity);
            for (int j = 0; j < nor_intensity.length; j++) {
                nor_intensity[j] = (nor_intensity[j] / my_max_intensity) * max_intensity + max_intensity * 0.2;
            }
            SpectrumLineData spec_data = creatSpecSysLibData(t_currentSampleNode.getSpcInfo().getAutoLineSourceWave(),
                    nor_intensity, matchResultNodeInfo.getSampleName());
            matchSpecList.add(spec_data);
        }

        return matchSpecList;
    }

    public SpectrumLineData creatVerificationSpecLineData(CalibrationFtInfo calibrationFtInfo, double max_intensity) {
        SpectrumDataProcessUtil dataProcessTool = new SpectrumDataProcessUtil();
        SpectrumLineData matchSpe;

        SampleNode t_currentSampleNode = dataProcessTool.LibDataProcess3(calibrationFtInfo);
        double[] nor_intensity = t_currentSampleNode.getSpcInfo().getAutoLineIntensity();
        double my_max_intensity = MathUtils.searchMax(nor_intensity);
        for (int j = 0; j < nor_intensity.length; j++) {
            nor_intensity[j] = (nor_intensity[j] / my_max_intensity) * max_intensity + max_intensity * 0.2;
        }
        SpectrumLineData spec_data = creatSpecSysLibData(t_currentSampleNode.getSpcInfo().getAutoLineSourceWave(),
                nor_intensity, calibrationFtInfo.getSampleName());

        return spec_data;
    }

    public SpectrumLineData creatShowSpecLineData(MatchFtInfo showFtInfo) {
        SpectrumDataProcessUtil dataProcessTool = new SpectrumDataProcessUtil();
        SpectrumLineData showSpec = null;
        MatchFtInfo t_showFtInfo = dataProcessTool.LibDataProcess2(showFtInfo);
        showSpec = creatLibSpecLineData(t_showFtInfo.getAutoLineSourceWave(), t_showFtInfo.getAutoLineIntensity(),
                t_showFtInfo.getUsePeakList(), t_showFtInfo.getFtInfo().getSampleName());
        return showSpec;
    }

    public SpectrumLineData creatNShowSpecLineData(MatchFtInfo showFtInfo) {
        SpectrumDataProcessUtil dataProcessTool = new SpectrumDataProcessUtil();
        SpectrumLineData showSpec = null;
        // Drawable icon =
        // ResourcesCompat.getDrawable(Utils.getContext().getResources(),
        // R.drawable.circle_shape, null);
        MatchFtInfo t_showFtInfo = dataProcessTool.LibDataProcess2(showFtInfo);
        showSpec = creatSpecSysLibData(t_showFtInfo.getAutoLineSourceWave(), t_showFtInfo.getAutoLineIntensity(),
                t_showFtInfo.getFtInfo().getSampleName());

        return showSpec;
    }

    public SpectrumLineData creatLibSpecLineData(double[] waves, double[] intensitys, List<PeakInfo> peakList,
            String title) {
        if (waves.length != intensitys.length) {
            // Throwable
        }
        SpectrumLineData spectrumLineData = new SpectrumLineData();
        double max_intensity = 0.0;
        List<Entry> entries = new ArrayList<>();
        for (int i = 0; i < waves.length; i++) {
            Entry entry = new Entry((float) waves[i], (float) intensitys[i]);
            entries.add(entry);
            if (max_intensity < intensitys[i]) {
                max_intensity = intensitys[i];
            }
        }

        try {
            // 根据是否是单一谱图选择合适的图标颜色
            int iconResId = isSingleSpectrum ? R.drawable.circle_shape_dark : R.drawable.circle_shape;
            Drawable icon = ResourcesCompat.getDrawable(Utils.getContext().getResources(), iconResId, null);
            for (PeakInfo peakinfo : peakList) {
                int index_p = Linq.of(waves).findIndex(p -> Math.abs(p - peakinfo.getWave()) <= 0.5);
                entries.get(index_p).setIcon(icon);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            //
        }

        spectrumLineData.setLabel(title);
        spectrumLineData.setEntries(entries);
        spectrumLineData.setMaxIntensity(max_intensity);
        return spectrumLineData;
    }

    public SpectrumLineData creatSpecSysLibData(double[] waves, double[] intensitys, String title) {
        if (waves.length != intensitys.length) {
            // Throwable
        }
        SpectrumLineData spectrumLineData = new SpectrumLineData();
        double max_intensity = 0.0;
        List<Entry> entries = new ArrayList<>();
        for (int i = 0; i < waves.length; i++) {
            Entry entry = new Entry((float) waves[i], (float) intensitys[i]);
            entries.add(entry);
            if (max_intensity < intensitys[i]) {
                max_intensity = intensitys[i];
            }
        }

        spectrumLineData.setLabel(title);
        spectrumLineData.setEntries(entries);
        spectrumLineData.setMaxIntensity(max_intensity);
        return spectrumLineData;
    }

}
