<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/nc_main">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.15" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.05" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline9"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.50" />

    <TextView
        android:id="@+id/textView34"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="@string/title_user_info"
        android:textColor="@color/nc_light"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/guideline9"
        app:layout_constraintStart_toStartOf="@+id/guideline9"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView27"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:text="@string/title_username_with_colon"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="@+id/guideline8"
        app:layout_constraintTop_toTopOf="@+id/guideline5" />

    <EditText
        android:id="@+id/txt_name"
        style="@style/CollectInfoEditTextStyle"
        android:layout_width="200dp"
        android:layout_height="32dp"
        android:layout_marginStart="20dp"
        android:background="@drawable/bottom_border_shadow_border"
        android:hint="@string/hint_enter_username"
        app:layout_constraintBottom_toBottomOf="@+id/textView27"
        app:layout_constraintStart_toEndOf="@+id/textView27"
        app:layout_constraintTop_toTopOf="@+id/textView27"
        tools:text="TextView" />

    <TextView
        android:id="@+id/textView28"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp"
        android:text="@string/title_password_with_colon"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@+id/textView27"
        app:layout_constraintTop_toBottomOf="@+id/textView27" />

    <EditText
        android:id="@+id/txt_password"
        style="@style/CollectInfoEditTextStyle"
        android:layout_width="200dp"
        android:layout_height="32dp"
        android:background="@drawable/bottom_border_shadow_border"
        android:hint="@string/hint_enter_password"
        android:inputType="textPassword"
        app:layout_constraintBottom_toBottomOf="@+id/textView28"
        app:layout_constraintEnd_toEndOf="@+id/txt_name"
        app:layout_constraintTop_toTopOf="@+id/textView28"
        tools:text="123456" />

    <TextView
        android:id="@+id/textView29"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp"
        android:text="@string/title_permission"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@+id/textView28"
        app:layout_constraintTop_toBottomOf="@+id/textView28" />

    <com.ssraman.control.spinner.MaterialSpinner
        android:id="@+id/cb_priority"
        android:layout_width="200dp"
        android:layout_height="32dp"
        android:background="@drawable/input_border_bottom_border"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@+id/textView29"
        app:layout_constraintEnd_toEndOf="@+id/txt_password"
        app:layout_constraintTop_toTopOf="@+id/textView29"
        app:ms_background_color="@color/dark_gray"
        app:ms_popupwindow_height="wrap_content"
        app:ms_popupwindow_maxheight="200dp"
        app:ms_text_color="@android:color/white" />

    <TextView
        android:id="@+id/textView30"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp"
        android:text="@string/title_user_description"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@+id/textView29"
        app:layout_constraintTop_toBottomOf="@+id/textView29" />

    <EditText
        android:id="@+id/txt_status"
        style="@style/CollectInfoEditTextStyle"
        android:layout_width="200dp"
        android:layout_height="32dp"
        android:background="@drawable/bottom_border_shadow_border"
        android:hint="@string/hint_enter_description"
        app:layout_constraintBottom_toBottomOf="@+id/textView30"
        app:layout_constraintEnd_toEndOf="@+id/cb_priority"
        app:layout_constraintTop_toTopOf="@+id/textView30"
        tools:text="TextView" />

    <ImageView
        android:id="@+id/underline1"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="20dp"
        android:background="@color/nc_light"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView30" />

    <LinearLayout
        android:id="@+id/box_button"
        android:layout_width="330dp"
        android:layout_height="50dp"
        android:layout_gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/underline1">

        <TextView
            android:id="@+id/btn_user_cancel"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/button_dialog_ios_left_light"
            android:clickable="true"
            android:gravity="center"
            android:text="@string/btn_cancel"
            android:textColor="@color/nc_light"
            android:textSize="16sp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/nc_light" />

        <TextView
            android:id="@+id/btn_user_ok"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/button_dialog_ios_right_light"
            android:clickable="true"
            android:gravity="center"
            android:text="@string/btn_confirm"
            android:textColor="@color/nc_light"
            android:textSize="16sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
