package com.ssraman.drugraman.custom;

import com.github.mikephil.charting.data.Entry;

import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/6/26
 */
public class SpectrumLineData {
    private String label;
    private List<Entry> entries;
    private double maxIntensity;
    private int index;

    public SpectrumLineData(String label, List<Entry> entries) {
        this.label = label;
        this.entries = entries;
    }

    public SpectrumLineData() {
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<Entry> getEntries() {
        return entries;
    }

    public void setEntries(List<Entry> entries) {
        this.entries = entries;
    }

    public double getMaxIntensity() {
        return maxIntensity;
    }

    public void setMaxIntensity(double maxIntensity) {
        this.maxIntensity = maxIntensity;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
