package com.ssraman.drugraman.rbac.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.ToMany;
import org.greenrobot.greendao.annotation.Unique;
import org.greenrobot.greendao.annotation.Generated;

import java.util.Date;
import java.util.List;

/**
 * 权限实体类
 * 定义系统中的权限信息
 */
@Entity(nameInDb = "tb_permission")
public class Permission {
    
    @Id(autoincrement = true)
    @Property(nameInDb = "id")
    private Long id;
    
    @Property(nameInDb = "permission_code")
    @Unique
    private String permissionCode;
    
    @Property(nameInDb = "permission_name")
    private String permissionName;
    
    @Property(nameInDb = "resource")
    private String resource; // 资源类型：DETECTION, REPORT, SPECTRUM, USER, SYSTEM等
    
    @Property(nameInDb = "action")
    private String action; // 操作类型：CREATE, READ, UPDATE, DELETE, EXECUTE等
    
    @Property(nameInDb = "description")
    private String description;
    
    @Property(nameInDb = "status")
    private Integer status; // 0:禁用, 1:启用
    
    @Property(nameInDb = "created_at")
    private Date createdAt;
    
    @Property(nameInDb = "updated_at")
    private Date updatedAt;
    
    @ToMany(referencedJoinProperty = "permissionId")
    private List<RolePermission> rolePermissions;

    @Generated(hash = 1091441395)
    public Permission(Long id, String permissionCode, String permissionName,
            String resource, String action, String description, Integer status,
            Date createdAt, Date updatedAt) {
        this.id = id;
        this.permissionCode = permissionCode;
        this.permissionName = permissionName;
        this.resource = resource;
        this.action = action;
        this.description = description;
        this.status = status;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    @Generated(hash = 1581595346)
    public Permission() {
    }
    
    // 构造函数
    public Permission(String permissionCode, String permissionName, String resource, String action, String description) {
        this.permissionCode = permissionCode;
        this.permissionName = permissionName;
        this.resource = resource;
        this.action = action;
        this.description = description;
        this.status = 1; // 默认启用
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }
    
    // 业务方法
    public boolean isEnabled() {
        return status != null && status == 1;
    }
    
    public void enable() {
        this.status = 1;
        this.updatedAt = new Date();
    }
    
    public void disable() {
        this.status = 0;
        this.updatedAt = new Date();
    }
    
    public boolean matchesResource(String resourceType) {
        return this.resource != null && this.resource.equalsIgnoreCase(resourceType);
    }
    
    public boolean matchesAction(String actionType) {
        return this.action != null && this.action.equalsIgnoreCase(actionType);
    }
    
    public boolean matches(String resourceType, String actionType) {
        return matchesResource(resourceType) && matchesAction(actionType);
    }
    
    public String getFullPermissionString() {
        return resource + ":" + action;
    }

    // Getters and Setters
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPermissionCode() {
        return this.permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public String getPermissionName() {
        return this.permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public String getResource() {
        return this.resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getAction() {
        return this.action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<RolePermission> getRolePermissions() {
        return this.rolePermissions;
    }

    public void setRolePermissions(List<RolePermission> rolePermissions) {
        this.rolePermissions = rolePermissions;
    }
}
