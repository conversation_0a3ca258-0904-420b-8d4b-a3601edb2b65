package com.ssraman.drugraman.ui.adapter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.databinding.ItemLibraryBinding;
import com.ssraman.drugraman.db.entity.SampleTypeInfo;
import com.ssraman.drugraman.item.SampleTestItem;
import com.ssraman.lib_common.adapter.SimpleDataBindingAdapter;

/**
 * @author: Administrator
 * @date: 2021/6/17
 */
public class ItemClassifyAdapter extends SimpleDataBindingAdapter<SampleTypeInfo, ItemLibraryBinding> {
    public ItemClassifyAdapter(Context context) {
        super(context,R.layout.item_library, new DiffUtil.ItemCallback<SampleTypeInfo>() {
            @Override
            public boolean areItemsTheSame(@NonNull SampleTypeInfo oldItem, @NonNull SampleTypeInfo newItem) {
                return oldItem.equals(newItem);
            }

            @Override
            public boolean areContentsTheSame(@NonNull SampleTypeInfo oldItem, @NonNull SampleTypeInfo newItem) {
                return oldItem.getId().equals(newItem.getId());
            }
        });

    }

    @Override
    protected void onBindItem(ItemLibraryBinding binding, SampleTypeInfo item, RecyclerView.ViewHolder holder) {
        SampleTestItem sampleTestItem=new SampleTestItem(item.getName(),item.getImageIndex(),item.getLibraryType());
        binding.setSampleTestItem(sampleTestItem);
    }
}
