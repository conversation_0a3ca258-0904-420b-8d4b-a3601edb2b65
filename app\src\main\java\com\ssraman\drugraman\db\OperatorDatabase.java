package com.ssraman.drugraman.db;

import android.content.Context;

import com.ssraman.drugraman.db.gen.DaoMaster;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.lib_common.constant.DbConst;

import org.greenrobot.greendao.database.Database;

/**
 * @author: Administrator
 * @date: 2021/6/20
 */
public class OperatorDatabase {
    private DaoMaster mDaoMaster;
    private DaoSession mDaoSession;
    private static OperatorDatabase mInstance;

    public static OperatorDatabase getInstance(Context context){
        if (mInstance==null){
            //保证异步处理安全操作
            synchronized (OperatorDatabase.class){
                if (mInstance==null){
                    mInstance=new OperatorDatabase (context);
                }
            }
        }
        return mInstance;
    }

    private OperatorDatabase (Context context){
        if (mInstance==null){
            final boolean ENCRYPTED=true;
            //DaoMaster.DevOpenHelper devOpenHelper = new DaoMaster.DevOpenHelper(context, DbConst.LOGIN_DATABASE_NAME, null);
            DaoMaster.DevOpenHelper devOpenHelper = new DaoMaster.DevOpenHelper(context, DbConst.LOGIN_DATABASE_NAME);
            Database db = ENCRYPTED ? devOpenHelper.getEncryptedWritableDb("www.ss-raman.com") : devOpenHelper.getWritableDb();
            mDaoMaster = new DaoMaster(db);
            mDaoSession=mDaoMaster.newSession();
        }
    }

    public DaoMaster getMaster(){
        return mDaoMaster;
    }
    public DaoSession getSession(){
        return mDaoSession;
    }
    public DaoSession getNewSession(){
        mDaoSession=mDaoMaster.newSession();
        return mDaoSession;
    }

}
