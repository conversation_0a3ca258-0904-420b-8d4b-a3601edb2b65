package com.ssraman.drugraman.db.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.ToOne;
import org.greenrobot.greendao.annotation.Generated;

import java.util.Date;

/**
 * RBAC审计日志实体类
 * 记录用户操作的审计信息
 */
@Entity(nameInDb = "tb_rbac_audit_log")
public class RbacAuditLog {
    
    @Id(autoincrement = true)
    @Property(nameInDb = "id")
    private Long id;
    
    @Property(nameInDb = "user_id")
    private Long userId;
    
    @Property(nameInDb = "action")
    private String action; // 操作类型
    
    @Property(nameInDb = "resource")
    private String resource; // 操作的资源
    
    @Property(nameInDb = "details")
    private String details; // 操作详情
    
    @Property(nameInDb = "ip_address")
    private String ipAddress; // IP地址
    
    @Property(nameInDb = "timestamp")
    private Date timestamp; // 操作时间
    
    @ToOne(joinProperty = "userId")
    private RbacUser user;

    @Generated(hash = 1186799675)
    public RbacAuditLog(Long id, Long userId, String action, String resource,
            String details, String ipAddress, Date timestamp) {
        this.id = id;
        this.userId = userId;
        this.action = action;
        this.resource = resource;
        this.details = details;
        this.ipAddress = ipAddress;
        this.timestamp = timestamp;
    }

    @Generated(hash = 1906849798)
    public RbacAuditLog() {
    }
    
    // 构造函数
    public RbacAuditLog(Long userId, String action, String resource, String details, String ipAddress) {
        this.userId = userId;
        this.action = action;
        this.resource = resource;
        this.details = details;
        this.ipAddress = ipAddress;
        this.timestamp = new Date();
    }
    
    // 业务方法
    public boolean isLoginAction() {
        return "LOGIN".equals(action);
    }
    
    public boolean isLogoutAction() {
        return "LOGOUT".equals(action);
    }
    
    public boolean isPermissionAction() {
        return action != null && action.startsWith("PERMISSION_");
    }
    
    public boolean isUserManagementAction() {
        return action != null && (action.startsWith("USER_") || action.startsWith("ROLE_"));
    }

    // Getters and Setters
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getAction() {
        return this.action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getResource() {
        return this.resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getDetails() {
        return this.details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getIpAddress() {
        return this.ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Date getTimestamp() {
        return this.timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public RbacUser getUser() {
        return this.user;
    }

    public void setUser(RbacUser user) {
        this.user = user;
    }
}
