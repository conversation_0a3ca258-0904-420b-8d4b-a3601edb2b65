package com.ssraman.drugraman.db.repository;

import com.ssraman.drugraman.db.StringDateConverter;
import com.ssraman.drugraman.db.entity.OperLogInfo;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.gen.OperLogInfoDao;
import com.ssraman.drugraman.newentiry.LogFilterInfo;
import com.ssraman.drugraman.newentiry.PageInfo;
import com.ssraman.lib_common.room.RxRoom;

import org.greenrobot.greendao.query.QueryBuilder;
import org.greenrobot.greendao.query.WhereCondition;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Completable;
import io.reactivex.Single;

/**
 * @author: Administrator
 * @date: 2021/10/20
 */
public class OperLogRepository {
    private DaoSession mDaoSession;
    private OperLogInfoDao operLogDao;

    public OperLogRepository(DaoSession daoSession) {
        this.mDaoSession = daoSession;
        operLogDao = this.mDaoSession.getOperLogInfoDao();
    }

    public Completable insertLog(OperLogInfo operLogInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                operLogDao.insert(operLogInfo);
                return null;
            }
        });
    }

    public long insertLogByRe(OperLogInfo operLogInfo)
    {
        return operLogDao.insert(operLogInfo);
    }

    public Completable deleteLog(OperLogInfo operLogInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                operLogDao.delete(operLogInfo);
                return null;
            }
        });
    }

    public Completable updateLog(OperLogInfo operLogInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                operLogDao.update(operLogInfo);
                return null;
            }
        });
    }


    public Single<List<OperLogInfo>> getLogRecordByFilter(PageInfo pageInfo, LogFilterInfo filter) {
        return RxRoom.createSingle(new Callable<List<OperLogInfo>>() {
            @Override
            public List<OperLogInfo> call() throws Exception {
                List<OperLogInfo> recordList;
                long count;
                if (filter != null) {
                    StringDateConverter DetectionTimeConverter = new StringDateConverter();
                    QueryBuilder<OperLogInfo> recordQueryBuilder = operLogDao.queryBuilder();
                    List<WhereCondition> whereConditions = new ArrayList<>();
                    if (filter.IsDateTime) {
                        String OperationStartTime = DetectionTimeConverter.convertToDatabaseValue(filter.OperationStartTime);
                        String OperationEndTime = DetectionTimeConverter.convertToDatabaseValue(filter.OperationEndTime);
                        WhereCondition startWhere = OperLogInfoDao.Properties.OperatorTime.gt(OperationStartTime);
                        WhereCondition endWhere = OperLogInfoDao.Properties.OperatorTime.lt(OperationEndTime);
                        whereConditions.add(startWhere);
                        whereConditions.add(endWhere);
                    }
                    if (filter.IsAndOperation) {
                        WhereCondition checkManWhere = OperLogInfoDao.Properties.Operator.like("%" + (String) filter.Operation + "%");
                        whereConditions.add(checkManWhere);
                    }
                    if (filter.IsAndOperationType) {
                        WhereCondition prjNameWhere = OperLogInfoDao.Properties.OperatorType.eq( filter.OperationType);
                        whereConditions.add(prjNameWhere);
                    }
                    if (whereConditions.size() > 0) {
                        for (int i = 0; i < whereConditions.size(); i++) {
                            recordQueryBuilder = recordQueryBuilder.where(whereConditions.get(i));
                        }
                    }
                    count = recordQueryBuilder.count();
                    if (pageInfo.getPageSize() != -1) {
                        //获取所有
                        recordList = recordQueryBuilder
                                .offset(pageInfo.getPageIndex() * pageInfo.getPageSize()).limit(pageInfo.getPageSize()).orderDesc(OperLogInfoDao.Properties.OperatorTime).list();
                    } else {
                        recordList = recordQueryBuilder.orderDesc(OperLogInfoDao.Properties.OperatorTime).list();
                    }
                } else {
                    count = operLogDao.queryBuilder().count();
                    if (pageInfo.getPageSize() != -1) {
                        //获取所有
                        recordList = operLogDao.queryBuilder()
                                .offset(pageInfo.getPageIndex() * pageInfo.getPageSize()).limit(pageInfo.getPageSize()).orderDesc(OperLogInfoDao.Properties.OperatorTime).list();
                    } else {
                        recordList = operLogDao.queryBuilder().orderDesc(OperLogInfoDao.Properties.OperatorTime).list();
                    }

                }
                return recordList;
            }
        });
    }

    public Single<List<OperLogInfo>> getLogRecordBySimple(PageInfo pageInfo, Date start, Date end) {
        return RxRoom.createSingle(new Callable<List<OperLogInfo>>() {
            @Override
            public List<OperLogInfo> call() throws Exception {
                List<OperLogInfo> recordList;
                long count;
                StringDateConverter DetectionTimeConverter = new StringDateConverter();
                QueryBuilder<OperLogInfo> recordQueryBuilder = operLogDao.queryBuilder();
                List<WhereCondition> whereConditions = new ArrayList<>();
                String str_start = DetectionTimeConverter.convertToDatabaseValue(start);
                String str_end = DetectionTimeConverter.convertToDatabaseValue(end);
                WhereCondition startWhere = OperLogInfoDao.Properties.OperatorTime.gt(str_start);
                WhereCondition endWhere = OperLogInfoDao.Properties.OperatorTime.lt(str_end);
                whereConditions.add(startWhere);
                whereConditions.add(endWhere);

                if (whereConditions.size() > 0) {
                    for (int i = 0; i < whereConditions.size(); i++) {
                        recordQueryBuilder = recordQueryBuilder.where(whereConditions.get(i));
                    }
                }
                count = recordQueryBuilder.count();
                if (pageInfo.getPageSize() != -1) {
                    //获取所有
                    recordList = recordQueryBuilder
                            .offset(pageInfo.getPageIndex() * pageInfo.getPageSize()).limit(pageInfo.getPageSize()).orderDesc(OperLogInfoDao.Properties.OperatorTime).list();
                } else {
                    recordList = recordQueryBuilder.orderDesc(OperLogInfoDao.Properties.OperatorTime).list();
                }

                return recordList;
            }
        });
    }

}
