package com.ssraman.drugraman.rbac.security;

import android.util.Log;

import com.ssraman.drugraman.db.entity.RbacPermission;
import com.ssraman.drugraman.db.entity.RbacRole;
import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.service.IRbacPermissionService;
import com.ssraman.drugraman.rbac.service.IRbacRoleService;

import java.util.List;
import java.util.Set;
import java.util.HashSet;

/**
 * 权限检查器
 * 提供统一的权限验证功能
 */
public class PermissionChecker {
    
    private static final String TAG = "PermissionChecker";
    
    private final IRbacPermissionService permissionService;
    private final IRbacRoleService roleService;
    private final PermissionCache permissionCache;
    
    public PermissionChecker(IRbacPermissionService permissionService, 
                           IRbacRoleService roleService,
                           PermissionCache permissionCache) {
        this.permissionService = permissionService;
        this.roleService = roleService;
        this.permissionCache = permissionCache;
    }
    
    /**
     * 检查用户是否具有指定权限
     * @param userId 用户ID
     * @param permissionCode 权限代码
     * @return 是否具有权限
     */
    public boolean hasPermission(Long userId, String permissionCode) {
        if (userId == null || permissionCode == null) {
            return false;
        }
        
        try {
            // 先从缓存获取
            Set<String> userPermissions = permissionCache.getUserPermissions(userId);
            if (userPermissions != null) {
                return userPermissions.contains(permissionCode);
            }
            
            // 缓存未命中，从数据库查询
            List<RbacPermission> permissions = permissionService.getUserPermissions(userId);
            userPermissions = new HashSet<>();
            for (RbacPermission permission : permissions) {
                if (permission.isEnabled()) {
                    userPermissions.add(permission.getPermissionCode());
                }
            }
            
            // 更新缓存
            permissionCache.setUserPermissions(userId, userPermissions);
            
            return userPermissions.contains(permissionCode);
            
        } catch (Exception e) {
            Log.e(TAG, "权限检查失败", e);
            // 发生异常时，拒绝访问
            return false;
        }
    }
    
    /**
     * 检查用户是否具有指定资源和操作的权限
     * @param userId 用户ID
     * @param resource 资源类型
     * @param action 操作类型
     * @return 是否具有权限
     */
    public boolean hasPermission(Long userId, String resource, String action) {
        try {
            PermissionType permissionType = PermissionType.fromResourceAndAction(resource, action);
            return hasPermission(userId, permissionType.getCode());
        } catch (IllegalArgumentException e) {
            Log.w(TAG, "无效的权限组合: " + resource + ":" + action);
            return false;
        }
    }
    
    /**
     * 检查用户是否具有指定权限类型
     * @param userId 用户ID
     * @param permissionType 权限类型
     * @return 是否具有权限
     */
    public boolean hasPermission(Long userId, PermissionType permissionType) {
        return hasPermission(userId, permissionType.getCode());
    }
    
    /**
     * 检查用户角色级别是否满足要求
     * @param userId 用户ID
     * @param requiredLevel 要求的最低级别
     * @return 是否满足级别要求
     */
    public boolean hasRoleLevel(Long userId, int requiredLevel) {
        if (userId == null) {
            return false;
        }
        
        try {
            List<RbacRole> userRoles = roleService.getUserRoles(userId);
            for (RbacRole role : userRoles) {
                if (role.isEnabled() && role.getLevel() != null && role.getLevel() >= requiredLevel) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "角色级别检查失败", e);
            return false;
        }
    }
    
    /**
     * 检查用户是否具有指定角色类型
     * @param userId 用户ID
     * @param roleType 角色类型
     * @return 是否具有角色
     */
    public boolean hasRole(Long userId, RoleType roleType) {
        if (userId == null || roleType == null) {
            return false;
        }
        
        try {
            List<RbacRole> userRoles = roleService.getUserRoles(userId);
            for (RbacRole role : userRoles) {
                if (role.isEnabled() && roleType.getCode().equals(role.getRoleCode())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "角色检查失败", e);
            return false;
        }
    }
    
    /**
     * 检查用户是否具有任一指定权限
     * @param userId 用户ID
     * @param permissionCodes 权限代码列表
     * @return 是否具有任一权限
     */
    public boolean hasAnyPermission(Long userId, String... permissionCodes) {
        if (userId == null || permissionCodes == null || permissionCodes.length == 0) {
            return false;
        }
        
        for (String permissionCode : permissionCodes) {
            if (hasPermission(userId, permissionCode)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查用户是否具有所有指定权限
     * @param userId 用户ID
     * @param permissionCodes 权限代码列表
     * @return 是否具有所有权限
     */
    public boolean hasAllPermissions(Long userId, String... permissionCodes) {
        if (userId == null || permissionCodes == null || permissionCodes.length == 0) {
            return false;
        }
        
        for (String permissionCode : permissionCodes) {
            if (!hasPermission(userId, permissionCode)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 获取用户的所有权限代码
     * @param userId 用户ID
     * @return 权限代码集合
     */
    public Set<String> getUserPermissionCodes(Long userId) {
        if (userId == null) {
            return new HashSet<>();
        }
        
        try {
            // 先从缓存获取
            Set<String> userPermissions = permissionCache.getUserPermissions(userId);
            if (userPermissions != null) {
                return new HashSet<>(userPermissions);
            }
            
            // 缓存未命中，从数据库查询
            List<RbacPermission> permissions = permissionService.getUserPermissions(userId);
            userPermissions = new HashSet<>();
            for (RbacPermission permission : permissions) {
                if (permission.isEnabled()) {
                    userPermissions.add(permission.getPermissionCode());
                }
            }
            
            // 更新缓存
            permissionCache.setUserPermissions(userId, userPermissions);
            
            return new HashSet<>(userPermissions);
            
        } catch (Exception e) {
            Log.e(TAG, "获取用户权限失败", e);
            return new HashSet<>();
        }
    }
    
    /**
     * 获取用户的最高角色级别
     * @param userId 用户ID
     * @return 最高角色级别
     */
    public int getUserMaxRoleLevel(Long userId) {
        if (userId == null) {
            return 0;
        }
        
        try {
            List<RbacRole> userRoles = roleService.getUserRoles(userId);
            int maxLevel = 0;
            for (RbacRole role : userRoles) {
                if (role.isEnabled() && role.getLevel() != null && role.getLevel() > maxLevel) {
                    maxLevel = role.getLevel();
                }
            }
            return maxLevel;
        } catch (Exception e) {
            Log.e(TAG, "获取用户最高角色级别失败", e);
            return 0;
        }
    }
    
    /**
     * 清除用户权限缓存
     * @param userId 用户ID
     */
    public void clearUserPermissionCache(Long userId) {
        if (userId != null) {
            permissionCache.removeUserPermissions(userId);
        }
    }
    
    /**
     * 清除所有权限缓存
     */
    public void clearAllPermissionCache() {
        permissionCache.clear();
    }
}
