package com.ssraman.drugraman.db.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Transient;
import org.greenrobot.greendao.annotation.Generated;

import java.io.Serializable;

/**
 * @author: Administrator
 * @date: 2021/10/9
 */
@Entity(nameInDb = "Peak", createInDb = false)
public class PeakInfo  implements Serializable  {

    @Id(autoincrement = true)
    @Property(nameInDb = "Id")
    private Long Id;

    @Property(nameInDb = "FtId")
    private Integer FtId;

    @Property(nameInDb = "PeakId")
    private Integer PeakId;

    @Property(nameInDb = "SendWave")
    private Double SendWave;
    //主要是为标定时方便，提供此项
    @Property(nameInDb = "Lumbda")
    private Double Lumbda;

    @Property(nameInDb = "Wave")
    private Double Wave;

    @Property(nameInDb = "Intensity")
    private Double Intensity;

    @Property(nameInDb = "Type")
    private Integer Type;

    @Property(nameInDb = "Must")
    private Integer Must;

    @Property(nameInDb = "StartIntensity")
    private Double StartIntensity = 0.0;

    @Property(nameInDb = "Calibration")
    private Double Calibration = 0.0;

    @Property(nameInDb = "EnableCalibration")
    private Integer EnableCalibration = 0;

    //匹配限值
    @Property(nameInDb = "MatchLimit")
    private Double MatchLimit = 10.0;

    @Transient
    private boolean selected = false;

    @Generated(hash = 1088863204)
    public PeakInfo(Long Id, Integer FtId, Integer PeakId, Double SendWave,
                    Double Lumbda, Double Wave, Double Intensity, Integer Type,
                    Integer Must, Double StartIntensity, Double Calibration,
                    Integer EnableCalibration, Double MatchLimit) {
        this.Id = Id;
        this.FtId = FtId;
        this.PeakId = PeakId;
        this.SendWave = SendWave;
        this.Lumbda = Lumbda;
        this.Wave = Wave;
        this.Intensity = Intensity;
        this.Type = Type;
        this.Must = Must;
        this.StartIntensity = StartIntensity;
        this.Calibration = Calibration;
        this.EnableCalibration = EnableCalibration;
        this.MatchLimit = MatchLimit;
    }

    @Generated(hash = 1721150100)
    public PeakInfo() {
    }

    public Long getId() {
        return this.Id;
    }

    public void setId(Long Id) {
        this.Id = Id;
    }

    public Integer getFtId() {
        return this.FtId;
    }

    public void setFtId(Integer FtId) {
        this.FtId = FtId;
    }

    public Integer getPeakId() {
        return this.PeakId;
    }

    public void setPeakId(Integer PeakId) {
        this.PeakId = PeakId;
    }

    public Double getSendWave() {
        return this.SendWave;
    }

    public void setSendWave(Double SendWave) {
        this.SendWave = SendWave;
    }

    public Double getWave() {
        return this.Wave;
    }

    public void setWave(Double Wave) {
        this.Wave = Wave;
    }

    public Double getIntensity() {
        return this.Intensity;
    }

    public void setIntensity(Double Intensity) {
        this.Intensity = Intensity;
    }

    public Integer getType() {
        return this.Type;
    }

    public void setType(Integer Type) {
        this.Type = Type;
    }

    public Integer getMust() {
        return this.Must;
    }

    public void setMust(Integer Must) {
        this.Must = Must;
    }

    public Double getStartIntensity() {
        return this.StartIntensity;
    }

    public void setStartIntensity(Double StartIntensity) {
        this.StartIntensity = StartIntensity;
    }

    public Double getCalibration() {
        return this.Calibration;
    }

    public void setCalibration(Double Calibration) {
        this.Calibration = Calibration;
    }

    public Integer getEnableCalibration() {
        return this.EnableCalibration;
    }

    public void setEnableCalibration(Integer EnableCalibration) {
        this.EnableCalibration = EnableCalibration;
    }

    public Double getMatchLimit() {
        return this.MatchLimit;
    }

    public void setMatchLimit(Double MatchLimit) {
        this.MatchLimit = MatchLimit;
    }

    public Double getLumbda() {
        return this.Lumbda;
    }

    public void setLumbda(Double Lumbda) {
        this.Lumbda = Lumbda;
    }

    static final long serialVersionUID = -15515459L;


}
