#include <math.h>
#include <malloc.h>
#include <string.h>
#include <stdlib.h>
#include <algorithm>
#include "basicfuc.h"
//#include "../../mathtools/inc/mathtool.h"
#include<fstream>  //ifstream
#include <sstream>
#include<iostream>
#include "drpublic.h"


using namespace std;
/***************************************
函数名：autoBaseline
返回：基线拉平光谱（STEP2）
参数：spec  原始光谱
参数：dLowWaveCnt 低波数
参数：innerData 预留数据
说明：取出光谱基线，需要完成STEP1
修订：已测试
***************************************/
int pack_autoBaseline(double* x, double* y, int len, double dLowWaveCnt, int nR,int mode) {
	SPEC spec;
	spec.dIntensity = y;
	spec.dWcnt = x;
	spec.len = len;
	autoBaseline(&spec, dLowWaveCnt, nR);
    return len;
}

void autoBaseline(SPEC* spec, double dLowWaveCnt, int nR) {
	int i, k, r = nR/*150*/, nlowCnt = 0;
	double  cy, r2, Spcmax = 0, Spcmin = spec->dIntensity[0];
	int stopPoint, start, end;
	double min;
	int dev = 5;
	int shortLen = spec->len / dev;
	double scaleP;
	int temp;
	int begin;
	double tempMin;
	double temDecPoint;
	//int temStartPos;
	int startjj;
	int jj, shotCnt;
	int tempCnt;
	double a[2][50];

	if (!decodeSoft(spec->dIntensity, spec->len))
		return;

	double* pBkgSpc = (double*)malloc(sizeof(double)*(spec->len));
	double* xx2 = (double*)malloc(sizeof(double)*r);
	int* shortSpcCnt = (int*)malloc(sizeof(int)*shortLen);
	double* tempDeclist;
	double* pbuff;

	for (i = 0; i < r; i++)
		xx2[i] = i * i;

	/*计算最大值，背景光谱清空*/
	for (i = 0; i < spec->len; i++) {
		if (nlowCnt > 0) {
			Spcmax = (Spcmax > spec->dIntensity[i]) ? Spcmax : spec->dIntensity[i];
			Spcmin = (Spcmin < spec->dIntensity[i]) ? Spcmin : spec->dIntensity[i];
		}
		else if (nlowCnt == 0)
			nlowCnt = (spec->dWcnt[i] >= dLowWaveCnt) ? i : nlowCnt;
		pBkgSpc[i] = 0;
	}
	//解决0点死循环错误
	if (Spcmin <= 0) {
		for (i = 0; i < spec->len; i++)
			spec->dIntensity[i] += Spcmin + 1;
	}
	/*重采样版本******************************************/
	for (i = 0; i < shortLen; i++) {
		tempMin = spec->dIntensity[i * dev];
		tempCnt = i * dev;
		for (k = 0; k < dev; k++) {
			if (tempMin > spec->dIntensity[i * dev + k]) {
				tempCnt = i * dev + k;
				tempMin = spec->dIntensity[tempCnt];
			}

		}
		shortSpcCnt[i] = tempCnt;
	}

	scaleP = (Spcmax - Spcmin) / shortLen;//根据视觉图建立圆xy尺度对应关系
	//建立摩擦弧
	cy = -r * scaleP;
	r2 = pow((double)r, 2);
	pbuff = (double*)malloc(sizeof(double)*((r << 1) + 2));//圆弧

	for (k = 0; k < r; k++) {
		pbuff[k] = sqrt(r2 - xx2[r - k - 1]) * scaleP + cy;
	}
	for (k = r; k < (r + r); k++) {
		pbuff[k] = sqrt(r2 - xx2[k - r]) * scaleP + cy;
	}

	//free(xx2);
	free(xx2);

	//i = (nlowCnt - r + 1 > 0) ? nlowCnt - r + 1 : 1;//光谱数据起始位置
	i = nlowCnt - r + 1 ;//光谱数据起始位置
	stopPoint = spec->len - 2 + r;
	//start = (i - r >= nlowCnt) ? i - r : nlowCnt;            //接触区位置
	//end = (i + r >= nlowCnt) ? i + r : nlowCnt;            //接触区位置
	//end = (i + r < spec->len - 1) ? i + r : spec->len - 1;


	tempDeclist = (double*)malloc(sizeof(double)*(r << 1) + 1);
	//tempCnt = (int*)malloc(sizeof(int)*(r<<1)+1);
	startjj = 0;
	jj = 0;

	for (jj = 0; shortSpcCnt[jj] < i; ) {
		jj++;
	}
	shotCnt = jj;
	if(jj>0)
		i = shortSpcCnt[jj];//当前位置
	while (i < stopPoint) {
		start = (i - r >= nlowCnt) ? i - r : nlowCnt;            //接触区位置
		end = (i + r >= nlowCnt) ? i + r : nlowCnt;            //接触区位置
		end = (end < spec->len - 1) ? end : spec->len - 1;
		//start = (i - r >= nlowCnt) ? i - r : nlowCnt;//(i-r>nlowCnt)?i-r:nlowCnt;
		//end = (i + r < spec->len - 1) ? i + r : spec->len - 1;
		min = Spcmax;


		// Logger.d("1"+jj);
		for (jj = startjj; shortSpcCnt[jj] < start; ) {
			jj++;
		}
		startjj = jj;


		// Logger.d("2"+startjj);

		for (jj = startjj; shortSpcCnt[jj] < end; jj++) {
			temDecPoint = spec->dIntensity[shortSpcCnt[jj]] - pbuff[shortSpcCnt[jj] - start];
			tempDeclist[jj - startjj] = temDecPoint;
			min = (min < temDecPoint) ? min : temDecPoint;
			if (jj > shortLen - 2)
				break;
		}

		tempMin = scaleP + min;
		for (jj = startjj; shortSpcCnt[jj] < end; jj++) {
			if (tempDeclist[jj - startjj] <= tempMin)
				pBkgSpc[shortSpcCnt[jj]] = spec->dIntensity[shortSpcCnt[jj]];
			if (jj > shortLen - 2)
				break;
		}
		if (shotCnt + 1 < shortLen) {
			if (i>=0) {
				i = shortSpcCnt[shotCnt++];
			}
			else {
				i+=3;
				for (jj = 0; shortSpcCnt[jj] < i; ) {
					jj++;
				}
				shotCnt = jj;
			}
		}
		else
			break;

	}
	free(shortSpcCnt);
	free(pbuff);
	free(tempDeclist);
	///////////////////////////////////////////////////计算基点
	temp = 0;
	begin = 0;
	end = spec->len - 1;

	while (spec->dWcnt[begin] < dLowWaveCnt) {
		spec->dIntensity[begin++] = 0;
	}
	while (pBkgSpc[begin] == 0 && begin < end) {
		spec->dIntensity[begin++] = 0;
	}
	while (pBkgSpc[end] == 0 && begin < end) {
		spec->dIntensity[end] = 0;
		end--;
	}
	for (i = begin; i <= end; i++) {
		if (pBkgSpc[i] == 0)
			pBkgSpc[i] = pBkgSpc[i - 1];
		else {
			int CP = 0;
			jj = 0;
			min = 10000;
			while (pBkgSpc[i + jj + 1] > 0 && jj < 50 && temp != 0) {
				a[0][jj] = (pBkgSpc[i + jj] - pBkgSpc[temp]) / (i + jj - temp);
				a[1][jj] = pBkgSpc[i + jj + 1] - pBkgSpc[i + jj];
				if (min >fabs(a[0][jj] - a[1][jj])) {
					min = fabs(a[0][jj] - a[1][jj]);
					CP = jj;
				}
				jj++;
			}
			i = i + CP;
			//
			for (k = i - 1; k >= temp; k--) {
				pBkgSpc[k] = (k - temp) * (pBkgSpc[i] - pBkgSpc[temp]) / (i - temp) + pBkgSpc[temp];
			}
			temp = i;
		}
	}
	/////////////////////////////////////////////////////////计算基线

	for (i = begin; i <= end; i++) {
		spec->dIntensity[i] = (spec->dIntensity[i] - pBkgSpc[i]);
	}
	encodeSoft(spec->dIntensity, spec->len);//

	free(pBkgSpc);
	return;

}

/***************************************
函数名：__smooth
返回：平滑后的光谱（
参数：spec  原始光谱
参数：spec_r 返回光谱
说明：取出光谱基线，需要完成STEP1
修订：测试
***************************************/
int smooth(SPEC* spec, int lowline, int topline, SPEC* spec_r) {
	int i, k, j;
	//int step = 1;   //重采样步长
	int ii_org = 1; //原始数据游标
	int ii_Re = 0;  //整理后数据游标
	int shiftRight = 0;
	int sampleNum = topline;
	double* spc = (double*)malloc(sizeof(double)*(sampleNum << 1));//光谱数据
	double prePoint = spec->dWcnt[0];
	double backPoint = spec->dWcnt[1];

	double x;    //脉冲信号的临时变量
	double xx[64];
	double dfactor = 4;  //越大脉冲越宽
	double pulse[64];
	//平滑运算
	int prePointCnt, backPointCnt;
	double* result = (double*)malloc(sizeof(double)*(sampleNum));

	if (!decodeSoft(spec->dIntensity, spec->len))
	{
		free(spc);
		free(result);
		return 0;
	}
	// 重新采样
	spc[0] = 0;
	result[0] = 0;
	spc[sampleNum] = 0;
	//横坐标加1，y归零
	for (i = 1; i < sampleNum; i++) {
		spc[i] = spc[i - 1] + 1;
		result[i] = 0;
		spc[i + sampleNum] = 0;
	}

	while (ii_Re < sampleNum) {
		//if (ii_Re >2300)
		//	ii_Re= ii_Re;
		if (spc[ii_Re] > prePoint && spc[ii_Re] < backPoint) {
			spc[ii_Re + sampleNum] = spec->dIntensity[ii_org - 1] + (spec->dIntensity[ii_org]
																	 - spec->dIntensity[ii_org - 1]) * (spc[ii_Re] - prePoint) / (backPoint - prePoint);
			ii_Re = ii_Re + 1;
		}
		else if (spc[ii_Re] <= prePoint) {
			spc[ii_Re + sampleNum] = spec->dIntensity[ii_org - 1];
			ii_Re = ii_Re + 1;
		}
		else if (spc[ii_Re] >= prePoint) {
			prePoint = spec->dWcnt[ii_org];
			ii_org = ii_org + 1;
			if (ii_org >= spec->len)
				break;
			backPoint = spec->dWcnt[ii_org];
		}
	}

	//////////////////////////////////

	//构建脉冲信号
	xx[0] = -32;
	x = (xx[0] - shiftRight) / dfactor;
	pulse[0] = 2 / (exp(x) + exp(-x));

	for (i = 1; i < 64; i++) {
		xx[i] = xx[i - 1] + 1;
		x = (xx[i] - shiftRight) / dfactor;
		pulse[i] = 2 / (exp(x) + exp(-x));
	}


	for (i = 0; i < sampleNum; i++) {
		if (i < 64) {
			prePointCnt = 63 - i;
		}
		else {
			prePointCnt = 0;
		}

		if (i + 64 > sampleNum) {
			backPointCnt = sampleNum - i - 1;
		}
		else {
			backPointCnt = 63;
		}

		k = i;
		for (j = prePointCnt; j < backPointCnt; j++) {
			result[i] += spc[k + sampleNum] * pulse[j];
			k = k + 1;
		}
	}
	free(spc);
	//清除反向假信号
	for (i = 0; i < sampleNum; i++) {

		result[i] = (result[i] < 0) ? 0 : result[i];
	}
	////////////////////////////////////////////////////////////////
	getNewWCnt(lowline, topline, spec_r->dWcnt);
	////////////////////////////////////////////////////////////////
	memcpy(spec_r->dIntensity, (void*)(result + lowline), sizeof(double)*(topline - lowline));
	free(result);
	spec_r->len = topline - lowline;

	encodeSoft(spec_r->dIntensity, spec_r->len);//
	return 1;
}
///////////////////////////////////////////////////////////////////////////////
unsigned char __grade(unsigned int value)
{
	unsigned char count = 0;
	while (value)
	{
		count++;
		value >>= 1;
	}

	return count;
}
/***************************************
函数名：SpecSNR
返回： SNR
参数： rawSpec 原始光谱数据，未经过平滑
参数： len 数据长度
参数： lowlineCnt 低波数对应的位置
参数： boxZeroPos  计算峰底的范围
参数： boxWidth    宽度
参数： peakCenter  信噪比计算主峰的位置
参数： FWHM        珠峰搜索范围
参数：innerData 预留数据
说明: 计算原始谱信噪比，
修订：
***************************************/

double SpecSNR(double*  specdata,    //16位原始光谱数据
			   int      len,        //数据长度
			   int      lowlineCnt, //低波数位置，像素单位
			   int      boxZeroPos, //特征峰框（在rawSpec中的首地址）
			   int      boxWidth,   //在特征峰框中的数据长度
			   int      peakCenter, //特征峰的参考位置，单位像素
			   int      FWHM       //特征峰的搜索范围      FWHM       //特征峰的搜索范围
) {
	unsigned int dcount[8];
	unsigned char intensityGrade, j, count = 1, cnt = 0;
	double lastMaxorMin = specdata[lowlineCnt];
	double sta[8][32], max = 0;
	double noise;
	double min[6];

	memset(sta, 0, sizeof(double) << 8); //*256
	memset(dcount, 0, sizeof(int) << 3);       //*2

	for (int i = lowlineCnt + 1; i<len - 1; i++) {
		if (specdata[i] >= specdata[i - 1] && specdata[i] >= specdata[i + 1]) {
			intensityGrade = __grade((unsigned int)(specdata[i]>lastMaxorMin ? (specdata[i] - lastMaxorMin) : 0));

			if (count<4 && intensityGrade<32) {

				sta[count][intensityGrade]++;
				lastMaxorMin = specdata[i];
			}
			count = 0;
		}
		else if (specdata[i] <= specdata[i - 1] && specdata[i] <= specdata[i + 1]) {
			intensityGrade = __grade(specdata[i]>lastMaxorMin ? 0 : (unsigned int)(lastMaxorMin - specdata[i]));
			if (count<4 && intensityGrade<32) {
				sta[count][intensityGrade]++;
				lastMaxorMin = specdata[i];
			}
			count = 0;
		}
		count++;
	}
	lastMaxorMin = 0;
	count = 0;
	for (j = 1; j<4; j++) {
		dcount[j] = 0;
		sta[j][0] = 0;
		for (int i = 1; i<32; i++) {
			sta[j][0] += (1 << i)*sta[j][i];
			dcount[j] += (unsigned int)sta[j][i];
		}
	}
	noise = (((sta[1][0] / dcount[1]) + (sta[2][0] / dcount[2]) + (sta[3][0] / dcount[3])) / 3) / 2;
	if (noise == 0)
		return 0;

	min[0] = (int)specdata[boxZeroPos];
	min[1] = (int)specdata[boxZeroPos];
	min[2] = (int)specdata[boxZeroPos];
	min[3] = (int)specdata[boxZeroPos];
	min[4] = (int)specdata[boxZeroPos];
	min[5] = (int)specdata[boxZeroPos];
	for (int i = peakCenter + FWHM; i<boxWidth - 1; i++) {
		if (specdata[boxZeroPos + i]<specdata[boxZeroPos + i - 1] && specdata[boxZeroPos + i]<specdata[boxZeroPos + i + 1]) {
			min[5] = min[4];
			min[4] = min[3];
			min[3] = specdata[boxZeroPos + i];
			if (fabs(min[3] - min[4]) <= noise && fabs(min[3] - min[5]) <= noise && cnt >= 3)
				i = boxWidth;
		}
	}
	if (cnt<3 && cnt>0) {
		for (unsigned char i = 5; i>2 + cnt; i--)
			min[i] = min[3];
	}
	else if (cnt == 0) {
		for (unsigned char i = 5; i>2; i--)
			min[i] = specdata[boxZeroPos + boxWidth - 1];
	}
	cnt = 0;
	for (int i = (peakCenter - FWHM>0 ? (peakCenter - FWHM) : 1); i>1; i--) {
		if (specdata[boxZeroPos + i]<specdata[boxZeroPos + i - 1] && specdata[boxZeroPos + i]<specdata[boxZeroPos + i + 1]) {
			min[0] = min[1];
			min[1] = min[2];
			min[2] = specdata[boxZeroPos + i];
			cnt++;
			if (fabs(min[1] - min[0]) <= noise && fabs(min[1] - min[2]) <= noise && cnt >= 3)
				i = 1;
		}
	}
	if (min[0]<0)
		min[0] = 0;
	if (cnt<3 && cnt>0) {
		for (unsigned char i = 0; i<3 - cnt; i++)
			min[i] = min[2];
	}
	else if (cnt == 0) {
		for (unsigned char i = 0; i<3; i++)
			min[i] = specdata[boxZeroPos];
	}
	for (int i = (peakCenter>FWHM ? (peakCenter - FWHM) : 0); i<peakCenter + FWHM; i++) {
		max = (max>specdata[boxZeroPos + i]) ? max : specdata[boxZeroPos + i];
	}
	if (min[0]<0 || min[1]<0 || min[2]<0 || min[3]<0 || min[4]<0 || min[5]<0)
		return 0;

	max = max - (unsigned int)((min[0] + min[1] + min[2] + min[3] + min[4] + min[5]) / 6);

	if (max<noise)
		return 0;
	/*if((max-noise)/noise>1000)
	max=max;*/
	return (max - noise) / noise;
}

/***************************************
函数名：getNewSpec
返回：平滑和重新采样的光谱（STEP1）
参数：spec  原始光谱
参数：lowline   低波数
参数：topline   高波数
参数：innerData 预留数据
说明：对光谱进行重新采样和平滑处理
修订：可能波长对的不准
***************************************/
int pack_getNewSpec(double* x,double* y, int len, int lowline, int topline, double* x_r,double* y_r,int len_r,int mode) {
	SPEC spec_r, spec;
	spec.dIntensity = y;
	spec.dWcnt = x;
	spec.len = len;
	spec_r.dIntensity = y_r;
	spec_r.dWcnt = x_r;
	spec_r.len = len_r;
	return getNewSpec(&spec, lowline, topline, &spec_r, mode);
}
int getNewSpec(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode) {
	// int i=0;

	int ret = 0;
	//smoothSpc->dWcnt=getNewWCnt(lowline,topline);
	//smoothSpc->len=topline-lowline;
	//在平滑函数中执行
	//lowline-=33;
	//topline-=33;

	if (!decodeSoft(spec->dIntensity, spec->len))
	{
		if (mode) {
			encodeSoft(spec->dIntensity, spec->len);
			ret = 2;
		}
		else
			return ret;

	}

	smooth(spec, lowline-30, topline-30, spec_r);


	//基线拉平
	encodeSoft(spec_r->dIntensity, spec_r->len);//

	autoBaseline(spec_r, (double)lowline, 20);

	return ret;
}

/***************************************
函数名：getNewWCnt
返回：平滑和重新采样的光谱（STEP1）
参数：spec  原始光谱
参数：num   spec长度
参数：lowline   低波数
参数：topline   高波数
参数：innerData 预留数据
说明：对光谱进行重新采样和平滑处理
修订：测试
***************************************/
void getNewWCnt(int lowline, int topline, double* newWcnt) {
	int i;
	for (i = lowline; i < topline; i++)
		newWcnt[i - lowline] = i+30;
	return;
}

//int autobaseline_org(double *pSpc,       //光谱缓冲区
//	double *pBkgSpc,    //背景缓冲区
//	double *pWaveCnt,   //波数缓冲区
//	double *pIntensity, //拉完基线之后的
//	int nLength,        //数据长度
//	double dLowWaveCnt, //低波数
//						//double dHighWaveCnt, //低波数
//	int nR,
//	int mode)
//{
//	memcpy(pIntensity, pSpc, sizeof(double)*nLength);
//	memcpy(pBkgSpc, pSpc, sizeof(double)*nLength);
//	return 1;
//}
//void pack_autoBaseline(double* x, double* y, int len, double dLowWaveCnt, int nR) {
//    SPEC spec;
//    spec.dIntensity = y;
//    spec.dWcnt = x;
//    spec.len = len;
//    autoBaseline(&spec, dLowWaveCnt, nR);
//}
int autobaseline_org(double *pSpc,       //光谱缓冲区
					 double *pBkgSpc,    //背景缓冲区
					 double *pWaveCnt,   //波数缓冲区
					 double *pIntensity, //拉完基线之后的
					 int nLength,        //数据长度
					 double dLowWaveCnt, //低波数
		//double dHighWaveCnt, //低波数
					 int nR,
					 int mode)
//0403修改，解决端点赋值问题
{


	//cout << nLength << endl;
	memset(pBkgSpc, 0, nLength * sizeof(double));
	memset(pIntensity, 0, nLength * sizeof(double));
	// double *pBkgSpc;
	int i, r, nlowCnt = -1, k;
	double cx, cy, r2, Spcmax = 0, Spcmin, R, t;
	double a[2][50], min;
	//int temp ;
	int begin;
	double scaleP;
	int start;
	int end;
	int stopPoint;
	double temp;
	int ntemp;
	int count;
	double *pbuff;
	int CP, jj;
	//pBkgSpc=(double *)malloc(sizeof(double)*nLength);
	//memset((void*)pBkgSpc,0,nLength*sizeof(double));

	double * p_spec_cut = pSpc;

	if (decodeSoft(pSpc, nLength) == 0 and mode==0)
	{
		xcode(pSpc, pIntensity, nLength);
		return 0;
	}

	Spcmin = pSpc[0];
	for (i = 0; i < nLength; i++)
	{
		if (i-nlowCnt > 0)
			Spcmax = (Spcmax > pSpc[i]) ? Spcmax : pSpc[i];
		Spcmin = (Spcmin < pSpc[i]) ? Spcmin : pSpc[i];
		nlowCnt = (nlowCnt == -1 && pWaveCnt[i] >= dLowWaveCnt) ? i : nlowCnt;


	}
	if (Spcmin <= 0)//解决0点死循环错误
	{
		for (i = 0; i < nLength; i++)
			pSpc[i] += Spcmin + 1;
	}
	//return 2;
	//计算起点圆心cx,xy
	scaleP = Spcmax / nLength;//根据视觉图建立圆xy尺度对应关系
	r = nR;
	i = (nlowCnt - r + 1 > 0) ? nlowCnt - r + 1 : 1;
	r2 = pow((double)r, 2);//__builtin_

	//初始化圆位于曲线下方，不与曲线相交，//由噪声决定，减背景后由负值
	start = (i - r > 0) ? i - r : 0;
	end = (i + r < nLength - 1) ? i + r : nLength - 1;
	/////////////////////cy低于光谱曲线
	min = pSpc[nlowCnt];
	for (k = start + 1; k < end; k++)
	{
		temp = (k < nlowCnt) ? pSpc[nlowCnt] : pSpc[k];
		min = (min < temp) ? min : temp;
	}
	R = r * scaleP;
	cy = min - R;
	//t;//=3*scaleP;    //上移步长

	i++;
	//for debug
	//CString str;
	//str.Format(L"i=%f,r=%d",pWaveCnt[i],r);
	//AfxMessageBox(str);
	stopPoint = nLength - 2 + r;

	//cout << "star:" << start << ",stop:" << stopPoint << endl;

	pbuff = (double *)malloc(sizeof(double)*(nLength));
	for (; i < stopPoint; i++)
	{
		ntemp = (start < nlowCnt) ? nlowCnt : start;
		min = (min != pSpc[ntemp]) ? min : pSpc[ntemp + 1];
		start = (i - r >= nlowCnt) ? ntemp + 1 : nlowCnt;//(i-r>nlowCnt)?i-r:nlowCnt;
		end = (i + r < nLength - 1) ? end + 1 : nLength - 1;
		//if(i>900)
		// end=end;
		//////////////////////////////////////////////////利用上一次的起点
		min = 0;//(min<pSpc[end])?min:pSpc[end];
		cy = min - 2 * R;
		////////////////////////////////////////////////*/
		cx = i;
		count = 2;//保证进入循环
		////////////////////////////////

		for (k = start; k < end; k++)
		{
			pbuff[k - start] = sqrt(r2 - pow(k - cx, 2))*scaleP + cy;//__builtin_
		}
		//  return 2;
		////////////////////////////////
		while (1)
		{
			count = 0;
			t = pSpc[start] - pbuff[0];
			for (k = start; k < end; k++)
			{
				if (pSpc[k] <= pbuff[k - start])
					count++;
				t = (t < pSpc[k] - pbuff[k - start]) ? t : pSpc[k] - pbuff[k - start];
			}
			if (count == 0)
			{
				for (k = 0; k <= end - start; k++)
					pbuff[k] += t;

			}
			else
				break;
			//cout << "count:" << count<<endl;
		}
		// return 2;
		//////////////////////////////////////////////确定切圆圆心
		for (k = start; k < end; k++)
		{
			if (pSpc[k] <= pbuff[k - start] && pBkgSpc[k] == 0)
			{
				pBkgSpc[k] = pSpc[k];
				/*if (k >= 1830) {
				cout << "k>length:"<<k << endl;
				}*/

			}
			/*if (k == 1831) {
				cout << "k>length" << endl;
			}*/
		}

		///////////////////////////////////////////////确定切点
	}
	free(pbuff);
	//for debug
	//AfxMessageBox(L"结束");
	//cout << "out"<<endl ;

	ntemp = 0;
	begin = 0;
	end = nLength - 1;
	/*if (pBkgSpc[nlowCnt] == 0) {
		int idx = nlowCnt+1;
		while (idx < nLength) {
			if (pBkgSpc[idx] != 0) {
				pBkgSpc[nlowCnt] = pBkgSpc[idx];
				break;
			}
			idx++;
		}


	}*/
	if (pBkgSpc[nlowCnt] == 0) {
		int idx = nlowCnt;
		while (idx < nLength && pBkgSpc[idx] == 0) {
			pBkgSpc[idx] = pSpc[idx];
			idx++;
		}
	}

	if (pBkgSpc[nLength-1] == 0) {
		int idx = nLength - 2;
		//cout << idx << endl;
		while (idx >0) {
			if (pBkgSpc[idx] != 0) {
				pBkgSpc[nLength - 1] = pBkgSpc[idx];
				break;
			}
			idx--;
		}

	}

	while (pWaveCnt[begin] < dLowWaveCnt)
	{
		pSpc[begin++] = 0;
	}
	while (pBkgSpc[begin] == 0 && begin < end)
	{
		pSpc[begin++] = 0;
	}
	while (pBkgSpc[end] == 0 && begin < end/*||pWaveCnt[begin]>dHighWaveCnt*/)
	{
		//pBkgSpc[end]=pSpc[end];
		pSpc[end] = 0;
		end--;
	}
	/*if (end >= 1831) {
		cout << "end>length" << endl;
	}*/
	for (i = begin; i <= end; i++)
	{
		if (pBkgSpc[i] == 0)
			pBkgSpc[i] = pBkgSpc[i - 1];
		else
		{
			//double a[2][50], min;
			CP = 0, jj = 0;
			min = 1000;
			//此处内存越界20210423
			while (pBkgSpc[i + jj + 1] > 0 && jj < 50 && ntemp != 0 && i + jj + 1<nLength)
			{
				a[0][jj] = (pBkgSpc[i + jj] - pBkgSpc[ntemp]) / (i + jj - ntemp);
				a[1][jj] = pBkgSpc[i + jj + 1] - pBkgSpc[i + jj];
				if (min >fabs(a[0][jj] - a[1][jj])) {
					min = fabs(a[0][jj] - a[1][jj]);
					CP = jj;
				}
				jj++;
			}
			i = i + CP;
			//

			for (k = i - 1; k >= ntemp; k--)
			{
				//cout << "k,ntemp,i" << k << "," << ntemp << "," << i << endl;
				pBkgSpc[k] = (k - ntemp)*(pBkgSpc[i] - pBkgSpc[ntemp]) / (i - ntemp) + pBkgSpc[ntemp];
				/*		if (k == 1831) {
                            cout << "k>length" << endl;
                        }*/
			}
			ntemp = i;
		}
	}

	//max=0;
	////求最大值
	//for (i=begin;i<=end;i++)
	//	{y[i]=y[i]-pBkgSpc[i];
	//     max=(y[i]>max)?y[i]:max;
	//	}

	//free(pBkgSpc);
	//cout << "done" <<endl;
	for (i = 0; i < nLength; i++)
	{
		double x = pSpc[i] - pBkgSpc[i];
		if (x < 0)
			x = 0;
		pIntensity[i] = x;

	}
	encodeSoft(pIntensity, nLength);//
	//cout << "0403" << endl;

	return 1;
}
/*20201026补充函数，用于更新差分光谱算法和原始光谱去噪，不损失特征峰强度
*/
/***************************************
函数名：smooth
返回：
参数：spec 和返回数据
说明：平滑光谱
修订：测试
***************************************/
int smooth(SPEC* spec,double para) {
	double pulse[121];
	get_pulse(121, pulse, para, 0);
	//归一化
	array_normalization_area(pulse,121);
	double *t_head=(double*)malloc((spec->len+180) * sizeof(double));
	double *cov_valsA = (double*)malloc((spec->len + 180) * sizeof(double));
	for (int i = 0; i < 90; i++) {
		t_head[i] = spec->dIntensity[0];
		t_head[spec->len + 180-1-i] = spec->dIntensity[spec->len-1];
	}
	memcpy((t_head + 90), spec->dIntensity, spec->len * sizeof(double));
	convolve_return(t_head, spec->len + 180, pulse, 121, cov_valsA);
	memcpy(spec->dIntensity, cov_valsA + 90, spec->len * sizeof(double));
	free(t_head);
	free(cov_valsA);
	return 0;
}

/***************************************
函数名：get_local_max_line
返回：
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：获取荧光包络曲线
修订：测试
***************************************/
int get_local_max_line(SPEC* spec, SPEC* spec_r )
{
	int cnt=1,i=0;
	double* max_x_lst= (double*)malloc(spec->len * sizeof(double));
	double* max_y_lst = (double*)malloc(spec->len * sizeof(double));
	double* tempx = (double*)malloc(spec->len * sizeof(double));
	/*spec_r->len = spec->len;
	spec_r->dWcnt = (double*)malloc(spec_r->len * sizeof(double));

	spec_r->dIntensity = (double*)malloc(spec_r->len * sizeof(double));*/

	max_x_lst[0] = 0;
	max_y_lst[0] = spec->dIntensity[0];
	for (i = 1; i < spec->len-1; i++) {
		if (spec->dIntensity[i] >= spec->dIntensity[i - 1] && spec->dIntensity[i] >= spec->dIntensity[i + 1]) {

			max_x_lst[cnt] = i ;
			max_y_lst[cnt++] = spec->dIntensity[i];
		}
	}
	if (max_x_lst[cnt - 1] != spec->len - 1) {
		max_x_lst[cnt] = spec->len - 1;
		max_y_lst[cnt] = spec->dIntensity[spec->len - 1];
	}
	//ofstream out1("D://DR//peak_pos_spec.csv");
	//if (out1.is_open()) {
	//	for (int i = 0; i <cnt; i++) {
	//		out1 << max_x_lst[i] << "," << max_y_lst[i]<< endl;
	//	}
	//}
	//out1.close();


	LinearInterpolate(cnt, max_x_lst, max_y_lst, 0, spec->len , tempx, spec_r ->dIntensity );
	free(max_x_lst);
	free(max_y_lst);
	free(tempx);
	return 0;
}

/***************************************
函数名：get_local_max_line_2
返回：
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：获取荧光包络曲线,不接受单边局部最大值
修订：测试
***************************************/
int get_local_max_line_2(SPEC* spec, SPEC* spec_r)
{
	int cnt = 1, i = 0;
	double* max_x_lst = (double*)malloc(spec->len * sizeof(double));
	double* max_y_lst = (double*)malloc(spec->len * sizeof(double));
	double* tempx = (double*)malloc(spec->len * sizeof(double));
	/*spec_r->len = spec->len;
	spec_r->dWcnt = (double*)malloc(spec_r->len * sizeof(double));

	spec_r->dIntensity = (double*)malloc(spec_r->len * sizeof(double));*/

	max_x_lst[0] = 0;
	max_y_lst[0] = spec->dIntensity[0];
	for (i = 1; i < spec->len - 1; i++) {
		if (spec->dIntensity[i] > spec->dIntensity[i - 1] && spec->dIntensity[i] > spec->dIntensity[i + 1]) {

			max_x_lst[cnt] = i;
			max_y_lst[cnt++] = spec->dIntensity[i];
		}
	}
	if (max_x_lst[cnt - 1] != spec->len - 1) {
		max_x_lst[cnt] = spec->len - 1;
		max_y_lst[cnt] = spec->dIntensity[spec->len - 1];
	}
	//ofstream out1("D://DR//peak_pos_spec.csv");
	//if (out1.is_open()) {
	//	for (int i = 0; i <cnt; i++) {
	//		out1 << max_x_lst[i] << "," << max_y_lst[i]<< endl;
	//	}
	//}
	//out1.close();


	LinearInterpolate(cnt, max_x_lst, max_y_lst, 0, spec->len, tempx, spec_r->dIntensity);
	free(max_x_lst);
	free(max_y_lst);
	free(tempx);
	return 0;
}
/***************************************
函数名：get_local_min_line
返回：
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：获取荧光包络曲线
修订：测试
***************************************/
int get_local_min_line(SPEC* spec, SPEC* spec_r)
{
	int cnt = 1, i = 0;
	double* max_x_lst = (double*)malloc(spec->len * sizeof(double));
	double* max_y_lst = (double*)malloc(spec->len * sizeof(double));
	double* tempx = (double*)malloc(spec->len * sizeof(double));
	/*spec_r->len = spec->len;
	spec_r->dWcnt = (double*)malloc(spec_r->len * sizeof(double));
	spec_r->dIntensity = (double*)malloc(spec_r->len * sizeof(double));*/

	max_x_lst[0] = 0;
	max_y_lst[0] = spec->dIntensity[0];
	for (i = 1; i < spec->len-1; i++) {
		if (spec->dIntensity[i] <= spec->dIntensity[i - 1] && spec->dIntensity[i] <= spec->dIntensity[i + 1]) {
			max_x_lst[cnt] = i;
			max_y_lst[cnt++] = spec->dIntensity[i];
		}
	}
	if (max_x_lst[cnt - 1] != spec->len - 1) {
		max_x_lst[cnt] = spec->len - 1;
		max_y_lst[cnt++] = spec->dIntensity[spec->len - 1];
	}


	LinearInterpolate(cnt, max_x_lst, max_y_lst, 0, spec->len , tempx, spec_r->dIntensity);
	free(max_x_lst);
	free(max_y_lst);
	free(tempx);
	return 0;
}
/***************************************
函数名：get_local_min_line2
返回：
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：获取荧光包络曲线,不接受单侧最小
修订：测试
***************************************/
int get_local_min_line_2(SPEC* spec, SPEC* spec_r)
{
	int cnt = 1, i = 0;
	double* max_x_lst = (double*)malloc(spec->len * sizeof(double));
	double* max_y_lst = (double*)malloc(spec->len * sizeof(double));
	double* tempx = (double*)malloc(spec->len * sizeof(double));
	/*spec_r->len = spec->len;
	spec_r->dWcnt = (double*)malloc(spec_r->len * sizeof(double));
	spec_r->dIntensity = (double*)malloc(spec_r->len * sizeof(double));*/

	max_x_lst[0] = 0;
	max_y_lst[0] = spec->dIntensity[0];
	for (i = 1; i < spec->len - 1; i++) {
		if (spec->dIntensity[i] < spec->dIntensity[i - 1] && spec->dIntensity[i] < spec->dIntensity[i + 1]) {
			max_x_lst[cnt] = i;
			max_y_lst[cnt++] = spec->dIntensity[i];
		}
	}
	if (max_x_lst[cnt - 1] != spec->len - 1) {
		max_x_lst[cnt] = spec->len - 1;
		max_y_lst[cnt++] = spec->dIntensity[spec->len - 1];
	}


	LinearInterpolate(cnt, max_x_lst, max_y_lst, 0, spec->len, tempx, spec_r->dIntensity);
	free(max_x_lst);
	free(max_y_lst);
	free(tempx);
	return 0;
}
/***************************************
函数名：get_smooth_line
返回：
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：获取荧光包络曲线
修订：测试
***************************************/
int get_smooth_line(SPEC* spec,  SPEC* spec_r,double para)
{
	double pulse[121];
	/*spec_r->dIntensity = (double*)malloc((spec->len) * sizeof(double));
	spec_r->len = spec->len;*/
	get_pulse(121, pulse, para, 0);
	//归一化
	array_normalization_area(pulse, 121);
	double *t_head = (double*)malloc((spec->len + 180) * sizeof(double));
	double *cov_valsA = (double*)malloc((spec->len + 180) * sizeof(double));
	for (int i = 0; i < 90; i++) {
		t_head[i] = spec->dIntensity[0];
		t_head[spec->len + 180 - 1 - i] = spec->dIntensity[spec->len - 1];
	}
	memcpy((t_head + 90), spec->dIntensity, spec->len * sizeof(double));
	convolve_return(t_head, spec->len + 180, pulse, 121, cov_valsA);


	memcpy(spec_r->dIntensity, cov_valsA + 90, spec->len * sizeof(double));
	free(t_head);
	free(cov_valsA);

	return 0;
}
/***************************************
函数名：get_smooth_line_2
返回：
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：获取荧光包络曲线
修订：测试
***************************************/
int get_smooth_line_2(SPEC spec_top, SPEC spec_bottom, SPEC* spec_r){
	/*spec_r->len = spec_top.len;
	spec_r->dIntensity = (double*)malloc(spec_top.len * sizeof(double));*/
	for (int i = 0; i < spec_r->len; i++)
		spec_r->dIntensity[i] = (spec_top.dIntensity[i] + spec_bottom.dIntensity[i]) / 2;
	return 0;
}
/***************************************
函数名：get_baseline
返回：噪声的标准偏差
参数：spec   原始光谱
参数：spec_r 返回光谱
参数：para   影响曲线平滑度，已优化
说明：获取荧光包络曲线
修订：测试
***************************************/
double get_baseline(SPEC* spec, SPEC* spec_r,double para){
	SPEC spec_top;
	SPEC spec_bottom;
	SPEC spec_mean_1;
	SPEC spec_mean_2;
	spec_top.dWcnt = spec->dWcnt;
	spec_top.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_top.len = spec->len;

	spec_bottom.dWcnt = spec->dWcnt;
	spec_bottom.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_bottom.len = spec->len;

	spec_mean_2.dWcnt = spec->dWcnt;
	spec_mean_2.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_mean_2.len = spec->len;

	spec_mean_1.dWcnt = spec->dWcnt;
	spec_mean_1.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_mean_1.len = spec->len;
	//取上包络线
	get_local_max_line(spec,  &spec_top);
	// 取下包络线
	get_local_min_line(spec, &spec_bottom);
	//均值线a
	get_smooth_line(spec,  &spec_mean_1, para);
	get_smooth_line_2(spec_top, spec_bottom, &spec_mean_2);
	double noise =0;
	int j = 0;
	for (int i = 0; i < spec->len; i++) {
		if (spec->dIntensity[i] > spec_mean_1.dIntensity[i] && spec_mean_1.dIntensity[i] > spec_mean_2.dIntensity[i]) {
			;
		}
		else if (spec->dIntensity[i] < spec_mean_1.dIntensity[i] && spec_mean_1.dIntensity[i] < spec_mean_2.dIntensity[i])
			;
		else {
			noise+= (spec->dIntensity[i] - spec_mean_1.dIntensity[i]) *(spec->dIntensity[i] - spec_mean_1.dIntensity[i]);
			noise += (spec_mean_2.dIntensity[i] - spec_mean_1.dIntensity[i]) *(spec_mean_2.dIntensity[i] - spec_mean_1.dIntensity[i]);
			j += 2;
		}
	}
	if (j < 2)
		noise = 0;
	else
		noise = sqrt(noise / j - 1);

	for (int i = 0; i < spec->len; i++) {
		if (spec_mean_1.dIntensity[i] > spec_top.dIntensity[i]) {
			spec_r->dIntensity[i] = spec_bottom.dIntensity[i];
		}
		else if (spec_top.dIntensity[i] - spec_bottom.dIntensity[i] > noise) {
			spec_r->dIntensity[i] = spec_bottom.dIntensity[i];
		}
		else if(spec->dIntensity[i] -spec_mean_1.dIntensity[i]>noise)
			spec_r->dIntensity[i] = spec_bottom.dIntensity[i];
		else {
			spec_r->dIntensity[i] = spec_mean_1.dIntensity[i];
		}
	}

	smooth(spec_r,para);
	free(spec_top.dIntensity);
	free(spec_bottom.dIntensity);
	free(spec_mean_1.dIntensity);
	free(spec_mean_2.dIntensity);


	/*ofstream out1("D://DR//peak_pos_spec.csv");
	if (out1.is_open()) {
		for (int i = 0; i < spec->len; i++) {
		out1 << spec->dWcnt[i] << "," << spec->dIntensity[i]
		<< "," << spec_r->dIntensity[i]<< endl;
		}
	}
	out1.close();*/

	return noise;
}
/***************************************
函数名：get_centerline
返回：噪声的标准偏差
参数：spec   原始光谱
参数：spec_r 返回光谱
参数：para   影响曲线平滑度，已优化
说明：获取荧光包络曲线
修订：测试
***************************************/

double get_centerline(SPEC* spec,SPEC* spec_r, double para) {
	SPEC spec_top;
	SPEC spec_bottom;
	SPEC spec_mean_1;
	SPEC spec_mean_2;
	spec_top.dWcnt = spec->dWcnt;
	spec_top.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_top.len = spec->len;

	spec_bottom.dWcnt = spec->dWcnt;
	spec_bottom.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_bottom.len = spec->len;

	spec_mean_2.dWcnt = spec->dWcnt;
	spec_mean_2.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_mean_2.len = spec->len;

	spec_mean_1.dWcnt = spec->dWcnt;
	spec_mean_1.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_mean_1.len = spec->len;
	//取上包络线
	get_local_max_line(spec,  &spec_top);
	// 取下包络线
	get_local_min_line(spec,  &spec_bottom);
	//均值线a
	get_smooth_line(spec,  &spec_mean_1, para);
	get_smooth_line_2(spec_top, spec_bottom, &spec_mean_2);

	/*ofstream out1("D://DR//peak_pos_spec.csv");
	if (out1.is_open()) {
		for (int i = 0; i < spec->len; i++) {
			out1 << spec->dWcnt[i] << "," << spec_top.dIntensity[i]
			    << "," << spec_bottom.dIntensity[i]<< ","
				<< spec_mean_1.dIntensity[i]
			    << "," << spec_mean_2.dIntensity[i] << endl;
		}
	}
	out1.close();*/

	double noise = 0;
	double* temp_noise_mem = (double*)malloc(sizeof(double) * 4 * spec->len);
	int temp_noise_mem_len = 1;
	temp_noise_mem[0] = spec->dWcnt[0];
	temp_noise_mem[0+ spec->len] = 0;
	temp_noise_mem[0 + 2 * spec->len] = 0;
	for (int i = 1; i < spec->len-1; i++) {
		if (spec->dIntensity[i] >= spec->dIntensity[i-1] && spec->dIntensity[i] >= spec->dIntensity[i + 1]) {
			//上极大值
			temp_noise_mem[temp_noise_mem_len] = spec->dWcnt[i];
			temp_noise_mem[temp_noise_mem_len + spec->len] = spec->dIntensity[i];
			temp_noise_mem[temp_noise_mem_len + 2 * spec->len] = 0;
			temp_noise_mem_len++;
		}else if(spec->dIntensity[i] <= spec->dIntensity[i - 1] && spec->dIntensity[i] <= spec->dIntensity[i + 1]) {
			//下极大值
			temp_noise_mem[temp_noise_mem_len] = spec->dWcnt[i];
			temp_noise_mem[temp_noise_mem_len + spec->len] = spec->dIntensity[i];
			temp_noise_mem[temp_noise_mem_len + 2 * spec->len] = 1;
			temp_noise_mem_len++;
		}
	}
	if(temp_noise_mem[1 + 2 * spec->len]==0)
		temp_noise_mem[0 + 2 * spec->len] = 1;

	temp_noise_mem[temp_noise_mem_len] = spec->dWcnt[spec->len - 1];
	temp_noise_mem[temp_noise_mem_len + spec->len] = spec->dIntensity[spec->len - 1];

	if(temp_noise_mem[temp_noise_mem_len-1 + 2 * spec->len] == 1)
		temp_noise_mem[temp_noise_mem_len + 2 * spec->len] = 0;
	else
		temp_noise_mem[temp_noise_mem_len + 2 * spec->len] = 1;

	int j = 0;

	//选出极大值极小值距离小于2的点作为噪声
	for (int i = 1; i < temp_noise_mem_len; i++)
	{
		if (temp_noise_mem[i] - temp_noise_mem[i - 1] < 2 && temp_noise_mem[i + 1] - temp_noise_mem[i] < 2)
		{
			if (temp_noise_mem[i + 2 * spec->len] != temp_noise_mem[i - 1 + 2 * spec->len] &&
				temp_noise_mem[i + 2 * spec->len] != temp_noise_mem[i - 1 + 2 * spec->len])
			{
				if (temp_noise_mem[i + 2 * spec->len] > 0)
					temp_noise_mem[3 * spec->len + j] = (temp_noise_mem[i - 1 + spec->len] + temp_noise_mem[i + 1 + spec->len]
														 - temp_noise_mem[i + spec->len] - temp_noise_mem[i + spec->len]);
				else
					temp_noise_mem[3 * spec->len + j] = -(temp_noise_mem[i - 1 + spec->len] + temp_noise_mem[i + 1 + spec->len]
														  - temp_noise_mem[i + spec->len] - temp_noise_mem[i + spec->len]);
				j++;
			}
		}
	}

	//排序找到提升超过1.05的作为分界点
	sort(&temp_noise_mem[3 * spec->len], &temp_noise_mem[j + 3 * spec->len]);
	int cnt = 1;
	noise = temp_noise_mem[3 * spec->len+1];
	for (int i = 1; i < j; i++)
	{
		noise += temp_noise_mem[i + 3 * spec->len];
		cnt++;

		if (temp_noise_mem[i + 3 * spec->len] > 50 &&
			temp_noise_mem[i + 3 * spec->len] - temp_noise_mem[i - 1 + 3 * spec->len] > temp_noise_mem[i + 3 * spec->len] * 0.05)
			break;
	}
	if(cnt>1)
		noise /= cnt;
	else
		noise =1;
	free(temp_noise_mem);
	//小于分界点的数据作为噪声
	//cout << j<<endl;
	//---------------------------------------------------------------------------------
	/*int j = 0;
	for (int i = 0; i < spec->len; i++) {
		if (spec->dIntensity[i] > spec_mean_1.dIntensity[i] && spec_mean_1.dIntensity[i] > spec_mean_2.dIntensity[i]) {
			;
		}
		else if (spec->dIntensity[i] < spec_mean_1.dIntensity[i] && spec_mean_1.dIntensity[i] < spec_mean_2.dIntensity[i])
			;
		else {
			noise += (spec->dIntensity[i] - spec_mean_1.dIntensity[i]) *(spec->dIntensity[i] - spec_mean_1.dIntensity[i]);
			noise += (spec_mean_2.dIntensity[i] - spec_mean_1.dIntensity[i]) *(spec_mean_2.dIntensity[i] - spec_mean_1.dIntensity[i]);
			j+=2;
		}
	}*/

	//-------------------------------------------------------------------
	/*int cnt = 4;
	int cnt2 = 4;
	int j = 0;
	for (int i = 0; i < spec->len; i++) {
		if (spec->dIntensity[i] > spec_mean_1.dIntensity[i])
		{
			cnt2 = 0;
			if (cnt <= 2)
			{cnt += 1;
			noise += (spec->dIntensity[i] - spec_mean_1.dIntensity[i]) *(spec->dIntensity[i] - spec_mean_1.dIntensity[i]);
			j += 1;
			}
		}
		else if (spec->dIntensity[i] < spec_mean_1.dIntensity[i])
		{
			cnt = 0;
			if (cnt2 <= 2)
			{cnt2 += 1;
			noise += (spec->dIntensity[i] - spec_mean_1.dIntensity[i]) *(spec->dIntensity[i] - spec_mean_1.dIntensity[i]);
			j += 1;
			}
		}
		else
		{
			cnt = 0;
			cnt2 = 0;
			noise += (spec->dIntensity[i] - spec_mean_1.dIntensity[i]) *(spec->dIntensity[i] - spec_mean_1.dIntensity[i]);
			j += 1;
		}
	}*/
	//----------------------------------------------------------------------------------
	//if (j < 2)
	//	noise = 1;//防止除0错误
	//else
	//	noise = sqrt(noise / (j - 1));

	//for (int i = 0; i < spec->len; i++) {
	//	if (spec->dIntensity[i]-spec_mean_1.dIntensity[i] > 3*noise) {
	//		spec_r->dIntensity[i] = spec->dIntensity[i];
	//	}
	//	else if (spec_mean_1.dIntensity[i]- spec->dIntensity[i]> 3*noise) {
	//		spec_r->dIntensity[i] = spec->dIntensity[i];
	//	}
	//	//else {
	//		spec_r->dIntensity[i] = spec_mean_1.dIntensity[i];
	//	//}
	//}
	//
	free(spec_top.dIntensity);
	free(spec_bottom.dIntensity);
	free(spec_mean_1.dIntensity);
	free(spec_mean_2.dIntensity);


	/*ofstream out1("D://DR//peak_pos_spec.csv");
	if (out1.is_open()) {
		for (int i = 0; i < spec->len; i++) {
			out1 << spec->dWcnt[i] << "," << spec->dIntensity[i]
			    << "," << spec_r->dIntensity[i]<< endl;
		}
	}
	out1.close();*/

	return noise;
}

/***************************************
函数名：getNewSpec_20201030
返回：平滑和重新采样的光谱（STEP1）
参数：spec  原始光谱
参数：lowline   低波数
参数：topline   高波数
参数：innerData 预留数据
说明：对光谱进行重新采样和平滑处理
修订：可能波长对的不准
***************************************/
int getNewSpec_20201030(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode) {
	// int i=0;

	int ret = 0;
	if (!decodeSoft(spec->dIntensity, spec->len))
	{
		if (mode) {
			encodeSoft(spec->dIntensity, spec->len);
			ret = 2;
		}
		else
			return ret;

	}
	SPEC spec_new, spec_baseline;
	spec_new.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_new.dWcnt = spec->dWcnt;
	spec_new.len = spec->len;

	spec_baseline.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_baseline.dWcnt = spec->dWcnt;
	spec_baseline.len = spec->len;

	get_baseline(spec, &spec_baseline);
	get_centerline(spec, &spec_new);

	double * temp = (double*)malloc(spec->len * sizeof(double));

	for (int i = 0; i < spec->len; i++) {
		temp[i] = spec_new.dIntensity[i] - spec_baseline.dIntensity[i];
		if (temp[i] < 0)
			temp[i] = 0;
	}
	free(spec_baseline.dIntensity);
	free(spec_new.dIntensity);
	LinearInterpolate(spec->len, spec->dWcnt, spec_r->dIntensity, lowline, topline, spec_r->dWcnt, spec_r ->dIntensity);

	//插值
	//基线拉平
	encodeSoft(spec_r->dIntensity, spec_r->len);//


	//autoBaseline(spec_r, (double)lowline, 35);

	return ret;
}

double getNewSpec_20201101(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode) {
	// int i=0;

	int ret = 0;
	if (!decodeSoft(spec->dIntensity, spec->len))
	{
		if (mode) {
			encodeSoft(spec->dIntensity, spec->len);
			ret = 2;
		}
		else
			return ret;

	}
	SPEC spec_new;
	spec_new.dIntensity = (double*)malloc(spec->len * sizeof(double));
	spec_new.dWcnt = spec->dWcnt;
	spec_new.len = spec->len;

	double noise=get_centerline(spec, &spec_new);
	//比较效果
	//LinearInterpolate(spec->len, spec->dWcnt, spec->dIntensity, lowline, topline, spec_r->dWcnt, spec_r->dIntensity);
	LinearInterpolate(spec->len, spec->dWcnt, spec_new.dIntensity, lowline, topline, spec_r->dWcnt, spec_r->dIntensity);
	free(spec_new.dIntensity);

	//插值
	//基线拉平
	encodeSoft(spec_r->dIntensity, spec_r->len);
	autoBaseline(spec_r, (double)lowline, 130);

	return noise;
}

void kmfilter(double* spec, int len, double noise)
{
	double* xhat = (double*)malloc(sizeof(double) * len);
	double* P = (double*)malloc(sizeof(double) * len);
	double* xhatm = (double*)malloc(sizeof(double) * len);
	double* Pm = (double*)malloc(sizeof(double) * len);
	double* K = (double*)malloc(sizeof(double) * len);
	double* V = (double*)malloc(sizeof(double) * len);
	double R = noise * noise;
	double max_v = 0, min_v = spec[0];
	for (int i = 1; i < len; i++)
	{
		max_v = (max_v > spec[i]) ? max_v : spec[i];
		min_v = (min_v < spec[i]) ? min_v : spec[i];
	}
	double Q = (max_v - min_v) * (max_v - min_v) * 0.02;
	double H = 1;

	xhat[0] = spec[0];
	P[0] = 1;
	V[0] = spec[1] - spec[0];

	for (int k = 1; k < len; k++) {
		if (k < 2 || V[k - 1] < 0) {
			xhatm[k] = xhat[k - 1];
		}
		else if (V[k - 1] * V[k] > 0)
			xhatm[k] = xhat[k - 1] + V[k - 1];
		else
			xhatm[k] = xhat[k - 1];

		double A = xhatm[k] / (xhat[k - 1] + 0.001);
		Pm[k] = A * P[k - 1] + Q;
		K[k] = Pm[k] / (Pm[k] + R);
		xhat[k] = xhatm[k] + K[k] * (spec[k] - H * xhatm[k]);
		P[k] = (1 - K[k] * H) * Pm[k];
		V[k] = xhat[k] - xhat[k - 1];
	}
	memcpy(spec, xhat, len * sizeof(double));

	free(xhat);
	free(xhatm);
	free(P);
	free(Pm);
	free(K);
	free(V);
}

double getNewSpec_20210308(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode) {
	//20210729修改，此处将原始光谱短波数前部分删除
	//清理无用变量spec_new
	// int i=0;

	int ret = 0;
	if (!decodeSoft(spec->dIntensity, spec->len))
	{
		if (mode) {
			encodeSoft(spec->dIntensity, spec->len);
			ret = 2;
		}
		else
			return ret;

	}
	SPEC spec_new;
	//spec_new.dWcnt = spec->dWcnt;
	//spec_new.len = spec->len;
	//spec_new.dIntensity=
	int start = 0;
	for (start = 0; spec->dWcnt[start] < (double)lowline; start++) {
		;
	}
	int len = start;
	while (len < spec->len) {
		if (spec->dWcnt[len] >= (double)topline) {
			len -= start;
			break;
		}
		else
			len += 1;
	}
	memcpy(spec->dWcnt, spec->dWcnt+start, len * sizeof(double));
	memcpy(spec->dIntensity, spec->dIntensity + start, len * sizeof(double));
	spec->len = len;

	double* spec_m = (double*)malloc(sizeof(double) * spec->len);
	for (int i = 0; i < spec->len; i++) {
		spec_m[i] = spec->dIntensity[spec->len - 1 - i];
	}
	double noise = get_centerline(spec, &spec_new);
	kmfilter(spec->dIntensity, spec->len, noise);
	kmfilter(spec_m, spec->len, noise);

	for (int i = 0; i < spec->len; i++) {
		spec->dIntensity[i] = spec->dIntensity[i] + spec_m[spec->len - 1 - i];
		//		for debug 直接传递结果
		//		spec_r->dIntensity[i]=spec_new.dIntensity[i];
		//		spec_r->dWcnt[i]=spec->dWcnt[i];
		//spec_new.dWcnt[i]-=4.5;
	}

	//�Ƚ�Ч��

//    spec_r->len=spec->len;
	LinearInterpolate(spec->len, spec->dWcnt, spec->dIntensity, lowline, topline, spec_r->dWcnt, spec_r->dIntensity);
	//    测试直接对输入插值
	//	LinearInterpolate(spec->len, spec->dWcnt, spec_new.dIntensity, lowline, topline, spec_r->dWcnt, spec_r->dIntensity);
	//free(spec_new.dIntensity);
	free(spec_m);
	//    for (int i=0;i<spec->len;i++){
	//        spec->dIntensity[i]=spec_r->dIntensity[i];
	//    }


	//平滑包含编码
	smooth(spec_r, 4);

	//��ֵ
	//������ƽ
	encodeSoft(spec_r->dIntensity, spec_r->len);
	autoBaseline(spec_r, (double)lowline, 130); // 20220915 FS 经跟刘博沟通后注释并替换以下拉基线方法

//    int _len=spec_r->len;
//    double *_pSpc=(double*)malloc(sizeof(double) * _len);
//    double *_pBkgSpc=(double*)malloc(sizeof(double) * _len);
//    double *_pWaveCnt=(double*)malloc(sizeof(double) * _len);
//    double *_pIntensity=(double*)malloc(sizeof(double) * _len);
//
//    memcpy(_pWaveCnt, spec_r->dWcnt, _len * sizeof(double));
//    memcpy(_pSpc, spec_r->dIntensity, _len * sizeof(double));
//    autobaseline_org(_pSpc,_pBkgSpc,_pWaveCnt,_pIntensity,_len,(double)lowline,55,1);
//
//    memcpy(spec_r->dIntensity, _pIntensity, _len * sizeof(double));
//    free(_pSpc);
//    free(_pBkgSpc);
//    free(_pWaveCnt);
//    free(_pIntensity);
	return noise;
}
/***************************************
函数名：_clear_feak_peaks
参数：double* spec, int len 光谱数据
参数：minSNR 最大信噪比
参数：width 限制宽度
说明：清除宽度过窄的特征峰，和强度太弱的特征峰
***************************************/
void clear_feak_peaks(double* spec, int len, int width, double minSNR) {
	int key = 0;
	tmpeak one_peak;
	PLST plist;
	double max_peak_hight = 0;
	for (int i = 1; i<len - 1; i++) {
		if (spec[i]>0) {
			if (key == 0) {//第一个像素
				//记录开始idx
				one_peak.pos = i;
				one_peak.maxv = spec[i];
			}
			one_peak.maxv = one_peak.maxv>spec[i] ? one_peak.maxv : spec[i];
			key += 1;

		}
		else if (spec[i] == 0 && key>0) {//结束像素
			//记录长度和最大值
			one_peak.len = key;
			plist.push_back(one_peak);
			max_peak_hight = (max_peak_hight < one_peak.maxv) ? max_peak_hight : one_peak.maxv;
			key = 0;
		}
	}

	for (PLST::iterator one_peak = plist.begin(); one_peak != plist.end(); one_peak++) {
		//取出pos，maxv 和 len，

		int peaklen = (*one_peak).len, pos = (*one_peak).pos;
		double maxv = (*one_peak).maxv;
		/*if (peaklen<width || max_peak_hight>minSNR*maxv) {
		memset(&spec[pos], 0, peaklen * sizeof(double));
		}*/
		if (peaklen<width || maxv<minSNR) {
			memset(&spec[pos], 0, peaklen * sizeof(double));
		}
	}
	for (int i = 0; i < len; i++) {
		if (spec[i] < 0)
			spec[i] = 0;
	}
    encodeSoft(spec, len);
}
//11月15日补充-------------------------------------------------------------
//增加提取基线函数（通过2分法获得光谱基线轮廓）

/***************************************
函数名：bottom_line_seg_lst
返回：第二段光谱的初始位置
参数：double* x, double*y,double* baseline,int len,输入和输出曲线
参数：int start_pos,int end_pos 曲线的其实位置，和终止位置
	  end_pos为等于最后像素位置+1
说明：二分分段，将分段值线保存在baseline中，并返回第二段曲线位置。
      当不能再分时返回-1
修订：测试
***************************************/
//轮廓连线
typedef struct End_POINT {
	/*double xa1;
	double ya1;
	double xa2;
	double ya2;*/
	int start_idx;
	int end_idx;
	double a;
	double b;
	/*double xb1;
	double yb1;
	double xb2;
	double yb2;*/
	int along;
	//int blong
}EP;
typedef std::list<End_POINT > EP_LST;

/***************************************
函数名：bottom_line
返回：第二条曲线的起始位置，-1表示不分段
参数：spec   原始光谱
参数：ep，返回两条曲线的端点
说明：计算轮廓连线的端点
      长度<100个像素
      或者端点为极值不再细分
修订：测试
***************************************/
int bottom_line(double* x, double*y,int len,EP* ep,
				int start_pos,int end_pos)
{


	int seg1_len = end_pos - start_pos;
	if (seg1_len == 1)
	{
		/*ep->xa1 = x[start_pos];
		ep->ya1 = y[start_pos];
		ep->xa2 = x[start_pos];
		ep->ya2 = y[start_pos];*/
		ep->start_idx = start_pos;
		//最后一个点idx+1
		ep->end_idx = end_pos;
		ep->along = 1;
		//cout << start_pos << "," << end_pos << endl;
		return -1;

	}
	//旋转曲线
	double a = (y[end_pos-1] - y[start_pos]) / (x[end_pos-1] - x[start_pos]);
	double b = y[start_pos] - a * x[start_pos];

	//取最小值位置
	double min_v = DBL_MAX;
	int min_id = start_pos;
	for (int i = start_pos; i < end_pos; i++)
	{
		if (y[i] - a * x[i] - b < min_v)
		{
			min_id = i;
			min_v = y[i] - a * x[i] -b;
		}
	}
	//如果点很靠边就不分了
	if (min_id < start_pos+10|| min_id > end_pos-11|| seg1_len <100)
	{
		/*ep->xa1 = x[start_pos];
		ep->ya1 = y[start_pos]+ min_v;
		ep->xa2 = x[end_pos-1];
		ep->ya2 = y[end_pos-1]+ min_v;*/
		ep->a = a;
		ep->b = b-min_v;
		ep->start_idx = start_pos;
		//最后一个点idx+1
		ep->end_idx = end_pos;
		ep->along = end_pos- start_pos;
		/*cout << start_pos << "," << end_pos << endl;
		cout << x[start_pos] << "," << y[start_pos] << endl;
		cout << x[end_pos-1] << "," << y[end_pos-1] << endl;
		cout << a << "," << b << endl;
		cout <<"----------------" << endl;*/
		return -1;
	}

	//返回第二条曲线的起始位置
	return min_id;
}
/***************************************
函数名：bottom_line_seg_lst
返回：无
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：二分分段，嵌套调用
修订：测试
***************************************/
void bottom_line_seg_lst(SPEC* spec,  int start_pos, int end_pos,EP_LST* ep_lst){
	EP ep;
	int second_pos = bottom_line(spec->dWcnt, spec->dIntensity,  spec->len,&ep, start_pos, end_pos);

	if (second_pos != -1)
	{
		//展开
		bottom_line_seg_lst(spec,  start_pos, second_pos, ep_lst);
		bottom_line_seg_lst(spec,  second_pos, end_pos, ep_lst);
	}
	else {
		if (ep_lst->size() == 0 and ep.start_idx != 0)
			cout << ep.start_idx << endl;
		ep_lst->push_back(ep);
	}
	//double a = (ep.ya2 - ep.ya1) / (ep.xa2 - ep.xa1);
	//double b = ep.ya1 - a * ep.xa1;
	//
	//for (int i = start_pos; i < end_pos; i++)
	//{
	//	spec_r->dIntensity[i] = a * spec->dWcnt[i] + b;
	//}
}


/***************************************
函数名：get_baseline3
返回：无
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：勾勒粗光谱的荧光背景，不处理细节
	  方法是二分分段，然后平滑

修订：测试
***************************************/
void get_baseline3(SPEC* spec, SPEC* spec_r, double para)
{
	EP_LST ep_lst;
	bottom_line_seg_lst(spec, 0, spec->len, &ep_lst);
	//解决端点不连贯问题
	//EP ep_last, ep_cur,ep_next;
	/*cout << ep_lst.size() << endl;*/
	EP_LST::iterator iter = ep_lst.begin();
	SPEC spec_temp;
	spec_temp.dWcnt = spec->dWcnt;
	spec_temp.len = spec->len;
	//cout << "C_org "<< spec->dIntensity[500]<< endl;
	//int st = (*iter).start_idx, ed = (*iter).end_idx;
	//cout << st << endl;
	if(ep_lst.size() == 1) //直接返回
	{
		for (int i = 0; i < (*iter).along; i++)
		{
			spec_r->dIntensity[i] = (*iter).a * spec_r->dWcnt[i] + (*iter).b;
		}
		//cout << "C_ epsize=0" << endl;
		return;
	}

		//else if (ep_lst.size == 2)
		//{

		//	double a1 = (*iter).a, b1 = (*iter).b;
		//	int start_pos = (*iter).start_idx;
		//	//最后一个点idx+1
		//	iter++;
		//	int end_pos = (*iter).end_idx;
		//	double a2 = (*iter).a, b2 = (*iter).b;
		//	//计算焦点x位置
		//	double min_x = (b2 - b1) / (a1 - a2);
		//	for (int i = start_pos; i < end_pos; i++)
		//	{
		//		if (spec_r->dWcnt[i]<min_x)
		//			spec_r->dIntensity[i] = a1 * spec_r->dWcnt[i] + b1;
		//		else
		//			spec_r->dIntensity[i] = a2 * spec_r->dWcnt[i] + b2;
		//	}
		//
		//}
		//if (ep_lst.size >= 3)
	else
	{
		spec_temp.dIntensity = (double*)malloc(sizeof(double)*spec_temp.len);
		double a1 = (*iter).a, b1 = (*iter).b;
		int start_pos = (*iter).start_idx;
		iter++;
		for (; iter != ep_lst.end(); iter++) {

			double a2 = (*iter).a, b2 = (*iter).b;
			int end_pos = (*iter).end_idx;
			//计算1,2曲线焦点
			//double o = (b2 - b1) / (a1 - a2);
			int key = 0;
			int b_start_pos = (*iter).start_idx;
			for (int i = start_pos; i < end_pos; i++)
			{
				double ah= a1 * spec_r->dWcnt[i] + b1;
				double bh = a2 * spec_r->dWcnt[i] + b2;

				ah = (spec->dIntensity[i] > ah) ? ah : spec->dIntensity[i];
				bh = (spec->dIntensity[i] > bh) ? bh : spec->dIntensity[i];
				double mah=ah > bh ? ah : bh;
				if (i < b_start_pos)
				{
					//spec_temp.dIntensity[i] = spec_temp.dIntensity[i] > mah ? spec_temp.dIntensity[i]  : mah;
					spec_temp.dIntensity[i] = spec_temp.dIntensity[i] > mah ? ah : mah;
				}
				else {
					//spec_temp.dIntensity[i] = mah;
					spec_temp.dIntensity[i] = spec_temp.dIntensity[i] > mah ? bh : mah;
				}

				/*if (spec_r->dWcnt[i] < o)
                {
                    spec_temp.dIntensity[i] = a1 * spec_r->dWcnt[i] + b1;
                }
                else
                {
                    if (key == 0)
                    {
                        start_pos = i;
                        key = 1;
                    }
                    spec_temp.dIntensity[i] = a2 * spec_r->dWcnt[i] + b2;

                }
                a1 = a2;
                b1 = b2;*/
			}
			a1 = a2;
			b1 = b2;
			start_pos = b_start_pos;
		}
	}
	//{
	//	spec_temp.dIntensity = (double*)malloc(sizeof(double)*spec_temp.len);
	//	double a1 = (*iter).a, b1 = (*iter).b;
	//	int start_pos = (*iter).start_idx;
	//	int end_pos_a = (*iter).end_idx;
	//	iter++;
	//	for (; iter != ep_lst.end(); iter++){
	//		double a2 = (*iter).a, b2 = (*iter).b;
	//		int start_pos_b = (*iter).start_idx;
	//		int end_pos = (*iter).end_idx;
	//		double center_v_a = spec_r->dWcnt[end_pos_a] * a1 + b1;
	//		double center_v_b = spec_r->dWcnt[start_pos_b] * a2 + b2;
	//		if (center_v_a < center_v_b)
	//		{//调整b参数
	//			for (int i = start_pos; i < end_pos_a;i++)
	//			{
	//				spec_temp.dIntensity[i]= spec_r->dWcnt[i] * a1 + b1;
	//			}
	//			a2 = (spec_r->dWcnt[end_pos] * a2 + b2 - center_v_a) / (spec_r->dWcnt[end_pos] - spec_r->dWcnt[start_pos_b]);
	//			b2 = center_v_a - spec_r->dWcnt[start_pos_b] * a2;
	//			for (int i = start_pos_b; i < end_pos; i++)
	//			{
	//				spec_temp.dIntensity[i] = spec_r->dWcnt[i] * a2 + b2;
	//			}
	//		}
	//		else if (center_v_a > center_v_b)
	//		{//调整a参数
	//			a1 = (center_v_b-spec_r->dWcnt[start_pos] * a1 + b1) / (spec_r->dWcnt[end_pos_a] - spec_r->dWcnt[start_pos]);
	//			b1 = center_v_b - spec_r->dWcnt[start_pos] * a1;
	//
	//			for (int i = start_pos; i < end_pos_a; i++)
	//			{
	//				spec_temp.dIntensity[i] = spec_r->dWcnt[i] * a1 + b1;
	//			}
	//
	//			for (int i = start_pos_b; i < end_pos; i++)
	//			{
	//				spec_temp.dIntensity[i] = spec_r->dWcnt[i] * a2 + b2;
	//			}
	//		}
	//		a1 = a2;
	//		b1 = b2;
	//		start_pos = start_pos_b;
	//		end_pos_a = end_pos;
	//	}
	//}
	//平滑
	//memcpy(spec_r->dIntensity, spec_temp.dIntensity, sizeof(double)*(spec_r->len));
	get_smooth_line(&spec_temp, spec_r, para);
	//释放资源
	//if (spec_r->dIntensity[500] > 1000)
	//{
	//	cout << spec_r->dIntensity[500];
	//	for (iter = ep_lst.begin(); iter != ep_lst.end(); iter++) {
	//		int s = (*iter).start_idx, e = (*iter).end_idx;
	//		cout <<s << "," << e << endl;
	//	}
	//}
	free(spec_temp.dIntensity);
}

//20201217 补充函数，提取极值曲线
/***************************************
函数名：get_local_extremum_line
返回：
参数：spec   原始光谱
参数：spec_r 返回光谱
说明：获取曲线极值点曲线
修订：测试
***************************************/
int get_local_extremum_line(SPEC* spec, SPEC* spec_r)
{
	int cnt = 1, i = 0;
	double* max_x_lst = (double*)malloc(spec->len * sizeof(double));
	double* max_y_lst = (double*)malloc(spec->len * sizeof(double));
	double* tempx = (double*)malloc(spec->len * sizeof(double));
	/*spec_r->len = spec->len;
	spec_r->dWcnt = (double*)malloc(spec_r->len * sizeof(double));
	spec_r->dIntensity = (double*)malloc(spec_r->len * sizeof(double));*/

	max_x_lst[0] = 0;
	max_y_lst[0] = spec->dIntensity[0];
	for (i = 1; i < spec->len - 1; i++) {
		if (spec->dIntensity[i] <= spec->dIntensity[i - 1] && spec->dIntensity[i] <= spec->dIntensity[i + 1]) {
			max_x_lst[cnt] = i;
			max_y_lst[cnt++] = spec->dIntensity[i];
		}
		else if (spec->dIntensity[i] >= spec->dIntensity[i - 1] && spec->dIntensity[i] >= spec->dIntensity[i + 1])
		{
			max_x_lst[cnt] = i;
			max_y_lst[cnt++] = spec->dIntensity[i];
		}
	}
	if (max_x_lst[cnt-1] != spec->len - 1) {
		max_x_lst[cnt] = spec->len - 1;
		max_y_lst[cnt++] = spec->dIntensity[spec->len - 1];
	}


	LinearInterpolate(cnt, max_x_lst, max_y_lst, 0, spec->len, tempx, spec_r->dIntensity);
	free(max_x_lst);
	free(max_y_lst);
	free(tempx);
	return 0;
}

void uniformbaseline(SPEC* line_a, SPEC* line_b)
{
	double area_a = 0, area_b = 0;
	for (int i = 0; i < line_a->len; i++)
	{
		area_a += line_a->dIntensity[i];
		area_b += line_b->dIntensity[i];
	}
	//比例
	area_a = area_a / area_b;
	double temp;
	for (int i = 0; i < line_a->len; i++) {
		temp = line_b->dIntensity[i] * area_a;
		if (line_a->dIntensity[i] < temp)
			//当a强度小于b按比例恢复的强度时候，修改b
		{
			//line_a->dIntensity[i] = area_a;
			line_b->dIntensity[i] = line_a->dIntensity[i] / area_a;
		}
		else
		{   //反之，修改a
			line_a->dIntensity[i] = line_b->dIntensity[i] * area_a;
			//line_b->dIntensity[i] = line_b->dIntensity[i] / area_a;
		}
	}

}


/////////////////////////////////////////////////////////////////////////////////////////

double getNewSpecSmooth_20210308(SPEC *spec, int lowline, int topline, SPEC *spec_r, int mode) {
	// int i=0;

	int ret = 0;
	if (!decodeSoft(spec->dIntensity, spec->len)) {
		if (mode) {
			encodeSoft(spec->dIntensity, spec->len);
			ret = 2;
		} else
			return ret;

	}
	SPEC spec_new;
	int start = 0;
	for (; spec->dWcnt[start] < double(lowline); start++) { ;
	}
	int len = start;
	while (len < spec->len) {
		if (spec->dWcnt[len] >= (double) topline) {
			len -= start;
			break;
		} else
			len += 1;
	}
	memcpy(spec->dWcnt, spec->dWcnt + start, len * sizeof(double));
	memcpy(spec->dIntensity, spec->dIntensity + start, len * sizeof(double));

	spec->len = len;

	double *spec_m = (double *) malloc(sizeof(double) * spec->len);
	for (int i = 0; i < spec->len; i++) {
		spec_m[i] = spec->dIntensity[spec->len - 1 - i];
	}
	double noise = get_centerline(spec, &spec_new);
	kmfilter(spec->dIntensity, spec->len, noise);
	kmfilter(spec_m, spec->len, noise);

	for (int i = 0; i < spec->len; i++) {
		spec->dIntensity[i] = spec->dIntensity[i] + spec_m[spec->len - 1 - i];
//		for debug 直接传递结果
//		spec_r->dIntensity[i]=spec_new.dIntensity[i];
//		spec_r->dWcnt[i]=spec->dWcnt[i];
		//spec_new.dWcnt[i]-=4.5;
	}

	//�Ƚ�Ч��

//    spec_r->len=spec->len;
	LinearInterpolate(spec->len, spec->dWcnt, spec->dIntensity, lowline, topline, spec_r->dWcnt,
					  spec_r->dIntensity);

//	memcpy(spec_r->dIntensity,spec->dIntensity,spec->len);
//	memcpy(spec_r->dWcnt,spec->dWcnt,spec->len);
//	spec_r->len=spec->len;

//	memcpy(spec_r->dIntensity,spec->dIntensity,spec->len*sizeof(double));
//	memcpy(spec_r->dWcnt,spec->dWcnt,spec->len*sizeof(double));
//	spec_r->len=spec->len;

//    测试直接对输入插值
//	LinearInterpolate(spec->len, spec->dWcnt, spec_new.dIntensity, lowline, topline, spec_r->dWcnt, spec_r->dIntensity);
	//free(spec_new.dIntensity);
	free(spec_m);
//    for (int i=0;i<spec->len;i++){
//        spec->dIntensity[i]=spec_r->dIntensity[i];
//    }


	//平滑包含编码
	smooth(spec_r,4);

	//��ֵ
	//������ƽ
	encodeSoft(spec_r->dIntensity, spec_r->len);
	//autoBaseline(spec_r, (double) lowline, 130);

	return noise;
}

double getNewSpecAutobase_20210308(SPEC *spec, int lowline, int topline, SPEC *spec_r, int mode) {
	// int i=0;

	int ret = 0;
	if (!decodeSoft(spec->dIntensity, spec->len)) {
		if (mode) {
			encodeSoft(spec->dIntensity, spec->len);
			ret = 2;
		} else
			return ret;

	}
	SPEC spec_new;
	int start = 0;
	for (; spec->dWcnt[start] < double(lowline); start++) { ;
	}
	int len = start;
	while (len < spec->len) {
		if (spec->dWcnt[len] >= (double) topline) {
			len -= start;
			break;
		} else
			len += 1;
	}
	memcpy(spec->dWcnt, spec->dWcnt + start, len * sizeof(double));
	memcpy(spec->dIntensity, spec->dIntensity + start, len * sizeof(double));

	spec->len = len;

	double *spec_m = (double *) malloc(sizeof(double) * spec->len);
	for (int i = 0; i < spec->len; i++) {
		spec_m[i] = spec->dIntensity[spec->len - 1 - i];
	}
	double noise = get_centerline(spec, &spec_new);
	kmfilter(spec->dIntensity, spec->len, noise);
	kmfilter(spec_m, spec->len, noise);

	for (int i = 0; i < spec->len; i++) {
		spec->dIntensity[i] = spec->dIntensity[i] + spec_m[spec->len - 1 - i];
//		for debug 直接传递结果
//		spec_r->dIntensity[i]=spec_new.dIntensity[i];
//		spec_r->dWcnt[i]=spec->dWcnt[i];
		//spec_new.dWcnt[i]-=4.5;
	}

	//�Ƚ�Ч��

//    spec_r->len=spec->len;
	LinearInterpolate(spec->len, spec->dWcnt, spec->dIntensity, lowline, topline, spec_r->dWcnt,
					  spec_r->dIntensity);

//	memcpy(spec_r->dIntensity,spec->dIntensity,spec->len);
//	memcpy(spec_r->dWcnt,spec->dWcnt,spec->len);
//	spec_r->len=spec->len;

//	memcpy(spec_r->dIntensity,spec->dIntensity,spec->len*sizeof(double));
//	memcpy(spec_r->dWcnt,spec->dWcnt,spec->len*sizeof(double));
//	spec_r->len=spec->len;

//    测试直接对输入插值
//	LinearInterpolate(spec->len, spec->dWcnt, spec_new.dIntensity, lowline, topline, spec_r->dWcnt, spec_r->dIntensity);
	//free(spec_new.dIntensity);
	free(spec_m);
//    for (int i=0;i<spec->len;i++){
//        spec->dIntensity[i]=spec_r->dIntensity[i];
//    }


	//平滑包含编码
	smooth(spec_r, 4);

	//��ֵ
	//������ƽ
	encodeSoft(spec_r->dIntensity, spec_r->len);
	autoBaseline(spec_r, (double)lowline, 130);

	return noise;
}

double getNewSpecInterpolate_20220909(SPEC *spec, int lowline, int topline, SPEC *spec_r, int mode) {
    // int i=0;

    int ret = 0;
    if (!decodeSoft(spec->dIntensity, spec->len)) {
        if (mode) {
            encodeSoft(spec->dIntensity, spec->len);
            ret = 2;
        } else
            return ret;

    }
    SPEC spec_new;
    int start = 0;
    for (; spec->dWcnt[start] < double(lowline); start++) { ;
    }
    int len = start;
    while (len < spec->len) {
        if (spec->dWcnt[len] >= (double) topline) {
            len -= start;
            break;
        } else
            len += 1;
    }
    memcpy(spec->dWcnt, spec->dWcnt + start, len * sizeof(double));
    memcpy(spec->dIntensity, spec->dIntensity + start, len * sizeof(double));

    spec->len = len;

    double *spec_m = (double *) malloc(sizeof(double) * spec->len);
    for (int i = 0; i < spec->len; i++) {
        spec_m[i] = spec->dIntensity[spec->len - 1 - i];
    }
    double noise = get_centerline(spec, &spec_new);
    kmfilter(spec->dIntensity, spec->len, noise);
    kmfilter(spec_m, spec->len, noise);

    for (int i = 0; i < spec->len; i++) {
        spec->dIntensity[i] = spec->dIntensity[i] + spec_m[spec->len - 1 - i];
//		for debug 直接传递结果
//		spec_r->dIntensity[i]=spec_new.dIntensity[i];
//		spec_r->dWcnt[i]=spec->dWcnt[i];
        //spec_new.dWcnt[i]-=4.5;
    }

    //�Ƚ�Ч��

//    spec_r->len=spec->len;
    LinearInterpolate(spec->len, spec->dWcnt, spec->dIntensity, lowline, topline, spec_r->dWcnt,
                      spec_r->dIntensity);

//	memcpy(spec_r->dIntensity,spec->dIntensity,spec->len);
//	memcpy(spec_r->dWcnt,spec->dWcnt,spec->len);
//	spec_r->len=spec->len;

//	memcpy(spec_r->dIntensity,spec->dIntensity,spec->len*sizeof(double));
//	memcpy(spec_r->dWcnt,spec->dWcnt,spec->len*sizeof(double));
//	spec_r->len=spec->len;

//    测试直接对输入插值
//	LinearInterpolate(spec->len, spec->dWcnt, spec_new.dIntensity, lowline, topline, spec_r->dWcnt, spec_r->dIntensity);
    //free(spec_new.dIntensity);
    free(spec_m);
//    for (int i=0;i<spec->len;i++){
//        spec->dIntensity[i]=spec_r->dIntensity[i];
//    }


    //平滑包含编码
   // smooth(spec_r, 4);

    //��ֵ
    //������ƽ
    encodeSoft(spec_r->dIntensity, spec_r->len);
    //autoBaseline(spec_r, (double)lowline, 130);

    return noise;
}

///////////////////////////////////////////////////////////////////////////////////////


void normalize_curve_f(double signal[], int signal_length) {
	double max = signal[0];
	double min = signal[0];
	for (int i = 1; i < signal_length; i++) {
		if (signal[i] >= max)
			max = signal[i];
		else;
		if (signal[i] <= min)
			min = signal[i];
		else;
	}
	double max_min = max - min;
	for (int i = 0; i < signal_length; i++) {
		signal[i] = max_min==0 ? (signal[i] - min) * 12000: ((signal[i] - min) / max_min) * 12000;
	}
}

void normalize_curve_l(double signal[], int signal_length) {
    double max = signal[0];
    double min = signal[0];
    for (int i = 1; i < signal_length; i++) {
        if (signal[i] >= max)
            max = signal[i];
        else;
        if (signal[i] <= min)
            min = signal[i];
        else;
    }
    double max_min = max - min;
    for (int i = 0; i < signal_length; i++) {
        signal[i] = max_min==0 ? (signal[i] - min) : ((signal[i] - min) / max_min);
    }
}

double aver(double sig[],
			int signal_length) {
	double sum_all = 0.0;
	for (int i = 0; i < signal_length; i++) {
		sum_all = sum_all + sig[i];
	}
	sum_all = sum_all / signal_length;
	return sum_all;
}




double match_value_cosd_f(double data1x[], double data1y[], double data2x[], double data2y[],
						  int sig_num)//
{
	int data_num = sig_num;
	double signal_aver = 0.0;
	double reference_aver = 0.0;

	double factor_x1_x2 = 0.0;
	double factor_y1_y2 = 0.0;
	double factor_x1_x1 = 0.0;
	double factor_y1_y1 = 0.0;
	double factor_x2_x2 = 0.0;
	double factor_y2_y2 = 0.0;

	double factor1 = 0.0;
	double factor2 = 0.0;
	double factor3 = 0.0;
	double value = 0;

	for (int i = 0; i < data_num; i++) {
		factor_x1_x2 = factor_x1_x2 + data1x[i] * data2x[i];
		factor_y1_y2 = factor_y1_y2 + data1y[i] * data2y[i];

		factor_x1_x1 = factor_x1_x1 + data1x[i] * data1x[i];
		factor_y1_y1 = factor_y1_y1 + data1y[i] * data1y[i];

		factor_x2_x2 = factor_x2_x2 + data2x[i] * data2x[i];
		factor_y2_y2 = factor_y2_y2 + data2y[i] * data2y[i];
	}

	factor2 = sqrt(factor_x1_x1 + factor_y1_y1);
	factor3 = sqrt(factor_x2_x2 + factor_y2_y2);
	factor1 = factor2 * factor3;
	//value = (factor_x1_x2+factor_y1_y2) / factor2 / factor3;
	value = factor1==0 ? 0 : (factor_x1_x2 + factor_y1_y2) / factor1;

	return value;//返回指示值
}



double
match_value_hqt_f(double data1x[], double data1y[], double data2x[], double data2y[], int sig_num)//
{
	int data_num = sig_num;
	double signal_aver = 0.0;
	double reference_aver = 0.0;

	double factor_x1_x2 = 0.0;
	double factor_y1_y2 = 0.0;
	double factor_x1_x1 = 0.0;
	double factor_y1_y1 = 0.0;
	double factor_x2_x2 = 0.0;
	double factor_y2_y2 = 0.0;

	double factor1 = 0.0;
	double factor2 = 0.0;
	double factor3 = 0.0;
	double factor4 = 0.0;
	double value = 0;

	for (int i = 0; i < data_num; i++) {
		factor_x1_x2 = factor_x1_x2 + data1x[i] * data2x[i];
		factor_y1_y2 = factor_y1_y2 + data1y[i] * data2y[i];

		factor_x1_x1 = factor_x1_x1 + data1x[i] * data1x[i];
		factor_y1_y1 = factor_y1_y1 + data1y[i] * data1y[i];

		factor_x2_x2 = factor_x2_x2 + data2x[i] * data2x[i];
		factor_y2_y2 = factor_y2_y2 + data2y[i] * data2y[i];
	}

	factor1 = factor_x1_x2 * factor_x1_x2 + factor_y1_y2 * factor_y1_y2;
	//factor1 = (factor_x1_x2  + factor_y1_y2)*(factor_x1_x2  + factor_y1_y2);
	factor2 = factor_x1_x1 + factor_y1_y1;
	factor3 = factor_x2_x2 + factor_y2_y2;
	factor4 = factor2 * factor3;

	value = factor4==0 ? 0 :factor1 / factor4;

	return value;//返回指示值
}

double
match_value_hqt_f2(double data1x[], double data1y[], double data2x[], double data2y[], int sig_num)//
{
    int data_num = sig_num;
    double signal_aver = 0.0;
    double reference_aver = 0.0;

    double factor_y1_y2 = 0.0;
    double factor_y1_ = 0.0;
    double factor_y1_y1 = 0.0;
    double factor_y2_y2 = 0.0;
    double factor_y2_ = 0.0;

    double factor1 = 0.0;
    double factor2 = 0.0;
    double factor3 = 0.0;
    double value = 0;

    signal_aver=aver(data1y,data_num);
    reference_aver=aver(data2y,data_num);

    for (int i = 0; i < data_num; i++) {
        factor_y1_=data1y[i]-signal_aver;
        factor_y2_=data2y[i]-reference_aver;
        factor_y1_y2 = factor_y1_y2 + factor_y1_ * factor_y2_;
        factor_y1_y1 = factor_y1_y1 + factor_y1_ * factor_y1_;
        factor_y2_y2 = factor_y2_y2 + factor_y2_ * factor_y2_;
    }

    factor2 = (factor_y1_y1==0) ? 1 :factor_y1_y1;
    factor3 = (factor_y2_y2==0) ? 1 :factor_y2_y2;
    factor1 = factor_y1_y2*factor_y1_y2;
    value = factor1/ factor2/factor3;

    return value;//返回指示值
}

double match_value_cosd_l(double data1x[], double data1y[], double data2x[], double data2y[],
                        int sig_num)//
{
    int data_num = sig_num;
    double signal_aver = 0.0;
    double reference_aver = 0.0;

    double factor_y1_y2 = 0.0;
    double factor_y1_ = 0.0;
    double factor_y1_y1 = 0.0;
    double factor_y2_y2 = 0.0;
    double factor_y2_ = 0.0;

    double factor1 = 0.0;
    double factor2 = 0.0;
    double factor3 = 0.0;
    double value = 0;

    signal_aver=aver(data1y,data_num);
    reference_aver=aver(data2y,data_num);

    for (int i = 0; i < data_num; i++) {
        factor_y1_=data1y[i]-signal_aver;
        factor_y2_=data2y[i]-reference_aver;
        factor_y1_y2 = factor_y1_y2 + factor_y1_ * factor_y2_;
        factor_y1_y1 = factor_y1_y1 + factor_y1_ * factor_y1_;
        factor_y2_y2 = factor_y2_y2 + factor_y2_ * factor_y2_;
    }

    factor2 = (factor_y1_y1==0) ? 1 :sqrt(factor_y1_y1);
    factor3 = (factor_y2_y2==0) ? 1 :sqrt(factor_y2_y2);
    factor1 = factor_y1_y2;
    value = factor1/ factor2/factor3;

    return value;//返回指示值
}

double
match_cosd(double wavenum1[], double signal1[], int length1, double wavenum2[], double signal2[],
		   int length2) {
	double spec_correlation = 0.0;
	double *c_signal1 = (double *) malloc(sizeof(double) * length1);
	double *c_signal2 = (double *) malloc(sizeof(double) * length2);
	memcpy(c_signal1, signal1, length1 * sizeof(double));
	memcpy(c_signal2, signal2, length2 * sizeof(double));
	normalize_curve_f(c_signal1, length1);
	normalize_curve_f(c_signal2, length2);
	spec_correlation = match_value_cosd_f(wavenum1, c_signal1, wavenum2, c_signal2, length1);
	free(c_signal1);
	free(c_signal2);
	return spec_correlation;
}

double
match_hqi(double wavenum1[], double signal1[], int length1, double wavenum2[], double signal2[],
		  int length2) {
	double spec_correlation = 0.0;
	double *c_signal1 = (double *) malloc(sizeof(double) * length1);
	double *c_signal2 = (double *) malloc(sizeof(double) * length2);
	memcpy(c_signal1, signal1, length1 * sizeof(double));
	memcpy(c_signal2, signal2, length2 * sizeof(double));
	normalize_curve_l(c_signal1, length1);
	normalize_curve_l(c_signal2, length2);
	spec_correlation = match_value_hqt_f2(wavenum1, c_signal1, wavenum2, c_signal2, length1);
	free(c_signal1);
	free(c_signal2);
	return spec_correlation;
}

double
match_cosd_w(double wavenum1[], double signal1[], int length1, double wavenum2[], double signal2[],
		   int length2) {
	double spec_correlation = 0.0;
	double *c_signal1 = (double *) malloc(sizeof(double) * length1);
	double *c_signal2 = (double *) malloc(sizeof(double) * length2);
	memcpy(c_signal1, signal1, length1 * sizeof(double));
	memcpy(c_signal2, signal2, length2 * sizeof(double));
	normalize_curve_l(c_signal1, length1);
	normalize_curve_l(c_signal2, length2);
	spec_correlation = match_value_cosd_l(wavenum1, c_signal1, wavenum2, c_signal2, length1);
	free(c_signal1);
	free(c_signal2);
	return spec_correlation;
}

int convertdata_org(unsigned short* spBuff, double* pIntensity, unsigned int len, double xs)
{
    unsigned short* spec16 = spBuff;
    if (decode(spec16, len) == 0)
        return 0;
    excode(spec16, len);
    if (xs == 1)
    {
        for (int i = 0; i < len; i++)
        {
            pIntensity[i] = spec16[i];
        }
    }
    else
    {
        for (int i = 0; i < len; i++)
        {
            pIntensity[i] = spec16[i];
            pIntensity[i] = (double)(pIntensity[i]) * xs;
        }
    }
    encodeSoft(pIntensity, len);
    return 1;
}




