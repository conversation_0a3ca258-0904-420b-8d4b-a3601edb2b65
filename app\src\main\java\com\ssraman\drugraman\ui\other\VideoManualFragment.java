package com.ssraman.drugraman.ui.other;

import android.content.pm.ActivityInfo;
import android.graphics.Point;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.MediaController;
import android.widget.RelativeLayout;
import android.widget.VideoView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.base.ExBaseFragment;
import com.ssraman.drugraman.databinding.FragmentVideoManualBinding;
import com.ssraman.drugraman.ui.vm.VideoManualViewModel;
import com.ssraman.drugraman.ui.MainActivity;
import com.ssraman.lib_common.utils.DensityUtils;
import com.ssraman.lib_common.utils.SDCardScanner;
import com.ssraman.lib_common.utils.ToastUtils;

import java.io.File;

import static android.content.Context.WINDOW_SERVICE;

public class VideoManualFragment extends ExBaseFragment<FragmentVideoManualBinding, VideoManualViewModel> {

    private VideoView video;
    public static final int PORT_SCREEN = 0;
    public static final int LAND_SCREEN = 1;
    public int screenState;

    private static final String TAG = "UIHelpViewPlayer";

    private GestureDetector mGesDetector;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_video_manual;
    }

    @Override
    public int initVariableId() {
        return 0;
    }

    @Override
    public void initData() {
        super.initData();

        video=binding.videoView;
        //设置视频控制器
        video.setMediaController(new MediaController(mActivity));
        //播放完成回调
        video.setOnCompletionListener( new MyPlayerOnCompletionListener());

        mGesDetector = new GestureDetector(mActivity,new MyGestureListener());
        mGesDetector.setOnDoubleTapListener(new MyGestureListener());

        initVediopath();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ((MainActivity) this.getActivity()).registerFragmentTouchListener(fragmentTouchListener);
    }

    MainActivity.FragmentTouchListener fragmentTouchListener = new MainActivity.FragmentTouchListener() {
        @Override
        public boolean onTouchEvent(MotionEvent event) {
            return  mGesDetector.onTouchEvent(event);
        }
    };

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (null != video) {
            video.suspend();
        }
        ((MainActivity) this.getActivity()).unRegisterFragmentTouchListener(fragmentTouchListener);

    }

    private void initVediopath() {
        String PathOfApp = SDCardScanner.getExternalStorageDirectory(mActivity);
        File file = new File(PathOfApp, "/Manual/"+"原辅料快检仪操作视频.mp4");
        if (file.exists()) {
            logcat("setPath");
            //设置视频路径
            //video.setVideoURI(uri);
            video.setVideoPath(file.getPath());
            //开始播放视频
            video.start();
        } else {
            ToastUtils.showShort("操作视频文件不存在");
        }
    }

    public String getSDPath(){
        //File sdDir = null;
        String sdDir = null;
        //long long_InternalSdcardTotalMemory=Environment.getExternalStorageDirectory().getTotalSpace();
        long long_InternalSdcardTotalMemory=mActivity.getExternalFilesDir(null).getTotalSpace();
        logcat("SD:"+long_InternalSdcardTotalMemory);
        boolean sdCardExist = Environment.getExternalStorageState()
                .equals(Environment.MEDIA_MOUNTED);//判断sd卡是否存在
        if(sdCardExist)
        {
           // sdDir = Environment.getExternalStorageDirectory();//获取跟目录
           // sdDir = mActivity.getExternalFilesDir(null);//获取跟目录
            sdDir = SDCardScanner.getExternalStorageDirectory(mActivity);//获取跟目录
        }
        //return sdDir.toString();
        return sdDir;
    }

    class MyPlayerOnCompletionListener implements MediaPlayer.OnCompletionListener {

        @Override
        public void onCompletion(MediaPlayer mp) {
           // Toast.makeText( VideoManualFragment.this, "播放完成了", Toast.LENGTH_SHORT).show();
        }
    }

    public void logcat(String log) {
        Log.i(TAG, log);
    }


    public class MyGestureListener extends GestureDetector.SimpleOnGestureListener  {

        public MyGestureListener() {
            super();
        }

        @Override
        public boolean onDoubleTap(MotionEvent e) {
//            if (screenState == LAND_SCREEN) {
//                convertToPortScreen();
//            } else if (screenState == PORT_SCREEN) {
//                convertToLandScreen();
//            }
            return true;
        }

    }

    private void convertToPortScreen(){
        mActivity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        mActivity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);//设置videoView竖屏播放
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, DensityUtils.dip2px(mActivity, 198f));
        params.setMargins(DensityUtils.dip2px(mActivity,12), DensityUtils.dip2px(mActivity,15),
                DensityUtils.dip2px(mActivity,12), DensityUtils.dip2px(mActivity,14));
        //params.addRule(RelativeLayout.CENTER_IN_PARENT);
        video.setLayoutParams(params);
        screenState = PORT_SCREEN;
    }
    private void convertToLandScreen(){
        mActivity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);//设置videoView全屏播放
        mActivity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);//设置videoView横屏播放
        WindowManager wm = (WindowManager) mActivity.getSystemService(WINDOW_SERVICE);
        android.view.Display display = wm.getDefaultDisplay();
        Point point = new Point();
        int API_LEVEL = android.os.Build.VERSION.SDK_INT;
        if(API_LEVEL >= 17){
            display.getRealSize(point);
        }else{
            display.getSize(point);
        }
        int height = point.y;
        int width = point.x;
        Log.i(TAG,"screenHeight = "+height+" ; screenWidth = "+width);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) video
                .getLayoutParams(); // 取控当前的布局参数
        layoutParams.height = height;
        layoutParams.width = width;
        layoutParams.setMargins(0,0,0,0);
        video.setLayoutParams(layoutParams);

        screenState = LAND_SCREEN;
    }


}