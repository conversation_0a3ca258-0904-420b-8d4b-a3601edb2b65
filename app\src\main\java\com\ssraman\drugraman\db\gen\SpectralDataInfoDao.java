package com.ssraman.drugraman.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.ssraman.drugraman.db.entity.SpectralDataInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "SpectralDataTable".
*/
public class SpectralDataInfoDao extends AbstractDao<SpectralDataInfo, Long> {

    public static final String TABLENAME = "SpectralDataTable";

    /**
     * Properties of entity SpectralDataInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "Id", true, "Id");
        public final static Property SpecName = new Property(1, String.class, "SpecName", false, "SpecName");
        public final static Property ObWave = new Property(2, byte[].class, "ObWave", false, "Wave");
        public final static Property ObIntensity = new Property(3, byte[].class, "ObIntensity", false, "Intensity");
        public final static Property PeakData = new Property(4, byte[].class, "PeakData", false, "PeakData");
        public final static Property SendWave = new Property(5, Double.class, "SendWave", false, "SendWave");
        public final static Property StartWave = new Property(6, Integer.class, "StartWave", false, "StartWave");
        public final static Property IntegratioTime = new Property(7, Integer.class, "IntegratioTime", false, "IntegratioTime");
        public final static Property LaserPower = new Property(8, Double.class, "LaserPower", false, "LaserPower");
        public final static Property AverageCount = new Property(9, Integer.class, "AverageCount", false, "AverageCount");
        public final static Property Raw_x = new Property(10, byte[].class, "Raw_x", false, "Raw_x");
        public final static Property Raw_y = new Property(11, byte[].class, "Raw_y", false, "Raw_y");
        public final static Property Mac_coefficient = new Property(12, String.class, "mac_coefficient", false, "mac_coefficient");
    }


    public SpectralDataInfoDao(DaoConfig config) {
        super(config);
    }
    
    public SpectralDataInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, SpectralDataInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        String SpecName = entity.getSpecName();
        if (SpecName != null) {
            stmt.bindString(2, SpecName);
        }
 
        byte[] ObWave = entity.getObWave();
        if (ObWave != null) {
            stmt.bindBlob(3, ObWave);
        }
 
        byte[] ObIntensity = entity.getObIntensity();
        if (ObIntensity != null) {
            stmt.bindBlob(4, ObIntensity);
        }
 
        byte[] PeakData = entity.getPeakData();
        if (PeakData != null) {
            stmt.bindBlob(5, PeakData);
        }
 
        Double SendWave = entity.getSendWave();
        if (SendWave != null) {
            stmt.bindDouble(6, SendWave);
        }
 
        Integer StartWave = entity.getStartWave();
        if (StartWave != null) {
            stmt.bindLong(7, StartWave);
        }
 
        Integer IntegratioTime = entity.getIntegratioTime();
        if (IntegratioTime != null) {
            stmt.bindLong(8, IntegratioTime);
        }
 
        Double LaserPower = entity.getLaserPower();
        if (LaserPower != null) {
            stmt.bindDouble(9, LaserPower);
        }
 
        Integer AverageCount = entity.getAverageCount();
        if (AverageCount != null) {
            stmt.bindLong(10, AverageCount);
        }
 
        byte[] Raw_x = entity.getRaw_x();
        if (Raw_x != null) {
            stmt.bindBlob(11, Raw_x);
        }
 
        byte[] Raw_y = entity.getRaw_y();
        if (Raw_y != null) {
            stmt.bindBlob(12, Raw_y);
        }
 
        String mac_coefficient = entity.getMac_coefficient();
        if (mac_coefficient != null) {
            stmt.bindString(13, mac_coefficient);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, SpectralDataInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        String SpecName = entity.getSpecName();
        if (SpecName != null) {
            stmt.bindString(2, SpecName);
        }
 
        byte[] ObWave = entity.getObWave();
        if (ObWave != null) {
            stmt.bindBlob(3, ObWave);
        }
 
        byte[] ObIntensity = entity.getObIntensity();
        if (ObIntensity != null) {
            stmt.bindBlob(4, ObIntensity);
        }
 
        byte[] PeakData = entity.getPeakData();
        if (PeakData != null) {
            stmt.bindBlob(5, PeakData);
        }
 
        Double SendWave = entity.getSendWave();
        if (SendWave != null) {
            stmt.bindDouble(6, SendWave);
        }
 
        Integer StartWave = entity.getStartWave();
        if (StartWave != null) {
            stmt.bindLong(7, StartWave);
        }
 
        Integer IntegratioTime = entity.getIntegratioTime();
        if (IntegratioTime != null) {
            stmt.bindLong(8, IntegratioTime);
        }
 
        Double LaserPower = entity.getLaserPower();
        if (LaserPower != null) {
            stmt.bindDouble(9, LaserPower);
        }
 
        Integer AverageCount = entity.getAverageCount();
        if (AverageCount != null) {
            stmt.bindLong(10, AverageCount);
        }
 
        byte[] Raw_x = entity.getRaw_x();
        if (Raw_x != null) {
            stmt.bindBlob(11, Raw_x);
        }
 
        byte[] Raw_y = entity.getRaw_y();
        if (Raw_y != null) {
            stmt.bindBlob(12, Raw_y);
        }
 
        String mac_coefficient = entity.getMac_coefficient();
        if (mac_coefficient != null) {
            stmt.bindString(13, mac_coefficient);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public SpectralDataInfo readEntity(Cursor cursor, int offset) {
        SpectralDataInfo entity = new SpectralDataInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // Id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // SpecName
            cursor.isNull(offset + 2) ? null : cursor.getBlob(offset + 2), // ObWave
            cursor.isNull(offset + 3) ? null : cursor.getBlob(offset + 3), // ObIntensity
            cursor.isNull(offset + 4) ? null : cursor.getBlob(offset + 4), // PeakData
            cursor.isNull(offset + 5) ? null : cursor.getDouble(offset + 5), // SendWave
            cursor.isNull(offset + 6) ? null : cursor.getInt(offset + 6), // StartWave
            cursor.isNull(offset + 7) ? null : cursor.getInt(offset + 7), // IntegratioTime
            cursor.isNull(offset + 8) ? null : cursor.getDouble(offset + 8), // LaserPower
            cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9), // AverageCount
            cursor.isNull(offset + 10) ? null : cursor.getBlob(offset + 10), // Raw_x
            cursor.isNull(offset + 11) ? null : cursor.getBlob(offset + 11), // Raw_y
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12) // mac_coefficient
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, SpectralDataInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setSpecName(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setObWave(cursor.isNull(offset + 2) ? null : cursor.getBlob(offset + 2));
        entity.setObIntensity(cursor.isNull(offset + 3) ? null : cursor.getBlob(offset + 3));
        entity.setPeakData(cursor.isNull(offset + 4) ? null : cursor.getBlob(offset + 4));
        entity.setSendWave(cursor.isNull(offset + 5) ? null : cursor.getDouble(offset + 5));
        entity.setStartWave(cursor.isNull(offset + 6) ? null : cursor.getInt(offset + 6));
        entity.setIntegratioTime(cursor.isNull(offset + 7) ? null : cursor.getInt(offset + 7));
        entity.setLaserPower(cursor.isNull(offset + 8) ? null : cursor.getDouble(offset + 8));
        entity.setAverageCount(cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9));
        entity.setRaw_x(cursor.isNull(offset + 10) ? null : cursor.getBlob(offset + 10));
        entity.setRaw_y(cursor.isNull(offset + 11) ? null : cursor.getBlob(offset + 11));
        entity.setMac_coefficient(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(SpectralDataInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(SpectralDataInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(SpectralDataInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
