package com.ssraman.drugraman.data;

import com.ssraman.drugraman.db.entity.User_info;

import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Observable;

public interface LoginDataManager {
    public Observable<User_info> GetUserPassInfo(String user, String password);

    public Observable<List<User_info>> GetAllUser();

    public Completable DeleteUser(User_info userInfo);

    public boolean existsUser(String user_name);

    public Completable AddUser(User_info add_data);

    public Completable UpdateUser(User_info update_data);

    public Observable<User_info> GetAuthenticationPassInfo(String user, String password);

}
