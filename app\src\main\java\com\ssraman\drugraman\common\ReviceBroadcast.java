package com.ssraman.drugraman.common;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.widget.Toast;

/**
 * Created by <PERSON><PERSON> on 2020/5/25.
 */

public class ReviceBroadcast extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        String motor= intent.getExtras().getString("motor");
        if (motor.equals("motorReSet")){
//            Toast.makeText(context, "马达复位成功",
//                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("motorBeforOrBeforOneHundred")){
//            Toast.makeText(context, "马达运行到最前面成功",
//                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("motorAfterOrAfterOneHundred")){
//            Toast.makeText(context, "马达运行到最后面成功",
//                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("motorCentre")){
//            Toast.makeText(context, "马达运行到中间成功",
//                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("systemError")){
            Toast.makeText(context, "马达回复：系统错误！",
                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("checkError")){
            Toast.makeText(context, "马达回复：校验错误！",
                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("PackageTypeDoesNotExistError")){
            Toast.makeText(context, "马达回复：不存在的包类型！",
                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("NonexistentOpcodeError")){
            Toast.makeText(context, "马达回复：不存在的操作码！",
                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("ParameterError")){
            Toast.makeText(context, "马达回复：参数错误！",
                    Toast.LENGTH_LONG).show();
        }else if (motor.equals("OperationError")){
            Toast.makeText(context, "马达回复：操作失败！",
                    Toast.LENGTH_LONG).show();
        }else {
            Toast.makeText(context, "其他未知因素s",
                    Toast.LENGTH_LONG).show();
        }
    }
}
