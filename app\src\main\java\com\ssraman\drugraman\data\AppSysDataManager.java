package com.ssraman.drugraman.data;

import android.content.Context;

import com.ssraman.drugraman.db.SystemDatabase;
import com.ssraman.drugraman.db.entity.CalibrationFtInfo;
import com.ssraman.drugraman.db.entity.FtInfo;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.db.entity.SampleTypeInfo;
import com.ssraman.drugraman.db.entity.SelectPeakInfo;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.repository.SysCalibrationFtRepository;
import com.ssraman.drugraman.db.repository.SysFtRepository;
import com.ssraman.drugraman.db.repository.SysPeakRepository;
import com.ssraman.drugraman.db.repository.SysSampleTypeRepository;
import com.ssraman.drugraman.newentiry.FtNodeInfo;
import com.ssraman.drugraman.newentiry.MatchFtInfo;
import com.ssraman.drugraman.newentiry.MatchNodeInfo;
import com.ssraman.drugraman.newentiry.SampleNode;
import com.ssraman.drugraman.newentiry.SampleNodeInfo;
import com.ssraman.drugraman.typeface.NodeItemType;
import com.ssraman.lib_common.constant.DbConst;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;

public class AppSysDataManager implements SysDataManager {
    private String TAG = "AppSysDataManager";
    private static AppSysDataManager appSysDataManager = null;
    private Context context = null;
    private SystemDatabase systemDatabase;
    private DaoSession mDaoSession;
    private SysSampleTypeRepository sampleTypeRepository;
    private SysFtRepository ftRepository;
    private SysPeakRepository peakRepository;

    private AppSysDataManager(Context context) {
        this.context = context;
        systemDatabase = SystemDatabase.getInstance(this.context, DbConst.SYS_DATABASE_NAME);

        mDaoSession = systemDatabase.getSession();
        sampleTypeRepository = new SysSampleTypeRepository(mDaoSession);
        ftRepository = new SysFtRepository(mDaoSession);
        peakRepository = new SysPeakRepository(mDaoSession);

    }

    public static AppSysDataManager getInstance(Context context) {
        synchronized (AppSysDataManager.class) {
            if (appSysDataManager == null) {
                appSysDataManager = new AppSysDataManager(context);
            }
        }
        return appSysDataManager;
    }

    public Observable<List<SampleTypeInfo>> getMenuList(int ParentId, int type) {
        return sampleTypeRepository.getDirectSubSampleType(ParentId).toObservable();
    }

    public List<MatchNodeInfo> getMatchListByClassify(Long classifyId) {
        List<MatchNodeInfo> matchNodeList = new ArrayList<>();
        MatchNodeInfo matchSample = new MatchNodeInfo();
        SampleTypeInfo sampleTypeInfo = sampleTypeRepository.getSampleTypeById(classifyId);
        matchSample.setMatchSample(sampleTypeInfo);

        List<FtInfo> list;
        // 获取所有
        list = ftRepository.getFtListBySampleTypeId(classifyId);
        List<MatchFtInfo> matchSampleList = new ArrayList<>();
        for (FtInfo ftInfo : list) {
            MatchFtInfo matchFt = new MatchFtInfo();
            matchFt.setFtInfo(ftInfo);
            List<PeakInfo> peakList = peakRepository.getPeakListByFtId(ftInfo.getId());
            matchFt.setPeakList(peakList);
            matchSampleList.add(matchFt);
        }
        matchSample.setMatchSampleFt(matchSampleList);
        matchNodeList.add(matchSample);
        return matchNodeList;
    }

    // 单向
    public List<MatchNodeInfo> getMatchedListByWave(double[] wave, Long classifyId) {
        List<MatchNodeInfo> matchNodeList = new ArrayList<>();
        if (wave.length <= 0) {
            return matchNodeList;
        }

        // 检查传入的classifyId是否对应Type=3的末级节点
        SampleTypeInfo classifyInfo = null;
        if (classifyId != null && classifyId > 0) {
            classifyInfo = sampleTypeRepository.getSampleTypeById(classifyId);
        }

        List<SelectPeakInfo> list;
        // 如果是Type=3的末级节点，直接根据该节点的ID查询相关FtInfo
        if (classifyInfo != null && classifyInfo.getType() != null
                && classifyInfo.getType() == NodeItemType.ItemLevelDirectory.value()) {
            // 是末级节点，不应将classifyId作为ParentId使用，而是直接匹配该样品
            // 这里调整为直接匹配样品ID
            android.util.Log.d("AppSysDataManager",
                    "末级节点检测: ID=" + classifyId + ", 名称=" + classifyInfo.getName() + ", 使用直接匹配样品ID");
            list = ftRepository.getSelectPeakListByItemId(wave, classifyId);
        } else {
            // 不是末级节点或为null，按原来逻辑处理，将classifyId作为ParentId使用
            if (classifyInfo != null) {
                android.util.Log.d("AppSysDataManager",
                        "非末级节点检测: ID=" + classifyId + ", 名称=" + classifyInfo.getName() + ", 类型="
                                + (classifyInfo.getType() != null ? classifyInfo.getType() : "null")
                                + ", 使用ParentId匹配");
            } else {
                android.util.Log.d("AppSysDataManager", "无节点信息或ID为0: ID=" + classifyId + ", 使用ParentId匹配");
            }
            list = ftRepository.getSelectPeakListByWave(wave, classifyId);
        }

        android.util.Log.d("AppSysDataManager", "匹配结果数量: " + list.size());

        long sumCount = 0;

        long oldSampleId = 0;
        MatchNodeInfo matchSample = new MatchNodeInfo();
        List<MatchFtInfo> matchSampleList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            SelectPeakInfo selectInfo = list.get(i);
            long sampleId = selectInfo.getSampleTypeId();
            if (sampleId != oldSampleId) {
                matchSample = new MatchNodeInfo();
                SampleTypeInfo sampleTypeInfo = sampleTypeRepository.getSampleTypeById(sampleId);
                matchSample.setMatchSample(sampleTypeInfo);
                matchSampleList = new ArrayList<>();
                sumCount += 1;
            }
            List<FtInfo> Ftlist = ftRepository.getFtListById(Long.valueOf(selectInfo.getFtId()));
            for (FtInfo ftInfo : Ftlist) {
                MatchFtInfo matchFt = new MatchFtInfo();
                matchFt.setFtInfo(ftInfo);
                List<PeakInfo> peakList = peakRepository.getPeakListByFtId(ftInfo.getId());
                matchFt.setPeakList(peakList);
                matchSampleList.add(matchFt);
            }
            if (sampleId != oldSampleId) {
                matchSample.setMatchSampleFt(matchSampleList);
                matchNodeList.add(matchSample);
            }
            oldSampleId = sampleId;
        }
        return matchNodeList;
    }

    public Observable<List<SampleTypeInfo>> getAllList() {
        return sampleTypeRepository.getAllSampleType().toObservable();
    }

    // 返回样品节点所有谱图数据
    public List<SampleNode> getNodeGroupFt(long id) {
        List<SampleNode> list = new ArrayList();
        List<PeakInfo> peakList;
        SampleTypeInfo tempSample = sampleTypeRepository.getNodeSampleTypeInfo(id);
        if (tempSample == null) {
            return null;
        }

        long selectFtId = tempSample.getId();
        List<FtInfo> ftList = ftRepository.getFtListBySampleTypeId_lib(selectFtId);
        if (ftList.size() > 0) {

            for (FtInfo tempFtInfo : ftList) {
                if (tempFtInfo != null) {
                    peakList = peakRepository.getPeakListByFtId(tempFtInfo.getId());
                    SampleNode sampleNode = new SampleNode();
                    sampleNode.setSampleTypeInfo(tempSample);
                    sampleNode.setTitle(tempSample.getName());
                    MatchFtInfo spcInfo = new MatchFtInfo();
                    spcInfo.setFtInfo(tempFtInfo);
                    spcInfo.setPeakList(peakList);
                    sampleNode.setSpcInfo(spcInfo);
                    list.add(sampleNode);

                }
            }
        }

        return list;
    }

    public SampleTypeInfo getNodeSampleTypeInfo(long id) {
        return sampleTypeRepository.getNodeSampleTypeInfo(id);
    }

    public List<SampleTypeInfo> getSampleTypeByParentId(long id) {
        return sampleTypeRepository.getSampleTypeByParentId(id);
    }

    public SampleTypeInfo getSampleTypeById(long id) {
        return sampleTypeRepository.getSampleTypeById(id);
    }

    public long insetSampleType(SampleTypeInfo info) {
        return sampleTypeRepository.insertSampleTypeByRe(info);
    }

    public void updateSampleType(SampleTypeInfo info) {
        sampleTypeRepository.updateSampleTypeByRe(info);
    }

    public long saveFtDataToLib(SampleNode info) {
        FtInfo ftInfo = info.getSpcInfo().getFtInfo();
        long ftId = ftRepository.insertFtByRe(ftInfo);
        List<PeakInfo> peakList = info.getSpcInfo().getPeakList();
        for (int i = 0; i < peakList.size(); i++) {
            peakList.get(i).setFtId((int) ftId);
        }
        peakRepository.insertPeakList(peakList);
        return ftId;
    }

    public Observable<Boolean> deleteFtData(SampleNode deleteData) {
        return Observable.create(new ObservableOnSubscribe<SampleNode>() {
            @Override
            public void subscribe(ObservableEmitter<SampleNode> emitter) throws Exception {
                emitter.onNext(deleteData);
                emitter.onComplete();
            }
        })
                .map(sampleNode -> {
                    ftRepository.deleteFt(sampleNode.getSpcInfo().getFtInfo());
                    peakRepository.deletePeaks(sampleNode.getSpcInfo().getPeakList());
                    return true;
                });
    }

    public Observable<Boolean> deletePeakData(List<PeakInfo> deleteData) {
        return Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) throws Exception {
                peakRepository.deletePeaks(deleteData);
                emitter.onNext(true);
                emitter.onComplete();
            }
        });
    }

    public Observable<Boolean> updateFtData(SampleNode updateData) {
        return Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) throws Exception {
                ftRepository.updateFtByRe(updateData.getSpcInfo().getFtInfo());
                peakRepository.updatePeaks(updateData.getSpcInfo().getPeakList());
                emitter.onNext(true);
                emitter.onComplete();
            }
        });
    }

    public boolean updateFtData(FtInfo update_ft, List<PeakInfo> update_peak_list, List<PeakInfo> un_seleted_list) {
        ftRepository.updateFtByRe(update_ft);
        peakRepository.updatePeaks(update_peak_list);
        peakRepository.deletePeaks(un_seleted_list);
        return true;
    }

    public SampleNodeInfo getSampleNodeData(SampleNodeInfo nodeInfo) {
        List<FtInfo> list;
        // 获取所有
        list = ftRepository.getFtListBySampleTypeId(nodeInfo.getSampleTypeInfo().getId());
        List<FtNodeInfo> ftNodeList = new ArrayList<>();
        for (FtInfo ftInfo : list) {
            FtNodeInfo ftNodeInfo = new FtNodeInfo();
            ftNodeInfo.setFtInfo(ftInfo);
            List<PeakInfo> peakList = peakRepository.getPeakListByFtId(ftInfo.getId());
            ftNodeInfo.setPeaklist(peakList);
            ftNodeList.add(ftNodeInfo);
        }
        nodeInfo.setFtNodeList(ftNodeList);
        return nodeInfo;
    }

    public Observable<List<SampleTypeInfo>> getAllSampleItem(int directoryType) {
        return sampleTypeRepository.getAllSampleType(directoryType).toObservable();
    }

    public Observable<FtNodeInfo> getSampleDataBySampleId(int sampleId) {
        return Observable.create(new ObservableOnSubscribe<FtNodeInfo>() {
            @Override
            public void subscribe(ObservableEmitter<FtNodeInfo> emitter) throws Exception {
                FtNodeInfo sample_data = new FtNodeInfo();
                List<FtInfo> ft_list = ftRepository.getFtListBySampleTypeId((long) sampleId);
                if (ft_list.size() > 0) {
                    sample_data.setFtInfo(ft_list.get(0));
                    List<PeakInfo> peakList = peakRepository.getPeakListByFtId(ft_list.get(0).getId());
                    sample_data.setPeaklist(peakList);
                }
                emitter.onNext(sample_data);
                emitter.onComplete();
            }
        });
    }

    public Observable<List<SampleTypeInfo>> getSampleTypeByEvaluationMode(int evaluation_mode) {
        if (evaluation_mode == 0)// 验证 0
        {
            return sampleTypeRepository.getAllSampleType().toObservable();
        } else // 辨识 1
        {
            return sampleTypeRepository.getAllSampleType((int) NodeItemType.LowLevelDirectory.value()).toObservable();
        }
    }

    public FtInfo getFtInfoById(long ft_id) {
        return ftRepository.getFtInfoById(ft_id);
    }

    public SampleTypeInfo existsSampleItem(long parent_id, String sample_name) {
        return sampleTypeRepository.existsSampleItem(parent_id, sample_name);
    }

    public long insertFtInfo(FtInfo add_ftInfo) {
        long ftId = ftRepository.insertFtByRe(add_ftInfo);
        return ftId;
    }

    public boolean insertPeakList(List<PeakInfo> peakList) {
        peakRepository.insertPeakList(peakList);
        return true;
    }

    @Override
    public List<FtInfo> getFtListBySampleTypeId(Long id) {
        return ftRepository.getFtListBySampleTypeId(id);
    }

    @Override
    public boolean deleteFtInfo(long ft_id) {
        try {
            android.util.Log.d("AppSysDataManager", "开始删除谱图，ID: " + ft_id);

            // 打印删除前的数据
            FtInfo ftInfo = ftRepository.getFtInfoById(ft_id);
            List<PeakInfo> peakList = peakRepository.getPeakListByFtId(ft_id);
            android.util.Log.d("AppSysDataManager",
                    "删除前 - 谱图数据: " + (ftInfo != null ? ftInfo.getSampleName() : "null"));
            android.util.Log.d("AppSysDataManager", "删除前 - 峰值数据数量: " + (peakList != null ? peakList.size() : 0));

            mDaoSession.runInTx(new Runnable() {
                @Override
                public void run() {
                    // 先删除相关的峰值数据
                    List<PeakInfo> peakList = peakRepository.getPeakListByFtId(ft_id);
                    if (peakList != null && peakList.size() > 0) {
                        android.util.Log.d("AppSysDataManager", "删除峰值数据，数量: " + peakList.size());
                        peakRepository.deletePeaks(peakList);
                    }

                    // 删除谱图数据
                    FtInfo ftInfo = ftRepository.getFtInfoById(ft_id);
                    if (ftInfo != null) {
                        android.util.Log.d("AppSysDataManager", "删除谱图数据: " + ftInfo.getSampleName());
                        ftRepository.deleteFtByRe(ftInfo);
                    }
                }
            });

            // 打印删除后的数据
            ftInfo = ftRepository.getFtInfoById(ft_id);
            peakList = peakRepository.getPeakListByFtId(ft_id);
            android.util.Log.d("AppSysDataManager",
                    "删除后 - 谱图数据: " + (ftInfo != null ? ftInfo.getSampleName() : "null"));
            android.util.Log.d("AppSysDataManager", "删除后 - 峰值数据数量: " + (peakList != null ? peakList.size() : 0));

            return true;
        } catch (Exception e) {
            android.util.Log.e("AppSysDataManager", "删除失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除SampleTypeInfo分类项
     * 
     * @param sampleTypeId SampleTypeInfo的ID
     * @return 删除是否成功
     */
    public boolean deleteSampleType(long sampleTypeId) {
        try {
            android.util.Log.d("AppSysDataManager", "开始删除分类项，ID: " + sampleTypeId);

            // 获取要删除的分类项
            SampleTypeInfo sampleTypeInfo = sampleTypeRepository.getSampleTypeById(sampleTypeId);
            if (sampleTypeInfo == null) {
                android.util.Log.e("AppSysDataManager", "未找到要删除的分类项，ID: " + sampleTypeId);
                return false;
            }

            // 打印删除前的分类项信息
            android.util.Log.d("AppSysDataManager", "删除前 - 分类项: " + sampleTypeInfo.getName());

            // 删除分类项
            sampleTypeRepository.deleteSampleType(sampleTypeInfo).blockingAwait();

            // 验证删除结果
            sampleTypeInfo = sampleTypeRepository.getSampleTypeById(sampleTypeId);
            android.util.Log.d("AppSysDataManager",
                    "删除后 - 分类项: " + (sampleTypeInfo != null ? sampleTypeInfo.getName() : "null"));

            return sampleTypeInfo == null; // 如果为null，说明删除成功
        } catch (Exception e) {
            android.util.Log.e("AppSysDataManager", "删除分类项失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

}
