package com.ssraman.drugraman.business;

public class MyCustomException extends Exception {
    private int code;
    private String displayMessage;

    public MyCustomException() {
    }

    public MyCustomException(String displayMessage,int code) {
        this.code = code;
        this.displayMessage = displayMessage;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDisplayMessage() {
        return displayMessage;
    }

    public void setDisplayMessage(String displayMessage) {
        this.displayMessage = displayMessage;
    }
}
