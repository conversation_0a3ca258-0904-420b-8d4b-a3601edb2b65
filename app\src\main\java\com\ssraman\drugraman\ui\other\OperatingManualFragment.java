package com.ssraman.drugraman.ui.other;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;

import androidx.annotation.Nullable;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.jeremyliao.liveeventbus.LiveEventBus;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.base.ExBaseFragment;
import com.ssraman.drugraman.databinding.FragmentOperatingManualBinding;
import com.ssraman.drugraman.item.MenuTconItem;
import com.ssraman.drugraman.ui.vm.OperatingManualViewModel;
import com.ssraman.drugraman.typeface.ManualFuncType;
import com.ssraman.drugraman.ui.adapter.MenuIconAdapter;
import com.ssraman.lib_common.mac.ToolBarSwichBean;

import java.util.ArrayList;


public class OperatingManualFragment extends ExBaseFragment<FragmentOperatingManualBinding, OperatingManualViewModel>  implements AdapterView.OnItemClickListener {
    private ArrayList<MenuTconItem> mData = null;
    private BaseAdapter mAdapter = null;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_operating_manual;
    }

    @Override
    public int initVariableId() {
        return 0;
    }

    @Override
    public void initData() {
        super.initData();
        mData = new ArrayList<MenuTconItem>();
        mData.add(new MenuTconItem(R.drawable.ic_helpword_64, 0, "操作说明"));
        mData.add(new MenuTconItem(R.drawable.ic_helpvido_64, 1, "操作视频"));


        mAdapter = new MenuIconAdapter<MenuTconItem>(mData, R.layout.btn_item) {
            @Override
            public void bindView(ViewHolder holder, MenuTconItem obj) {
                holder.setImageResource(R.id.img_icon, obj.getResId());
                holder.setText(R.id.txt_icon, obj.getTitle());
            }
        };
        binding.setGridView.setAdapter(mAdapter);
        binding.setGridView.setOnItemClickListener(this);

        LiveEventBus.get("toolbar_swich").postAcrossProcess(new ToolBarSwichBean(1,"主菜单"));
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        NavController navController = Navigation.findNavController(view);
        ManualFuncType selectbtn = ManualFuncType.valueOf(position);
        Bundle args = null;
        switch (selectbtn) {
            case DocManual://1
                args = new Bundle();
                navController.navigate(R.id.docManualFragment, args);
                break;
            case VideoManual:
                args = new Bundle();
                navController.navigate(R.id.videoManualFragment, args);
                break;
            default:
                break;
        }
    }
}