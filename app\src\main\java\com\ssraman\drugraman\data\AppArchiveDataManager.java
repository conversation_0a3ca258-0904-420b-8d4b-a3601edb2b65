package com.ssraman.drugraman.data;

import android.content.Context;

import com.ssraman.drugraman.db.ArchiveDatabase;
import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;
import com.ssraman.drugraman.db.entity.CalibrationFtInfo;
import com.ssraman.drugraman.db.entity.OperLogInfo;
import com.ssraman.drugraman.db.entity.SpectralDataInfo;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.repository.ArchiveRecordRepository;
import com.ssraman.drugraman.db.repository.OperLogRepository;
import com.ssraman.drugraman.db.repository.SpectralDataRepository;
import com.ssraman.drugraman.db.repository.SysCalibrationFtRepository;
import com.ssraman.drugraman.newentiry.FilterInfo;
import com.ssraman.drugraman.newentiry.LogFilterInfo;
import com.ssraman.drugraman.newentiry.PageInfo;
import com.ssraman.lib_common.constant.DbConst;

import java.util.Date;
import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Observable;


/**
 * @author: Administrator
 * @date: 2021/10/20
 */
public class AppArchiveDataManager implements ArchiveDataManager {
    private String TAG = "AppLogDataManager";
    private static AppArchiveDataManager appArchiveDataManager = null;
    private Context context = null;
    private ArchiveDatabase archiveDatabase;
    private DaoSession mDaoSession;
    private OperLogRepository operLogRepository;
    private SysCalibrationFtRepository calibrationFtRepository;

    private ArchiveRecordRepository archiveRecordRepository;
    private SpectralDataRepository spectralDataRepository;

    private AppArchiveDataManager(Context context) {
        this.context = context;
        archiveDatabase = ArchiveDatabase.getInstance(this.context, DbConst.USER_DATABASE_NAME);
        //systemDatabase = SystemDatabase.getInstance(this.context, SettingPre.getOutDBName());
        mDaoSession = archiveDatabase.getSession();
        operLogRepository = new OperLogRepository(mDaoSession);

        archiveRecordRepository = new ArchiveRecordRepository(mDaoSession);
        spectralDataRepository = new SpectralDataRepository(mDaoSession);

        calibrationFtRepository=new SysCalibrationFtRepository(mDaoSession);
    }

    public static AppArchiveDataManager getInstance(Context context) {
        synchronized (AppArchiveDataManager.class) {
            if (appArchiveDataManager == null) {
                appArchiveDataManager = new AppArchiveDataManager(context);
            }
        }
        return appArchiveDataManager;
    }

    public Completable insertLog(OperLogInfo logInfo) {
        return operLogRepository.insertLog(logInfo);
    }

    public long insertLogByRe(OperLogInfo logInfo) {
        long re_id = operLogRepository.insertLogByRe(logInfo);
        return re_id;
    }

    public Completable deleteLog(OperLogInfo logInfo) {
        return operLogRepository.deleteLog(logInfo);
    }

    public Completable updateLog(OperLogInfo logInfo) {
        return operLogRepository.updateLog(logInfo);
    }


    public Observable<List<OperLogInfo>> getLogRecordListBySimple(PageInfo pageInfo, Date start, Date end) {
        return operLogRepository.getLogRecordBySimple(pageInfo, start, end).toObservable();
    }

    public Observable<List<OperLogInfo>> getLogRecordListByFilter(PageInfo pageInfo, LogFilterInfo filter) {
        return operLogRepository.getLogRecordByFilter(pageInfo, filter).toObservable();
    }

    /////////////////////////////////////////

    public Observable<List<ArchiveRecordsInfo>> getRecordListBySimple(PageInfo pageInfo, Date start, Date end) {
        return archiveRecordRepository.getRecordListBySimple(pageInfo, start, end).toObservable();
    }

    public Observable<List<ArchiveRecordsInfo>> getRecordListByFilter(PageInfo pageInfo, FilterInfo filter) {
        return archiveRecordRepository.getRecordListByFilter(pageInfo, filter).toObservable();
    }

    public Observable<SpectralDataInfo> getSpectralDataInfoById(long id) {
        return spectralDataRepository.getSpecDataById(id).toObservable();
    }

    public Observable<Boolean> updateRecordData(ArchiveRecordsInfo archiveRecordsInfo) {
        return archiveRecordRepository.updateRecords(archiveRecordsInfo).toObservable();
    }

    public SpectralDataInfo getSpectralDataById(long id) {
        return spectralDataRepository.getSpecDataByIdSimple(id);
    }

    public void updateMqttRecordData(ArchiveRecordsInfo archiveRecordsInfo) {
        archiveRecordRepository.updateRecordSimple(archiveRecordsInfo);
    }

    ////////////////////////////
    public long DetectionResultSave(SpectralDataInfo spectralDataInfo, ArchiveRecordsInfo saveDetectionInfo) {
        long spec_id = spectralDataRepository.insertSpecDataByRe(spectralDataInfo);
        spectralDataInfo.setId(spec_id);
        saveDetectionInfo.setSpecId(spec_id);
        long record_id = archiveRecordRepository.insertRecordByRe(saveDetectionInfo);
        return record_id;
    }

    public long DetectionResultUpdate(SpectralDataInfo spectralDataInfo, ArchiveRecordsInfo saveDetectionInfo) {
        long spec_id = spectralDataRepository.updateSpecDataByRe(spectralDataInfo);
        spectralDataInfo.setId(spec_id);
        saveDetectionInfo.setSpecId(spec_id);
        long record_id = archiveRecordRepository.updateRecordByRe(saveDetectionInfo);
        return record_id;
    }

    public boolean updateUploadOkById(int record_id) {
        return archiveRecordRepository.updateUploadOkById(record_id);
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////////

    public CalibrationFtInfo getCalibrationFtInfo() {
        return calibrationFtRepository.getCalibrationFtInfo();
    }

    public long insetCalibrationFtInfo(CalibrationFtInfo info) {
        return calibrationFtRepository.insertCalibrationFtByRe(info);
    }

    public void updateCalibrationFtInfo(CalibrationFtInfo info) {
        calibrationFtRepository.updateCalibrationFtByRe(info);
    }


}
