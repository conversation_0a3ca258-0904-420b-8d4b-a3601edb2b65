package com.ssraman.drugraman.ui.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.db.entity.PeakInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Administrator
 * @date: 2021/11/11
 */
public class AddPeakRecyViewAdapter extends RecyclerView.Adapter<AddPeakRecyViewAdapter.ViewHolder> {

    private Context mContext;
    private List<PeakInfo> mListData;
    private HashMap<Integer, Boolean> maps = new HashMap<Integer, Boolean>();//多选
    public AddPeakRecyViewAdapter(Context mContext) {
        this.mContext = mContext;
        this.mListData = new ArrayList<>();
        initMap();
    }

    private void initMap() {
        for (int i = 0; i <mListData.size() ; i++) {
            maps.put(i,false);   //每一次进入列表页面  都是未选中状态
        }
    }

    //全选方法
    public void All() {
        initMap();
        Set<Map.Entry<Integer, Boolean>> entries = maps.entrySet();
        boolean shouldall = false;
        for (Map.Entry<Integer, Boolean> entry : entries) {
            Boolean value = entry.getValue();
            if (!value) {
                shouldall = true;
                break;
            }
        }
        for (Map.Entry<Integer, Boolean> entry : entries) {
            entry.setValue(shouldall);
        }
        notifyDataSetChanged();
    }

    //反选
    public void neverall() {
        Set<Map.Entry<Integer, Boolean>> entries = maps.entrySet();
        for (Map.Entry<Integer, Boolean> entry : entries) {
            entry.setValue(!entry.getValue());
        }
        notifyDataSetChanged();
    }

    //多选
    public void MultiSelection(int position) {
        //对当前状态取反
        if (maps.get(position)) {
            maps.put(position, false);
        } else {
            maps.put(position, true);
        }
        notifyItemChanged(position);
    }

    //获取最终的map存储数据
    public Map<Integer, Boolean> getMap() {
        return maps;
    }

    public void update(List<PeakInfo> list) {
        if (list != null && list.size() > 0) {
            this.mListData.addAll(list);
            notifyDataSetChanged();
        }
    }

    /**
     * 设置适配器数据
     *
     * @param dataList
     * @param isClear  是否需要清空list然后在加载数据
     */
    public void update(List<PeakInfo> dataList, Boolean isClear) {
        if (isClear) {
            clear();
        }
        if (dataList != null) {
            this.mListData.addAll(dataList);
        }
        if(isClear)
        {
            initMap();
        }
        notifyDataSetChanged();
    }


    /**
     * 清空集合，设置适配器数据
     *
     * @param list
     */
    public void setDataList(List<PeakInfo> list) {
        this.mListData.clear();
        this.mListData.addAll(list);
        initMap();
        notifyDataSetChanged();
    }

    /**
     * 清除集合数据
     */
    public void clear() {
        this.mListData.clear();
        maps.clear();
        notifyDataSetChanged();
    }

    public List<PeakInfo> getSeletedItem()
    {
        List<PeakInfo> seletedList=new ArrayList<>();
        for(int i=0;i<this.mListData.size();i++)
        {
            if(maps.get(i))
            {
                seletedList.add(this.mListData.get(i));
            }
        }
        return  seletedList;
    }

    public List<PeakInfo> getUnSeletedItem()
    {
        List<PeakInfo> unSeletedList=new ArrayList<>();
        for(int i=0;i<this.mListData.size();i++)
        {
            if(!maps.get(i))
            {
                unSeletedList.add(this.mListData.get(i));
            }
        }
        return  unSeletedList;
    }

    @Override
    public int getItemCount() {
        return mListData.size();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int position) {
        View itemView = LayoutInflater.from(mContext).inflate(R.layout.item_recyitem_addlib_peak, viewGroup, false);
        return new ViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder,final int position) {
        viewHolder.edit_wave.setText(String.format("%.1f",mListData.get(position).getWave()));
        viewHolder.edit_intensity.setText(String.format("%.2f",mListData.get(position).getIntensity()));
        viewHolder.item_index.setText(String.valueOf(position+1));
        if(mListData.get(position).getMatchLimit()!=null) {
            viewHolder.edit_matchLimit.setText(String.valueOf(mListData.get(position).getMatchLimit()));
        }
        else
        {
            viewHolder.edit_matchLimit.setText("10");
        }
        viewHolder.edit_matchLimit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                try {
                    double match_limit = Double.valueOf(s.toString().trim());
                    mListData.get(position).setMatchLimit(match_limit);
                }
                catch (Exception e1)
                { }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        viewHolder.chk_select.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                maps.put(position, isChecked);
//                if (isChecked == true) {
//                    maps.put(position, true);
//                } else {
//                    maps.remove(position);
//                }
                if(onItemClickListener!=null)
                {
                    onItemClickListener.onItemClickListener(buttonView,position,isChecked);
                }
            }
        });

        if (maps.get(position) == null) {
            maps.put(position, false);
        }
        //没有设置tag之前会有item重复选框出现，设置tag之后，此问题解决
        viewHolder.chk_select.setChecked(maps.get(position));

        viewHolder.chk_must.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                mListData.get(position).setMust(isChecked?1:0);
            }
        });
        viewHolder.chk_must.setChecked( mListData.get(position).getMust()==1?true:false);

    }

    public class ViewHolder extends RecyclerView.ViewHolder{
        private RecyclerViewOnItemClickListener mListener;//接口
        private CheckBox chk_select;
        private TextView edit_wave;
        private TextView edit_intensity;
        private EditText edit_matchLimit;
        private CheckBox chk_must;
        private TextView item_index;

        public ViewHolder(View itemView) {
            super(itemView);
            chk_select = itemView.findViewById(R.id.chk_select);
            edit_wave = itemView.findViewById(R.id.edit_wave);
            edit_intensity = itemView.findViewById(R.id.edit_intensity);
            edit_matchLimit = itemView.findViewById(R.id.edit_matchLimit);
            chk_must = itemView.findViewById(R.id.chk_must);
            item_index = itemView.findViewById(R.id.edit_item_index);
        }
    }

    public RecyclerViewOnItemClickListener onItemClickListener;

    //回调的接口
    public void setItemClickListener(RecyclerViewOnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    //接口回调设置点击事件
    public interface RecyclerViewOnItemClickListener {
        //点击事件
        void onItemClickListener(View view, int position,boolean isChecked);
    }

}
