package com.ssraman.drugraman.rbac.service;

import com.ssraman.drugraman.rbac.entity.User;
import com.ssraman.drugraman.rbac.dto.AuthResult;
import com.ssraman.drugraman.rbac.dto.CreateUserRequest;
import com.ssraman.drugraman.rbac.dto.UpdateUserRequest;

import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Observable;
import io.reactivex.Single;

/**
 * 用户服务接口
 * 提供用户管理的核心功能
 */
public interface IUserService {
    
    // ========== 用户认证 ==========
    
    /**
     * 用户认证
     * @param username 用户名
     * @param password 密码
     * @return 认证结果
     */
    Single<AuthResult> authenticate(String username, String password);
    
    /**
     * 验证用户会话
     * @param sessionToken 会话令牌
     * @return 用户信息
     */
    Single<User> validateSession(String sessionToken);
    
    /**
     * 用户登出
     * @param sessionToken 会话令牌
     * @return 操作结果
     */
    Completable logout(String sessionToken);
    
    /**
     * 刷新会话
     * @param sessionToken 当前会话令牌
     * @return 新的会话令牌
     */
    Single<String> refreshSession(String sessionToken);
    
    // ========== 用户管理 ==========
    
    /**
     * 创建用户
     * @param request 创建用户请求
     * @return 创建的用户
     */
    Single<User> createUser(CreateUserRequest request);
    
    /**
     * 更新用户信息
     * @param userId 用户ID
     * @param request 更新用户请求
     * @return 更新后的用户
     */
    Single<User> updateUser(Long userId, UpdateUserRequest request);
    
    /**
     * 删除用户（软删除）
     * @param userId 用户ID
     * @return 操作结果
     */
    Completable deleteUser(Long userId);
    
    /**
     * 根据ID获取用户
     * @param userId 用户ID
     * @return 用户信息
     */
    Single<User> getUserById(Long userId);
    
    /**
     * 根据用户名获取用户
     * @param username 用户名
     * @return 用户信息
     */
    Single<User> getUserByUsername(String username);
    
    /**
     * 获取所有用户
     * @return 用户列表
     */
    Observable<List<User>> getAllUsers();
    
    /**
     * 搜索用户
     * @param keyword 关键词
     * @return 用户列表
     */
    Observable<List<User>> searchUsers(String keyword);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    Single<Boolean> existsUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    Single<Boolean> existsEmail(String email);
    
    // ========== 密码管理 ==========
    
    /**
     * 修改密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 操作结果
     */
    Completable changePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 重置密码
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 操作结果
     */
    Completable resetPassword(Long userId, String newPassword);
    
    /**
     * 验证密码强度
     * @param password 密码
     * @return 是否符合强度要求
     */
    Single<Boolean> validatePasswordStrength(String password);
    
    // ========== 用户状态管理 ==========
    
    /**
     * 激活用户
     * @param userId 用户ID
     * @return 操作结果
     */
    Completable activateUser(Long userId);
    
    /**
     * 停用用户
     * @param userId 用户ID
     * @return 操作结果
     */
    Completable deactivateUser(Long userId);
    
    /**
     * 锁定用户
     * @param userId 用户ID
     * @param lockDurationMinutes 锁定时长（分钟）
     * @return 操作结果
     */
    Completable lockUser(Long userId, int lockDurationMinutes);
    
    /**
     * 解锁用户
     * @param userId 用户ID
     * @return 操作结果
     */
    Completable unlockUser(Long userId);
    
    /**
     * 重置登录尝试次数
     * @param userId 用户ID
     * @return 操作结果
     */
    Completable resetLoginAttempts(Long userId);
    
    // ========== 用户统计 ==========
    
    /**
     * 获取用户总数
     * @return 用户总数
     */
    Single<Long> getUserCount();
    
    /**
     * 获取活跃用户数
     * @return 活跃用户数
     */
    Single<Long> getActiveUserCount();
    
    /**
     * 获取锁定用户数
     * @return 锁定用户数
     */
    Single<Long> getLockedUserCount();
    
    /**
     * 获取最近登录的用户列表
     * @param limit 限制数量
     * @return 用户列表
     */
    Observable<List<User>> getRecentLoginUsers(int limit);
}
