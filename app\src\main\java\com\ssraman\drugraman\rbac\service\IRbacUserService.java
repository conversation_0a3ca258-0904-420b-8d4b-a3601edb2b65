package com.ssraman.drugraman.rbac.service;

import com.ssraman.drugraman.db.entity.RbacUser;
import com.ssraman.drugraman.rbac.dto.AuthResult;

import java.util.List;

/**
 * RBAC用户服务接口
 * 提供用户管理的核心功能
 */
public interface IRbacUserService {
    
    // ========== 用户认证 ==========
    
    /**
     * 用户认证
     * @param username 用户名
     * @param password 密码
     * @return 认证结果
     */
    AuthResult authenticate(String username, String password);
    
    /**
     * 验证用户会话
     * @param sessionToken 会话令牌
     * @return 用户信息
     */
    RbacUser validateSession(String sessionToken);
    
    /**
     * 用户登出
     * @param sessionToken 会话令牌
     * @return 操作结果
     */
    boolean logout(String sessionToken);
    
    /**
     * 刷新会话
     * @param sessionToken 当前会话令牌
     * @return 新的会话令牌
     */
    String refreshSession(String sessionToken);
    
    // ========== 用户管理 ==========
    
    /**
     * 创建用户
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱
     * @param fullName 全名
     * @return 创建的用户
     */
    RbacUser createUser(String username, String password, String email, String fullName);
    
    /**
     * 更新用户信息
     * @param userId 用户ID
     * @param email 邮箱
     * @param fullName 全名
     * @return 更新后的用户
     */
    RbacUser updateUser(Long userId, String email, String fullName);
    
    /**
     * 删除用户（软删除）
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean deleteUser(Long userId);
    
    /**
     * 根据ID获取用户
     * @param userId 用户ID
     * @return 用户信息
     */
    RbacUser getUserById(Long userId);
    
    /**
     * 根据用户名获取用户
     * @param username 用户名
     * @return 用户信息
     */
    RbacUser getUserByUsername(String username);
    
    /**
     * 获取所有用户
     * @return 用户列表
     */
    List<RbacUser> getAllUsers();
    
    /**
     * 搜索用户
     * @param keyword 关键词
     * @return 用户列表
     */
    List<RbacUser> searchUsers(String keyword);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsEmail(String email);
    
    // ========== 密码管理 ==========
    
    /**
     * 修改密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 操作结果
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 重置密码
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 操作结果
     */
    boolean resetPassword(Long userId, String newPassword);
    
    /**
     * 验证密码强度
     * @param password 密码
     * @return 是否符合强度要求
     */
    boolean validatePasswordStrength(String password);
    
    // ========== 用户状态管理 ==========
    
    /**
     * 激活用户
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean activateUser(Long userId);
    
    /**
     * 停用用户
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean deactivateUser(Long userId);
    
    /**
     * 锁定用户
     * @param userId 用户ID
     * @param lockDurationMinutes 锁定时长（分钟）
     * @return 操作结果
     */
    boolean lockUser(Long userId, int lockDurationMinutes);
    
    /**
     * 解锁用户
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean unlockUser(Long userId);
    
    /**
     * 重置登录尝试次数
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean resetLoginAttempts(Long userId);
    
    // ========== 用户统计 ==========
    
    /**
     * 获取用户总数
     * @return 用户总数
     */
    long getUserCount();
    
    /**
     * 获取活跃用户数
     * @return 活跃用户数
     */
    long getActiveUserCount();
    
    /**
     * 获取锁定用户数
     * @return 锁定用户数
     */
    long getLockedUserCount();
    
    /**
     * 获取最近登录的用户列表
     * @param limit 限制数量
     * @return 用户列表
     */
    List<RbacUser> getRecentLoginUsers(int limit);
}
