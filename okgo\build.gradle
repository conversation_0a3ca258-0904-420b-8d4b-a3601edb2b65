apply plugin: 'com.android.library'

android {
    compileSdkVersion build_versions.compileSdk
    buildToolsVersion build_versions.buildTools

    defaultConfig {
        minSdkVersion build_versions.minSdk
        targetSdkVersion build_versions.targetSdk
        versionCode app_version.versionCode
        versionName app_version.versionName
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation deps.okhttp.okhttp3
}

