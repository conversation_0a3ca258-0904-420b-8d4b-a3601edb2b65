# DrugRaman RBAC权限系统重构项目

## 项目概述

本项目是对DrugRaman药物拉曼光谱检测系统的权限管理模块进行的全面重构，将原有的简单权限级别系统升级为完整的基于角色的访问控制（RBAC）系统。

## 重构目标

### 业务目标
- 实现5种标准角色的权限体系
- 提供细粒度的功能权限控制
- 增强系统安全性和可扩展性
- 改善用户权限管理体验

### 技术目标
- 建立完整的RBAC权限模型
- 实现统一的权限验证机制
- 提供安全的会话管理
- 建立完善的审计日志系统

## 角色权限体系

### 1. 操作员（Operator）
- **权限级别**: 1
- **权限范围**: 仅可执行检测操作
- **具体功能**:
  - 药物拉曼光谱检测
  - 查看检测结果
  - 导出基础检测数据

### 2. 审核员（Reviewer）
- **权限级别**: 2
- **权限范围**: 检测 + 报告管理
- **具体功能**:
  - 继承操作员所有权限
  - 创建和发布检测报告
  - 复审已发布的报告
  - 报告审批和签名
  - 查看和搜索档案记录

### 3. 谱图管理员（Spectrum Manager）
- **权限级别**: 3
- **权限范围**: 检测 + 谱图库管理
- **具体功能**:
  - 继承操作员所有权限
  - 创建和管理拉曼谱图
  - 维护标准谱图数据
  - 管理谱图库
  - 校准数据管理

### 4. 用户管理员（User Administrator）
- **权限级别**: 4
- **权限范围**: 仅用户账号管理
- **具体功能**:
  - 创建、编辑、删除用户账号
  - 分配和撤销角色权限
  - 管理用户状态
  - 重置用户密码

### 5. 系统管理员（System Administrator）
- **权限级别**: 5
- **权限范围**: 所有系统权限
- **具体功能**:
  - 继承所有其他角色的权限
  - 系统配置和管理
  - 数据备份和恢复
  - 查看系统日志和审计日志
  - 系统监控

## 技术架构

### 数据库设计
```
tb_user (用户表)
├── id, username, password_hash, salt
├── email, full_name, status
└── created_at, updated_at, last_login

tb_role (角色表)
├── id, role_code, role_name
├── description, level, status
└── created_at, updated_at

tb_permission (权限表)
├── id, permission_code, permission_name
├── resource, action, description
└── status, created_at, updated_at

tb_user_role (用户角色关联表)
├── id, user_id, role_id
├── assigned_at, assigned_by
└── expires_at, status

tb_role_permission (角色权限关联表)
├── id, role_id, permission_id
└── assigned_at, assigned_by, status

tb_user_session (用户会话表)
├── id, user_id, session_token
├── device_info, created_at
└── expires_at, status

tb_audit_log (审计日志表)
├── id, user_id, action
├── resource, details
└── ip_address, timestamp
```

### 核心组件

#### 1. 权限检查器 (PermissionChecker)
- 统一的权限验证接口
- 支持权限缓存机制
- 提供多种权限检查方式

#### 2. 安全上下文 (SecurityContext)
- 线程安全的用户信息管理
- 当前用户权限缓存
- 会话状态管理

#### 3. 权限拦截器 (PermissionInterceptor)
- 基于注解的权限验证
- 方法级别的权限控制
- 统一的异常处理

#### 4. 权限缓存 (PermissionCache)
- 内存缓存用户权限
- 自动过期和清理机制
- 提高权限检查性能

### API接口设计

#### 用户管理API
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/users` - 创建用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

#### 角色管理API
- `GET /api/roles` - 获取角色列表
- `POST /api/users/{id}/roles` - 分配角色
- `DELETE /api/users/{id}/roles/{roleId}` - 撤销角色

#### 权限管理API
- `GET /api/permissions` - 获取权限列表
- `GET /api/users/{id}/permissions` - 获取用户权限
- `POST /api/auth/permissions/check` - 检查权限

## 安全特性

### 1. 密码安全
- 使用SHA-256哈希算法
- 随机盐值加密
- 密码强度验证

### 2. 会话管理
- 安全的会话令牌
- 自动过期机制
- 设备信息记录

### 3. 权限验证
- 最小权限原则
- 权限分离机制
- 实时权限检查

### 4. 审计日志
- 完整的操作记录
- 用户行为追踪
- 安全事件监控

## 使用示例

### 权限注解使用
```java
@RequirePermission("USER_CREATE")
public void createUser(CreateUserRequest request) {
    // 创建用户逻辑
}

@RequireRole(minLevel = 3)
public void manageSpectrum() {
    // 谱图管理逻辑
}

@RequirePermission(value = {"REPORT_CREATE", "REPORT_PUBLISH"}, 
                   logical = RequirePermission.LogicalOperator.OR)
public void handleReport() {
    // 报告处理逻辑
}
```

### 程序化权限检查
```java
// 检查单个权限
if (permissionChecker.hasPermission(userId, PermissionType.DETECTION_EXECUTE)) {
    // 执行检测
}

// 检查角色级别
if (permissionChecker.hasRoleLevel(userId, RoleType.REVIEWER.getLevel())) {
    // 审核员级别操作
}

// 检查多个权限
if (permissionChecker.hasAllPermissions(userId, "USER_CREATE", "USER_EDIT")) {
    // 需要多个权限的操作
}
```

### 安全上下文使用
```java
// 设置当前用户
SecurityContext.setCurrentUser(user);
SecurityContext.setCurrentPermissions(permissions);

// 获取当前用户信息
User currentUser = SecurityContext.getCurrentUser();
boolean hasPermission = SecurityContext.hasPermission("USER_CREATE");

// 清理上下文
SecurityContext.clear();
```

## 测试覆盖

### 单元测试
- PermissionChecker功能测试
- SecurityContext线程安全测试
- 权限注解验证测试

### 集成测试
- 完整RBAC权限体系测试
- 角色权限继承测试
- 跨角色权限验证测试

### 安全测试
- 权限绕过攻击测试
- 会话安全测试
- 输入验证测试

## 部署说明

### 环境要求
- Android API Level 21+
- SQLite 3.0+
- RxJava 2.x

### 部署步骤
1. 备份现有数据库
2. 执行数据库迁移脚本
3. 部署新版本应用
4. 验证功能正常性
5. 监控系统运行状态

### 回滚方案
- 数据库备份恢复
- 应用版本回退
- 配置文件恢复

## 性能优化

### 权限缓存
- 30分钟权限缓存
- 自动清理过期缓存
- 缓存命中率监控

### 数据库优化
- 关键字段索引
- 查询语句优化
- 连接池管理

## 维护指南

### 日常维护
- 监控权限系统性能
- 定期清理审计日志
- 检查安全事件

### 扩展开发
- 新增权限类型
- 自定义角色创建
- 细粒度权限控制

## 联系信息

如有问题或建议，请联系开发团队。

---

**版本**: 2.0.0  
**更新日期**: 2024年12月  
**维护团队**: DrugRaman开发组
