<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="10dp"
    android:paddingRight="10dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@color/nc_sub"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_login_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:text="@string/title_user_login"
                android:textColor="@color/black"
                android:textSize="17dp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="5dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="2dp"
                    android:text="@string/title_username_with_colon"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <com.ssraman.control.spinner.MaterialSpinner
                    android:id="@+id/etuser"
                    android:layout_width="200dp"
                    android:layout_height="44dp"
                    android:background="@drawable/input_border_bottom_border"
                    android:textSize="24sp"
                    app:ms_background_color="@color/dark_gray"
                    app:ms_popupwindow_height="wrap_content"
                    app:ms_popupwindow_maxheight="200dp"
                    app:ms_text_color="@android:color/white"
                    tools:layout_editor_absoluteX="129dp"
                    tools:layout_editor_absoluteY="69dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="5dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="26dp"
                    android:paddingTop="10dp"
                    android:text="@string/title_password_with_colon"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:hint="@string/login_password_hint"
                    android:textColorHint="@color/light_gray"
                    app:hintEnabled="false"
                    app:hintTextAppearance="@style/TextAppearence.App.TextInputLayout"
                    app:passwordToggleEnabled="true"
                    app:passwordToggleTint="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPassword"
                        style="@style/TextStyle"
                        android:layout_width="200dp"
                        android:layout_height="44dp"
                        android:background="@drawable/input_border_bottom_border"
                        android:cursorVisible="true"
                        android:gravity="center|left|bottom"
                        android:inputType="textPassword"
                        android:maxLength="50"
                        android:paddingLeft="8dp"
                        android:paddingBottom="10dp"
                        android:text=""
                        android:textColor="@color/white"
                        android:textCursorDrawable="@null"
                        android:textSize="18sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginTop="10dp"
                android:background="@color/nc_light" />

            <LinearLayout
                android:id="@+id/box_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_login_cancel"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_dialog_ios_left_light"
                    android:clickable="true"
                    android:gravity="center"
                    android:text="@string/btn_cancel"
                    android:textColor="@color/nc_light"
                    android:textSize="17dp" />

                <ImageView
                    android:layout_width="1px"
                    android:layout_height="match_parent"
                    android:background="@color/nc_light" />

                <TextView
                    android:id="@+id/btn_login_ok"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_dialog_ios_right_light"
                    android:clickable="true"
                    android:gravity="center"
                    android:text="@string/btn_confirm"
                    android:textColor="@color/nc_light"
                    android:textSize="17dp" />
            </LinearLayout>

        </LinearLayout>


    </RelativeLayout>
</RelativeLayout>
