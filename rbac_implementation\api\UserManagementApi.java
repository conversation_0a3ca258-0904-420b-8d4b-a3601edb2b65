package com.ssraman.drugraman.rbac.api;

import com.ssraman.drugraman.rbac.annotation.RequirePermission;
import com.ssraman.drugraman.rbac.annotation.RequireRole;
import com.ssraman.drugraman.rbac.dto.*;
import com.ssraman.drugraman.rbac.entity.User;
import com.ssraman.drugraman.rbac.service.IUserService;

import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Observable;
import io.reactivex.Single;

/**
 * 用户管理API接口
 * 提供用户管理相关的REST API
 */
public class UserManagementApi {
    
    private final IUserService userService;
    
    public UserManagementApi(IUserService userService) {
        this.userService = userService;
    }
    
    // ========== 用户认证相关API ==========
    
    /**
     * 用户登录
     * POST /api/auth/login
     */
    public Single<LoginResponse> login(LoginRequest request) {
        return userService.authenticate(request.getUsername(), request.getPassword())
                .map(authResult -> {
                    LoginResponse response = new LoginResponse();
                    response.setSuccess(authResult.isSuccess());
                    response.setMessage(authResult.getMessage());
                    if (authResult.isSuccess()) {
                        response.setUser(authResult.getUser());
                        response.setSessionToken(authResult.getSessionToken());
                        response.setPermissions(authResult.getPermissions());
                        response.setRoles(authResult.getRoles());
                    }
                    return response;
                });
    }
    
    /**
     * 用户登出
     * POST /api/auth/logout
     */
    public Completable logout(LogoutRequest request) {
        return userService.logout(request.getSessionToken());
    }
    
    /**
     * 刷新会话
     * POST /api/auth/refresh
     */
    public Single<RefreshResponse> refreshSession(RefreshRequest request) {
        return userService.refreshSession(request.getSessionToken())
                .map(newToken -> {
                    RefreshResponse response = new RefreshResponse();
                    response.setSessionToken(newToken);
                    return response;
                });
    }
    
    /**
     * 验证会话
     * GET /api/auth/validate
     */
    public Single<ValidateResponse> validateSession(String sessionToken) {
        return userService.validateSession(sessionToken)
                .map(user -> {
                    ValidateResponse response = new ValidateResponse();
                    response.setValid(true);
                    response.setUser(user);
                    return response;
                })
                .onErrorReturn(throwable -> {
                    ValidateResponse response = new ValidateResponse();
                    response.setValid(false);
                    response.setMessage(throwable.getMessage());
                    return response;
                });
    }
    
    // ========== 用户管理相关API ==========
    
    /**
     * 创建用户
     * POST /api/users
     */
    @RequirePermission("USER_CREATE")
    public Single<CreateUserResponse> createUser(CreateUserRequest request) {
        return userService.createUser(request)
                .map(user -> {
                    CreateUserResponse response = new CreateUserResponse();
                    response.setSuccess(true);
                    response.setUser(user);
                    response.setMessage("用户创建成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    CreateUserResponse response = new CreateUserResponse();
                    response.setSuccess(false);
                    response.setMessage("用户创建失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 更新用户信息
     * PUT /api/users/{userId}
     */
    @RequirePermission("USER_EDIT")
    public Single<UpdateUserResponse> updateUser(Long userId, UpdateUserRequest request) {
        return userService.updateUser(userId, request)
                .map(user -> {
                    UpdateUserResponse response = new UpdateUserResponse();
                    response.setSuccess(true);
                    response.setUser(user);
                    response.setMessage("用户信息更新成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    UpdateUserResponse response = new UpdateUserResponse();
                    response.setSuccess(false);
                    response.setMessage("用户信息更新失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 删除用户
     * DELETE /api/users/{userId}
     */
    @RequirePermission("USER_DELETE")
    public Single<DeleteUserResponse> deleteUser(Long userId) {
        return userService.deleteUser(userId)
                .toSingle(() -> {
                    DeleteUserResponse response = new DeleteUserResponse();
                    response.setSuccess(true);
                    response.setMessage("用户删除成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    DeleteUserResponse response = new DeleteUserResponse();
                    response.setSuccess(false);
                    response.setMessage("用户删除失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取用户详情
     * GET /api/users/{userId}
     */
    @RequirePermission("USER_VIEW")
    public Single<GetUserResponse> getUser(Long userId) {
        return userService.getUserById(userId)
                .map(user -> {
                    GetUserResponse response = new GetUserResponse();
                    response.setSuccess(true);
                    response.setUser(user);
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetUserResponse response = new GetUserResponse();
                    response.setSuccess(false);
                    response.setMessage("获取用户信息失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 获取用户列表
     * GET /api/users
     */
    @RequirePermission("USER_VIEW")
    public Single<GetUsersResponse> getUsers(GetUsersRequest request) {
        Observable<List<User>> usersObservable;
        
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            usersObservable = userService.searchUsers(request.getKeyword());
        } else {
            usersObservable = userService.getAllUsers();
        }
        
        return usersObservable
                .firstOrError()
                .map(users -> {
                    GetUsersResponse response = new GetUsersResponse();
                    response.setSuccess(true);
                    response.setUsers(users);
                    response.setTotal(users.size());
                    return response;
                })
                .onErrorReturn(throwable -> {
                    GetUsersResponse response = new GetUsersResponse();
                    response.setSuccess(false);
                    response.setMessage("获取用户列表失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 修改密码
     * PUT /api/users/{userId}/password
     */
    @RequirePermission("USER_EDIT")
    public Single<ChangePasswordResponse> changePassword(Long userId, ChangePasswordRequest request) {
        return userService.changePassword(userId, request.getOldPassword(), request.getNewPassword())
                .toSingle(() -> {
                    ChangePasswordResponse response = new ChangePasswordResponse();
                    response.setSuccess(true);
                    response.setMessage("密码修改成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    ChangePasswordResponse response = new ChangePasswordResponse();
                    response.setSuccess(false);
                    response.setMessage("密码修改失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 重置密码
     * PUT /api/users/{userId}/reset-password
     */
    @RequirePermission("PASSWORD_RESET")
    public Single<ResetPasswordResponse> resetPassword(Long userId, ResetPasswordRequest request) {
        return userService.resetPassword(userId, request.getNewPassword())
                .toSingle(() -> {
                    ResetPasswordResponse response = new ResetPasswordResponse();
                    response.setSuccess(true);
                    response.setMessage("密码重置成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    ResetPasswordResponse response = new ResetPasswordResponse();
                    response.setSuccess(false);
                    response.setMessage("密码重置失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 激活用户
     * PUT /api/users/{userId}/activate
     */
    @RequirePermission("USER_STATUS_MANAGE")
    public Single<UserStatusResponse> activateUser(Long userId) {
        return userService.activateUser(userId)
                .toSingle(() -> {
                    UserStatusResponse response = new UserStatusResponse();
                    response.setSuccess(true);
                    response.setMessage("用户激活成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    UserStatusResponse response = new UserStatusResponse();
                    response.setSuccess(false);
                    response.setMessage("用户激活失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 停用用户
     * PUT /api/users/{userId}/deactivate
     */
    @RequirePermission("USER_STATUS_MANAGE")
    public Single<UserStatusResponse> deactivateUser(Long userId) {
        return userService.deactivateUser(userId)
                .toSingle(() -> {
                    UserStatusResponse response = new UserStatusResponse();
                    response.setSuccess(true);
                    response.setMessage("用户停用成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    UserStatusResponse response = new UserStatusResponse();
                    response.setSuccess(false);
                    response.setMessage("用户停用失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 锁定用户
     * PUT /api/users/{userId}/lock
     */
    @RequirePermission("USER_STATUS_MANAGE")
    public Single<UserStatusResponse> lockUser(Long userId, LockUserRequest request) {
        return userService.lockUser(userId, request.getLockDurationMinutes())
                .toSingle(() -> {
                    UserStatusResponse response = new UserStatusResponse();
                    response.setSuccess(true);
                    response.setMessage("用户锁定成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    UserStatusResponse response = new UserStatusResponse();
                    response.setSuccess(false);
                    response.setMessage("用户锁定失败: " + throwable.getMessage());
                    return response;
                });
    }
    
    /**
     * 解锁用户
     * PUT /api/users/{userId}/unlock
     */
    @RequirePermission("USER_STATUS_MANAGE")
    public Single<UserStatusResponse> unlockUser(Long userId) {
        return userService.unlockUser(userId)
                .toSingle(() -> {
                    UserStatusResponse response = new UserStatusResponse();
                    response.setSuccess(true);
                    response.setMessage("用户解锁成功");
                    return response;
                })
                .onErrorReturn(throwable -> {
                    UserStatusResponse response = new UserStatusResponse();
                    response.setSuccess(false);
                    response.setMessage("用户解锁失败: " + throwable.getMessage());
                    return response;
                });
    }
}
