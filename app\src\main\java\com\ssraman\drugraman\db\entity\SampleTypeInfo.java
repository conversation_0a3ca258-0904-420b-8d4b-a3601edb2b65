package com.ssraman.drugraman.db.entity;

import androidx.annotation.NonNull;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Generated;

/**
 * @author: Administrator
 * @date: 2021/10/9
 */
@Entity(nameInDb = "SampleType",createInDb = false)
public class SampleTypeInfo {
    // id主键
    @Id(autoincrement = true)
    @Property(nameInDb = "Id")
    private Long Id;
    // 父节点
    @Property(nameInDb = "ParentId")
    private Integer ParentId;
    // 系统Ft的ParentId，此表的外键
    @Property(nameInDb = "FId")
    private Integer FId=0;
    // 名称
    @Property(nameInDb = "Name")
    private String Name;
    // 备注
    @Property(nameInDb = "Remark")
    private String Remark;
    // 类别(节点类型：高层、低层、项)(项节点的名称是在加入用户库设定的)
    @Property(nameInDb = "Type")
    private Integer Type;
    // 类别,自定义加入标准时为0（配合主键查询本库），系统为1（配合外键查询外库），其他可扩展，如：自定义标准样品是其他节点生成的可定义为2（配合外键查询本库）；
    // 删除时只有主键可删除Ft数据，  扩展：且外键无本删除ID及LibraryType=2的，如有也要删除相应sampleType数据；
    @Property(nameInDb = "LibraryType")
    private Integer LibraryType=1;
    // 图标号
    @Property(nameInDb = "ImageIndex")
    private Integer ImageIndex=0;
    @Generated(hash = 691650741)
    public SampleTypeInfo(Long Id, Integer ParentId, Integer FId, String Name, String Remark,
            Integer Type, Integer LibraryType, Integer ImageIndex) {
        this.Id = Id;
        this.ParentId = ParentId;
        this.FId = FId;
        this.Name = Name;
        this.Remark = Remark;
        this.Type = Type;
        this.LibraryType = LibraryType;
        this.ImageIndex = ImageIndex;
    }
    @Generated(hash = 945180449)
    public SampleTypeInfo() {
    }
    public Long getId() {
        return this.Id;
    }
    public void setId(Long Id) {
        this.Id = Id;
    }
    public Integer getParentId() {
        return this.ParentId;
    }
    public void setParentId(Integer ParentId) {
        this.ParentId = ParentId;
    }
    public Integer getFId() {
        return this.FId;
    }
    public void setFId(Integer FId) {
        this.FId = FId;
    }
    public String getName() {
        return this.Name;
    }
    public void setName(String Name) {
        this.Name = Name;
    }
    public String getRemark() {
        return this.Remark;
    }
    public void setRemark(String Remark) {
        this.Remark = Remark;
    }
    public Integer getType() {
        return this.Type;
    }
    public void setType(Integer Type) {
        this.Type = Type;
    }
    public Integer getLibraryType() {
        return this.LibraryType;
    }
    public void setLibraryType(Integer LibraryType) {
        this.LibraryType = LibraryType;
    }
    public Integer getImageIndex() {
        return this.ImageIndex;
    }
    public void setImageIndex(Integer ImageIndex) {
        this.ImageIndex = ImageIndex;
    }


    @NonNull
    @Override
    public String toString() {
        return this.Name.toString();
    }

}
