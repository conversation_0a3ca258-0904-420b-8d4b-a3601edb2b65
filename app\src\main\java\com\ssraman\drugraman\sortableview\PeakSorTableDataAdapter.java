package com.ssraman.drugraman.sortableview;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ssraman.drugraman.db.entity.PeakInfo;

import java.util.List;

import de.codecrafters.tableview.TableView;
import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * @author: Administrator
 * @date: 2021/6/29
 */
public class PeakSorTableDataAdapter extends LongPressAwareTableDataAdapter<PeakInfo> {
    private static final int TEXT_SIZE = 16;

    public PeakSorTableDataAdapter(Context context, List data, TableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final PeakInfo peakInfo = getRowData(rowIndex);
        View renderedView = null;
        switch (columnIndex) {
            case 0:
                double wave = peakInfo.getWave();
                renderedView = renderString(String.format("%.2f",wave));
                break;
            case 1:
                double intensity = peakInfo.getIntensity();
                renderedView = renderString(String.format("%.2f",intensity));
                break;
        }
        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final PeakInfo peakInfo = getRowData(rowIndex);
        View renderedView = null;

        renderedView = getDefaultCellView(rowIndex, columnIndex, parentView);

        return renderedView;
    }

    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        if(value==null||value.equals("null"))
        {
            textView.setText("");
        }
        else
        {
            textView.setText(value);
        }
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setTextColor(Color.WHITE);
        return textView;
    }

}
