package com.ssraman.drugraman.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.ssraman.drugraman.db.StringDateConverter;
import java.util.Date;

import com.ssraman.drugraman.db.entity.FtInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "Ft".
*/
public class FtInfoDao extends AbstractDao<FtInfo, Long> {

    public static final String TABLENAME = "Ft";

    /**
     * Properties of entity FtInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "Id", true, "Id");
        public final static Property Mark = new Property(1, String.class, "Mark", false, "Mark");
        public final static Property SampleTypeId = new Property(2, Integer.class, "SampleTypeId", false, "SampleTypeId");
        public final static Property ObWave = new Property(3, byte[].class, "ObWave", false, "Wave");
        public final static Property ObIntensity = new Property(4, byte[].class, "ObIntensity", false, "Intensity");
        public final static Property ObNoise = new Property(5, byte[].class, "ObNoise", false, "Noise");
        public final static Property SendWave = new Property(6, Double.class, "SendWave", false, "SendWave");
        public final static Property HanLiang = new Property(7, Double.class, "HanLiang", false, "HanLiang");
        public final static Property Rate = new Property(8, Double.class, "Rate", false, "rate");
        public final static Property Type = new Property(9, Integer.class, "Type", false, "type");
        public final static Property SampleName = new Property(10, String.class, "SampleName", false, "SampleName");
        public final static Property MatchLimit = new Property(11, Double.class, "MatchLimit", false, "MatchLimit");
        public final static Property MatchScale = new Property(12, Double.class, "MatchScale", false, "MatchScale");
        public final static Property Confidence = new Property(13, Double.class, "Confidence", false, "Confidence");
        public final static Property Standard = new Property(14, Integer.class, "Standard", false, "Standard");
        public final static Property StartWave = new Property(15, Integer.class, "StartWave", false, "StartWave");
        public final static Property IntegratioTime = new Property(16, Integer.class, "IntegratioTime", false, "IntegratioTime");
        public final static Property LaserPower = new Property(17, Double.class, "LaserPower", false, "LaserPower");
        public final static Property AverageCount = new Property(18, Integer.class, "AverageCount", false, "AverageCount");
        public final static Property SourceType = new Property(19, Integer.class, "SourceType", false, "SourceType");
        public final static Property DescriptionInformation = new Property(20, byte[].class, "DescriptionInformation", false, "DescriptionInformation");
        public final static Property SpecType = new Property(21, Integer.class, "SpecType", false, "SpecType");
        public final static Property Inspector = new Property(22, String.class, "Inspector", false, "Publisher");
        public final static Property InspectorTime = new Property(23, String.class, "InspectorTime", false, "PublisherTime");
        public final static Property InspectorPass = new Property(24, Integer.class, "InspectorPass", false, "PublisherPass");
        public final static Property PublisherMacAddress = new Property(25, String.class, "PublisherMacAddress", false, "PublisherMacAddress");
        public final static Property Reviewer = new Property(26, String.class, "Reviewer", false, "Reviewer");
        public final static Property ReviewerTime = new Property(27, String.class, "ReviewerTime", false, "ReviewerTime");
        public final static Property ReviewerPass = new Property(28, Integer.class, "ReviewerPass", false, "ReviewerPass");
        public final static Property ReviewerMacAddress = new Property(29, String.class, "ReviewerMacAddress", false, "ReviewerMacAddress");
    }

    private final StringDateConverter InspectorTimeConverter = new StringDateConverter();
    private final StringDateConverter ReviewerTimeConverter = new StringDateConverter();

    public FtInfoDao(DaoConfig config) {
        super(config);
    }
    
    public FtInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, FtInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        String Mark = entity.getMark();
        if (Mark != null) {
            stmt.bindString(2, Mark);
        }
 
        Integer SampleTypeId = entity.getSampleTypeId();
        if (SampleTypeId != null) {
            stmt.bindLong(3, SampleTypeId);
        }
 
        byte[] ObWave = entity.getObWave();
        if (ObWave != null) {
            stmt.bindBlob(4, ObWave);
        }
 
        byte[] ObIntensity = entity.getObIntensity();
        if (ObIntensity != null) {
            stmt.bindBlob(5, ObIntensity);
        }
 
        byte[] ObNoise = entity.getObNoise();
        if (ObNoise != null) {
            stmt.bindBlob(6, ObNoise);
        }
 
        Double SendWave = entity.getSendWave();
        if (SendWave != null) {
            stmt.bindDouble(7, SendWave);
        }
 
        Double HanLiang = entity.getHanLiang();
        if (HanLiang != null) {
            stmt.bindDouble(8, HanLiang);
        }
 
        Double Rate = entity.getRate();
        if (Rate != null) {
            stmt.bindDouble(9, Rate);
        }
 
        Integer Type = entity.getType();
        if (Type != null) {
            stmt.bindLong(10, Type);
        }
 
        String SampleName = entity.getSampleName();
        if (SampleName != null) {
            stmt.bindString(11, SampleName);
        }
 
        Double MatchLimit = entity.getMatchLimit();
        if (MatchLimit != null) {
            stmt.bindDouble(12, MatchLimit);
        }
 
        Double MatchScale = entity.getMatchScale();
        if (MatchScale != null) {
            stmt.bindDouble(13, MatchScale);
        }
 
        Double Confidence = entity.getConfidence();
        if (Confidence != null) {
            stmt.bindDouble(14, Confidence);
        }
 
        Integer Standard = entity.getStandard();
        if (Standard != null) {
            stmt.bindLong(15, Standard);
        }
 
        Integer StartWave = entity.getStartWave();
        if (StartWave != null) {
            stmt.bindLong(16, StartWave);
        }
 
        Integer IntegratioTime = entity.getIntegratioTime();
        if (IntegratioTime != null) {
            stmt.bindLong(17, IntegratioTime);
        }
 
        Double LaserPower = entity.getLaserPower();
        if (LaserPower != null) {
            stmt.bindDouble(18, LaserPower);
        }
 
        Integer AverageCount = entity.getAverageCount();
        if (AverageCount != null) {
            stmt.bindLong(19, AverageCount);
        }
 
        Integer SourceType = entity.getSourceType();
        if (SourceType != null) {
            stmt.bindLong(20, SourceType);
        }
 
        byte[] DescriptionInformation = entity.getDescriptionInformation();
        if (DescriptionInformation != null) {
            stmt.bindBlob(21, DescriptionInformation);
        }
 
        Integer SpecType = entity.getSpecType();
        if (SpecType != null) {
            stmt.bindLong(22, SpecType);
        }
 
        String Inspector = entity.getInspector();
        if (Inspector != null) {
            stmt.bindString(23, Inspector);
        }
 
        Date InspectorTime = entity.getInspectorTime();
        if (InspectorTime != null) {
            stmt.bindString(24, InspectorTimeConverter.convertToDatabaseValue(InspectorTime));
        }
 
        Integer InspectorPass = entity.getInspectorPass();
        if (InspectorPass != null) {
            stmt.bindLong(25, InspectorPass);
        }
 
        String PublisherMacAddress = entity.getPublisherMacAddress();
        if (PublisherMacAddress != null) {
            stmt.bindString(26, PublisherMacAddress);
        }
 
        String Reviewer = entity.getReviewer();
        if (Reviewer != null) {
            stmt.bindString(27, Reviewer);
        }
 
        Date ReviewerTime = entity.getReviewerTime();
        if (ReviewerTime != null) {
            stmt.bindString(28, ReviewerTimeConverter.convertToDatabaseValue(ReviewerTime));
        }
 
        Integer ReviewerPass = entity.getReviewerPass();
        if (ReviewerPass != null) {
            stmt.bindLong(29, ReviewerPass);
        }
 
        String ReviewerMacAddress = entity.getReviewerMacAddress();
        if (ReviewerMacAddress != null) {
            stmt.bindString(30, ReviewerMacAddress);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, FtInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        String Mark = entity.getMark();
        if (Mark != null) {
            stmt.bindString(2, Mark);
        }
 
        Integer SampleTypeId = entity.getSampleTypeId();
        if (SampleTypeId != null) {
            stmt.bindLong(3, SampleTypeId);
        }
 
        byte[] ObWave = entity.getObWave();
        if (ObWave != null) {
            stmt.bindBlob(4, ObWave);
        }
 
        byte[] ObIntensity = entity.getObIntensity();
        if (ObIntensity != null) {
            stmt.bindBlob(5, ObIntensity);
        }
 
        byte[] ObNoise = entity.getObNoise();
        if (ObNoise != null) {
            stmt.bindBlob(6, ObNoise);
        }
 
        Double SendWave = entity.getSendWave();
        if (SendWave != null) {
            stmt.bindDouble(7, SendWave);
        }
 
        Double HanLiang = entity.getHanLiang();
        if (HanLiang != null) {
            stmt.bindDouble(8, HanLiang);
        }
 
        Double Rate = entity.getRate();
        if (Rate != null) {
            stmt.bindDouble(9, Rate);
        }
 
        Integer Type = entity.getType();
        if (Type != null) {
            stmt.bindLong(10, Type);
        }
 
        String SampleName = entity.getSampleName();
        if (SampleName != null) {
            stmt.bindString(11, SampleName);
        }
 
        Double MatchLimit = entity.getMatchLimit();
        if (MatchLimit != null) {
            stmt.bindDouble(12, MatchLimit);
        }
 
        Double MatchScale = entity.getMatchScale();
        if (MatchScale != null) {
            stmt.bindDouble(13, MatchScale);
        }
 
        Double Confidence = entity.getConfidence();
        if (Confidence != null) {
            stmt.bindDouble(14, Confidence);
        }
 
        Integer Standard = entity.getStandard();
        if (Standard != null) {
            stmt.bindLong(15, Standard);
        }
 
        Integer StartWave = entity.getStartWave();
        if (StartWave != null) {
            stmt.bindLong(16, StartWave);
        }
 
        Integer IntegratioTime = entity.getIntegratioTime();
        if (IntegratioTime != null) {
            stmt.bindLong(17, IntegratioTime);
        }
 
        Double LaserPower = entity.getLaserPower();
        if (LaserPower != null) {
            stmt.bindDouble(18, LaserPower);
        }
 
        Integer AverageCount = entity.getAverageCount();
        if (AverageCount != null) {
            stmt.bindLong(19, AverageCount);
        }
 
        Integer SourceType = entity.getSourceType();
        if (SourceType != null) {
            stmt.bindLong(20, SourceType);
        }
 
        byte[] DescriptionInformation = entity.getDescriptionInformation();
        if (DescriptionInformation != null) {
            stmt.bindBlob(21, DescriptionInformation);
        }
 
        Integer SpecType = entity.getSpecType();
        if (SpecType != null) {
            stmt.bindLong(22, SpecType);
        }
 
        String Inspector = entity.getInspector();
        if (Inspector != null) {
            stmt.bindString(23, Inspector);
        }
 
        Date InspectorTime = entity.getInspectorTime();
        if (InspectorTime != null) {
            stmt.bindString(24, InspectorTimeConverter.convertToDatabaseValue(InspectorTime));
        }
 
        Integer InspectorPass = entity.getInspectorPass();
        if (InspectorPass != null) {
            stmt.bindLong(25, InspectorPass);
        }
 
        String PublisherMacAddress = entity.getPublisherMacAddress();
        if (PublisherMacAddress != null) {
            stmt.bindString(26, PublisherMacAddress);
        }
 
        String Reviewer = entity.getReviewer();
        if (Reviewer != null) {
            stmt.bindString(27, Reviewer);
        }
 
        Date ReviewerTime = entity.getReviewerTime();
        if (ReviewerTime != null) {
            stmt.bindString(28, ReviewerTimeConverter.convertToDatabaseValue(ReviewerTime));
        }
 
        Integer ReviewerPass = entity.getReviewerPass();
        if (ReviewerPass != null) {
            stmt.bindLong(29, ReviewerPass);
        }
 
        String ReviewerMacAddress = entity.getReviewerMacAddress();
        if (ReviewerMacAddress != null) {
            stmt.bindString(30, ReviewerMacAddress);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public FtInfo readEntity(Cursor cursor, int offset) {
        FtInfo entity = new FtInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // Id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // Mark
            cursor.isNull(offset + 2) ? null : cursor.getInt(offset + 2), // SampleTypeId
            cursor.isNull(offset + 3) ? null : cursor.getBlob(offset + 3), // ObWave
            cursor.isNull(offset + 4) ? null : cursor.getBlob(offset + 4), // ObIntensity
            cursor.isNull(offset + 5) ? null : cursor.getBlob(offset + 5), // ObNoise
            cursor.isNull(offset + 6) ? null : cursor.getDouble(offset + 6), // SendWave
            cursor.isNull(offset + 7) ? null : cursor.getDouble(offset + 7), // HanLiang
            cursor.isNull(offset + 8) ? null : cursor.getDouble(offset + 8), // Rate
            cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9), // Type
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // SampleName
            cursor.isNull(offset + 11) ? null : cursor.getDouble(offset + 11), // MatchLimit
            cursor.isNull(offset + 12) ? null : cursor.getDouble(offset + 12), // MatchScale
            cursor.isNull(offset + 13) ? null : cursor.getDouble(offset + 13), // Confidence
            cursor.isNull(offset + 14) ? null : cursor.getInt(offset + 14), // Standard
            cursor.isNull(offset + 15) ? null : cursor.getInt(offset + 15), // StartWave
            cursor.isNull(offset + 16) ? null : cursor.getInt(offset + 16), // IntegratioTime
            cursor.isNull(offset + 17) ? null : cursor.getDouble(offset + 17), // LaserPower
            cursor.isNull(offset + 18) ? null : cursor.getInt(offset + 18), // AverageCount
            cursor.isNull(offset + 19) ? null : cursor.getInt(offset + 19), // SourceType
            cursor.isNull(offset + 20) ? null : cursor.getBlob(offset + 20), // DescriptionInformation
            cursor.isNull(offset + 21) ? null : cursor.getInt(offset + 21), // SpecType
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22), // Inspector
            cursor.isNull(offset + 23) ? null : InspectorTimeConverter.convertToEntityProperty(cursor.getString(offset + 23)), // InspectorTime
            cursor.isNull(offset + 24) ? null : cursor.getInt(offset + 24), // InspectorPass
            cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25), // PublisherMacAddress
            cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26), // Reviewer
            cursor.isNull(offset + 27) ? null : ReviewerTimeConverter.convertToEntityProperty(cursor.getString(offset + 27)), // ReviewerTime
            cursor.isNull(offset + 28) ? null : cursor.getInt(offset + 28), // ReviewerPass
            cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29) // ReviewerMacAddress
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, FtInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setMark(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setSampleTypeId(cursor.isNull(offset + 2) ? null : cursor.getInt(offset + 2));
        entity.setObWave(cursor.isNull(offset + 3) ? null : cursor.getBlob(offset + 3));
        entity.setObIntensity(cursor.isNull(offset + 4) ? null : cursor.getBlob(offset + 4));
        entity.setObNoise(cursor.isNull(offset + 5) ? null : cursor.getBlob(offset + 5));
        entity.setSendWave(cursor.isNull(offset + 6) ? null : cursor.getDouble(offset + 6));
        entity.setHanLiang(cursor.isNull(offset + 7) ? null : cursor.getDouble(offset + 7));
        entity.setRate(cursor.isNull(offset + 8) ? null : cursor.getDouble(offset + 8));
        entity.setType(cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9));
        entity.setSampleName(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setMatchLimit(cursor.isNull(offset + 11) ? null : cursor.getDouble(offset + 11));
        entity.setMatchScale(cursor.isNull(offset + 12) ? null : cursor.getDouble(offset + 12));
        entity.setConfidence(cursor.isNull(offset + 13) ? null : cursor.getDouble(offset + 13));
        entity.setStandard(cursor.isNull(offset + 14) ? null : cursor.getInt(offset + 14));
        entity.setStartWave(cursor.isNull(offset + 15) ? null : cursor.getInt(offset + 15));
        entity.setIntegratioTime(cursor.isNull(offset + 16) ? null : cursor.getInt(offset + 16));
        entity.setLaserPower(cursor.isNull(offset + 17) ? null : cursor.getDouble(offset + 17));
        entity.setAverageCount(cursor.isNull(offset + 18) ? null : cursor.getInt(offset + 18));
        entity.setSourceType(cursor.isNull(offset + 19) ? null : cursor.getInt(offset + 19));
        entity.setDescriptionInformation(cursor.isNull(offset + 20) ? null : cursor.getBlob(offset + 20));
        entity.setSpecType(cursor.isNull(offset + 21) ? null : cursor.getInt(offset + 21));
        entity.setInspector(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
        entity.setInspectorTime(cursor.isNull(offset + 23) ? null : InspectorTimeConverter.convertToEntityProperty(cursor.getString(offset + 23)));
        entity.setInspectorPass(cursor.isNull(offset + 24) ? null : cursor.getInt(offset + 24));
        entity.setPublisherMacAddress(cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25));
        entity.setReviewer(cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26));
        entity.setReviewerTime(cursor.isNull(offset + 27) ? null : ReviewerTimeConverter.convertToEntityProperty(cursor.getString(offset + 27)));
        entity.setReviewerPass(cursor.isNull(offset + 28) ? null : cursor.getInt(offset + 28));
        entity.setReviewerMacAddress(cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(FtInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(FtInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(FtInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
