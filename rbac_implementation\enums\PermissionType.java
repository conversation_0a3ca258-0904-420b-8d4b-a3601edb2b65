package com.ssraman.drugraman.rbac.enums;

/**
 * 权限类型枚举
 * 定义系统中的所有权限
 */
public enum PermissionType {
    
    // ========== 检测相关权限 ==========
    DETECTION_EXECUTE("DETECTION_EXECUTE", "执行检测", "DETECTION", "EXECUTE", "执行药物拉曼光谱检测"),
    DETECTION_VIEW_RESULT("DETECTION_VIEW_RESULT", "查看检测结果", "DETECTION", "VIEW", "查看检测结果"),
    DETECTION_EXPORT_BASIC("DETECTION_EXPORT_BASIC", "导出基础检测数据", "DETECTION", "EXPORT", "导出基础检测数据"),
    
    // ========== 报告相关权限 ==========
    REPORT_CREATE("REPORT_CREATE", "创建报告", "REPORT", "CREATE", "创建检测报告"),
    REPORT_PUBLISH("REPORT_PUBLISH", "发布报告", "REPORT", "PUBLISH", "发布检测报告"),
    REPORT_REVIEW("REPORT_REVIEW", "复审报告", "REPORT", "REVIEW", "复审已发布的报告"),
    REPORT_APPROVE("REPORT_APPROVE", "审批报告", "REPORT", "APPROVE", "审批报告"),
    REPORT_REJECT("REPORT_REJECT", "拒绝报告", "REPORT", "REJECT", "拒绝报告"),
    REPORT_SIGN("REPORT_SIGN", "报告签名", "REPORT", "SIGN", "报告签名"),
    
    // ========== 谱图相关权限 ==========
    SPECTRUM_CREATE("SPECTRUM_CREATE", "创建谱图", "SPECTRUM", "CREATE", "创建拉曼谱图"),
    SPECTRUM_EDIT("SPECTRUM_EDIT", "编辑谱图", "SPECTRUM", "EDIT", "编辑谱图数据"),
    SPECTRUM_DELETE("SPECTRUM_DELETE", "删除谱图", "SPECTRUM", "DELETE", "删除谱图数据"),
    SPECTRUM_IMPORT("SPECTRUM_IMPORT", "导入谱图", "SPECTRUM", "IMPORT", "导入标准谱图"),
    SPECTRUM_EXPORT("SPECTRUM_EXPORT", "导出谱图", "SPECTRUM", "EXPORT", "导出谱图数据"),
    LIBRARY_MANAGE("LIBRARY_MANAGE", "管理谱图库", "SPECTRUM", "MANAGE", "管理谱图库"),
    STANDARD_MAINTAIN("STANDARD_MAINTAIN", "维护标准谱图", "SPECTRUM", "MAINTAIN", "维护标准谱图数据"),
    CALIBRATION_MANAGE("CALIBRATION_MANAGE", "管理校准数据", "SPECTRUM", "CALIBRATE", "管理校准数据"),
    
    // ========== 用户相关权限 ==========
    USER_CREATE("USER_CREATE", "创建用户", "USER", "CREATE", "创建用户账号"),
    USER_EDIT("USER_EDIT", "编辑用户", "USER", "EDIT", "编辑用户信息"),
    USER_DELETE("USER_DELETE", "删除用户", "USER", "DELETE", "删除用户账号"),
    USER_VIEW("USER_VIEW", "查看用户", "USER", "VIEW", "查看用户列表"),
    USER_SEARCH("USER_SEARCH", "搜索用户", "USER", "SEARCH", "搜索用户"),
    ROLE_ASSIGN("ROLE_ASSIGN", "分配角色", "USER", "ASSIGN_ROLE", "分配角色权限"),
    ROLE_REVOKE("ROLE_REVOKE", "撤销角色", "USER", "REVOKE_ROLE", "撤销角色权限"),
    USER_STATUS_MANAGE("USER_STATUS_MANAGE", "管理用户状态", "USER", "MANAGE_STATUS", "管理用户状态（启用/禁用）"),
    PASSWORD_RESET("PASSWORD_RESET", "重置密码", "USER", "RESET_PASSWORD", "重置用户密码"),
    
    // ========== 系统相关权限 ==========
    SYSTEM_CONFIG("SYSTEM_CONFIG", "系统配置", "SYSTEM", "CONFIG", "系统配置管理"),
    SYSTEM_BACKUP("SYSTEM_BACKUP", "系统备份", "SYSTEM", "BACKUP", "系统备份"),
    SYSTEM_RESTORE("SYSTEM_RESTORE", "系统恢复", "SYSTEM", "RESTORE", "系统恢复"),
    LOG_VIEW("LOG_VIEW", "查看日志", "SYSTEM", "VIEW_LOG", "查看系统日志"),
    LOG_EXPORT("LOG_EXPORT", "导出日志", "SYSTEM", "EXPORT_LOG", "导出日志"),
    AUDIT_VIEW("AUDIT_VIEW", "查看审计日志", "SYSTEM", "VIEW_AUDIT", "查看审计日志"),
    PERMISSION_MANAGE("PERMISSION_MANAGE", "权限管理", "SYSTEM", "MANAGE_PERMISSION", "权限管理"),
    ROLE_MANAGE("ROLE_MANAGE", "角色管理", "SYSTEM", "MANAGE_ROLE", "角色管理"),
    DATABASE_MANAGE("DATABASE_MANAGE", "数据库管理", "SYSTEM", "MANAGE_DATABASE", "数据库管理"),
    SYSTEM_MONITOR("SYSTEM_MONITOR", "系统监控", "SYSTEM", "MONITOR", "系统监控"),
    
    // ========== 档案相关权限 ==========
    ARCHIVE_VIEW("ARCHIVE_VIEW", "查看档案", "ARCHIVE", "VIEW", "查看档案记录"),
    ARCHIVE_SEARCH("ARCHIVE_SEARCH", "搜索档案", "ARCHIVE", "SEARCH", "搜索档案记录"),
    ARCHIVE_EXPORT("ARCHIVE_EXPORT", "导出档案", "ARCHIVE", "EXPORT", "导出档案");
    
    private final String code;
    private final String name;
    private final String resource;
    private final String action;
    private final String description;
    
    PermissionType(String code, String name, String resource, String action, String description) {
        this.code = code;
        this.name = name;
        this.resource = resource;
        this.action = action;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getResource() {
        return resource;
    }
    
    public String getAction() {
        return action;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取完整的权限字符串 (resource:action)
     */
    public String getFullPermissionString() {
        return resource + ":" + action;
    }
    
    /**
     * 根据代码获取权限类型
     */
    public static PermissionType fromCode(String code) {
        for (PermissionType permissionType : values()) {
            if (permissionType.getCode().equals(code)) {
                return permissionType;
            }
        }
        throw new IllegalArgumentException("未知的权限代码: " + code);
    }
    
    /**
     * 根据资源和操作获取权限类型
     */
    public static PermissionType fromResourceAndAction(String resource, String action) {
        for (PermissionType permissionType : values()) {
            if (permissionType.getResource().equals(resource) && permissionType.getAction().equals(action)) {
                return permissionType;
            }
        }
        throw new IllegalArgumentException("未知的权限组合: " + resource + ":" + action);
    }
    
    /**
     * 获取指定资源的所有权限
     */
    public static PermissionType[] getPermissionsByResource(String resource) {
        return java.util.Arrays.stream(values())
                .filter(p -> p.getResource().equals(resource))
                .toArray(PermissionType[]::new);
    }
    
    /**
     * 检查权限代码是否有效
     */
    public static boolean isValidCode(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 检查资源和操作组合是否有效
     */
    public static boolean isValidResourceAction(String resource, String action) {
        try {
            fromResourceAndAction(resource, action);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 获取所有权限代码
     */
    public static String[] getAllCodes() {
        PermissionType[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有资源类型
     */
    public static String[] getAllResources() {
        return java.util.Arrays.stream(values())
                .map(PermissionType::getResource)
                .distinct()
                .toArray(String[]::new);
    }
}
