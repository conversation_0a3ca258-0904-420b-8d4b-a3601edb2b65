<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="mpresenter"
            type="com.ssraman.drugraman.ui.user.ChangSelfPwdFragment.Presenter" />
    </data>

    <FrameLayout
        android:id="@+id/layout_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent"
        tools:context=".ui.user.ChangSelfPwdFragment">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="280dp"
            android:layout_gravity="center">

            <androidx.cardview.widget.CardView
                android:layout_width="330dp"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                app:cardCornerRadius="7dp"
                app:cardElevation="22dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|center"
                    android:layout_marginTop="15dp"
                    android:text="@string/title_reset_password"
                    android:textAllCaps="true"
                    android:textSize="22sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|bottom"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="120dp"
                            android:layout_height="32dp"
                            android:gravity="right|center"
                            android:text="@string/title_enter_old_password"
                            android:textSize="14sp" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputLayout1"
                            android:layout_width="180dp"
                            android:layout_height="40dp"
                            android:layout_marginStart="7dp"
                            android:layout_marginEnd="9dp"
                            android:hint="@string/login_password_hint"
                            android:textColorHint="@color/gray"
                            app:hintTextAppearance="@style/TextAppearence.App.TextInputLayout"
                            app:passwordToggleEnabled="true">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etPassword_old"
                                style="@style/TextStyle"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/input_border_bottom"
                                android:cursorVisible="true"
                                android:gravity="center|left|bottom"
                                android:inputType="textPassword"
                                android:maxLength="50"
                                android:paddingBottom="1dp"
                                android:textColor="@color/black_effective"
                                android:textSize="16sp" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="120dp"
                            android:layout_height="32dp"
                            android:gravity="right"
                            android:text="@string/title_enter_new_password"
                            android:textSize="14sp" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputLayout2"
                            android:layout_width="180dp"
                            android:layout_height="40dp"
                            android:layout_marginStart="7dp"
                            android:layout_marginEnd="9dp"
                            android:hint="@string/login_password_hint"
                            android:textColorHint="@color/gray"
                            app:hintTextAppearance="@style/TextAppearence.App.TextInputLayout"
                            app:passwordToggleEnabled="true">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etPassword_new"
                                style="@style/TextStyle"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/input_border_bottom"
                                android:cursorVisible="true"
                                android:gravity="center|left|bottom"
                                android:inputType="textPassword"
                                android:maxLength="50"
                                android:paddingBottom="1dp"
                                android:textColor="@color/black_effective"
                                android:textSize="16sp" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="120dp"
                            android:layout_height="32dp"
                            android:gravity="right"
                            android:text="@string/title_confirm_new_password"
                            android:textSize="14sp" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputLayout3"
                            android:layout_width="180dp"
                            android:layout_height="40dp"
                            android:layout_marginStart="7dp"
                            android:layout_marginEnd="9dp"
                            android:layout_marginBottom="30dp"
                            android:hint="@string/login_password_hint"
                            android:textColorHint="@color/gray"
                            app:hintTextAppearance="@style/TextAppearence.App.TextInputLayout"
                            app:passwordToggleEnabled="true">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etPassword_confirm"
                                style="@style/TextStyle"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/input_border_bottom"
                                android:cursorVisible="true"
                                android:gravity="center|left|bottom"
                                android:inputType="textPassword"
                                android:maxLength="50"
                                android:paddingBottom="1dp"
                                android:textColor="@color/black_effective"
                                android:textSize="16sp" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/underline1"
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:layout_marginTop="20dp"
                        android:background="@color/dialogSplitIOSLight" />

                    <LinearLayout
                        android:id="@+id/box_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/btn_selectNegative"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:layout_weight="1"
                            android:background="@drawable/button_dialog_ios_left_light"
                            android:gravity="center"
                            android:onClick="@{(view)->mpresenter.CancelClick(view)}"
                            android:text="@string/btn_cancel"
                            android:textColor="@color/dialogButtonIOSNormal"
                            android:textSize="18sp" />


                        <ImageView
                            android:id="@+id/split_vertical2"
                            android:layout_width="1px"
                            android:layout_height="match_parent"
                            android:background="@color/dialogSplitIOSLight" />

                        <TextView
                            android:id="@+id/btn_selectPositive"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:layout_weight="1"
                            android:background="@drawable/button_dialog_ios_right_light"
                            android:gravity="center"
                            android:onClick="@{(view)->mpresenter.ConfirmClick(view)}"
                            android:text="@string/btn_confirm"
                            android:textColor="@color/dialogButtonIOSNormal"
                            android:textSize="18sp" />
                    </LinearLayout>
                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </RelativeLayout>

    </FrameLayout>
</layout>