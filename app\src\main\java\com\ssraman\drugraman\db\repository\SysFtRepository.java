package com.ssraman.drugraman.db.repository;

import android.database.Cursor;

import com.ssraman.drugraman.db.entity.FtInfo;
import com.ssraman.drugraman.db.entity.SelectPeakInfo;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.gen.FtInfoDao;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Completable;

/**
 * @author: Administrator
 * @date: 2021/6/21
 */
public class SysFtRepository {
    private DaoSession mDaoSession;
    private FtInfoDao ftDao;

    public SysFtRepository(DaoSession daoSession) {
        this.mDaoSession = daoSession;
        ftDao = this.mDaoSession.getFtInfoDao();
    }

    public Completable insertFt(FtInfo ftInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                ftDao.insert(ftInfo);
                return null;
            }
        });
    }

    public long insertFtByRe(FtInfo ftInfo) {
        return ftDao.insert(ftInfo);
    }

    public Completable deleteFt(FtInfo ftInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                ftDao.delete(ftInfo);
                return null;
            }
        });
    }

    public Completable updateFt(FtInfo ftInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                ftDao.update(ftInfo);
                return null;
            }
        });
    }

    public void updateFtByRe(FtInfo ftInfo) {
        ftDao.update(ftInfo);
    }

    public List<FtInfo> getFtListBySampleTypeId(Long id) {
        return ftDao.queryBuilder().where(FtInfoDao.Properties.SampleTypeId.eq(id)).list();
    }

    public FtInfo getFtInfoById(long ft_id) {
        List<FtInfo> list = ftDao.queryBuilder().where(FtInfoDao.Properties.Id.eq(ft_id)).list();
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    public List<FtInfo> getFtListById(long ft_id) {
        List<FtInfo> list = ftDao.queryBuilder().where(FtInfoDao.Properties.Id.eq(ft_id)).list();
        return list;
    }

    public List<SelectPeakInfo> getSelectPeakListByWave(double[] wave, long parentId) {
        List<SelectPeakInfo> list = new ArrayList<>();
        if (wave.length <= 0) {
            return list;
        }
        try {
            String strSql = "select a.[MatchNum],a.[MustMatchNum],a.[Wave],b.[FtId],b.[PeakCount],b.[MustNum],b.[PeakMatch],b.[SampleName],b.[SampleTypeId] "
                    +
                    "from (SELECT count(Peak.[FtId]) as MatchNum,sum(Peak.[Must]) as MustMatchNum ,Peak.[FtId],Peak.[Wave],Ft.[SampleTypeId] FROM Peak,Ft,SampleType "
                    +
                    "where %%classify_sql%% Ft.[SampleTypeId]==SampleType.[Id] and Peak.[FtId]==Ft.[Id] and (%%peak_match_sql%%) GROUP BY Peak.[FtId] ) a "
                    +
                    "left join (Select e.[FtId],e.MustNum,e.PeakCount, e.[iMatchNum],Ft.[MatchScale],Ft.[SampleName],Ft.[SampleTypeId],e.PeakCount*Ft.[MatchScale] as PeakMatch "
                    +
                    "from ( Select c.[FtId], c.[iMatchNum],d.PeakCount,d.MustNum from (SELECT Peak.[FtId],count(Peak.[FtId]) as iMatchNum,sum(Peak.[Must]) as iMustMatchNum FROM Peak "
                    +
                    "where (%%peak_match_sql2%%) group by Peak.[FtId]) c ,(select Peak.[FtId], count(Peak.[FtId]) as PeakCount,sum(Peak.[Must]) as MustNum  FROM Peak group by Peak.[FtId]) d where "
                    +
                    " d.[FtId]==c.FtId and c.[iMatchNum]>=(d.PeakCount*0.4)  and (c.iMustMatchNum == d.MustNum  or (d.MustNum is not null))) e,Ft "
                    +
                    "  where e.[FtId]==Ft.[Id]) b on a.[FtId]==b.[FtId] and a.[MatchNum]>=b.[PeakMatch] " +
                    "where a.MatchNum>=1 and (a.MustMatchNum == b.MustNum  or (b.MustNum is not null))";

            StringBuilder classify_sql = new StringBuilder();
            if (parentId == 0) {
                classify_sql.append(" ");
            } else {
                classify_sql.append("SampleType.[ParentId]==" + parentId + " and ");
            }

            StringBuilder peak_match_sql = new StringBuilder();
            peak_match_sql.append("abs(Peak.[Wave]-'" + wave[0] + "')<Peak.[MatchLimit] ");
            for (int i = 1; i < wave.length; i++) {
                peak_match_sql.append(" or abs(Peak.[Wave]-'" + wave[i] + "')<Peak.[MatchLimit]");
            }

            strSql = strSql.replace("%%classify_sql%%", classify_sql.toString());
            strSql = strSql.replace("%%peak_match_sql%%", peak_match_sql.toString());
            strSql = strSql.replace("%%peak_match_sql2%%", peak_match_sql.toString());

            Cursor c = mDaoSession.getDatabase().rawQuery(strSql.toString(), null);
            if (c.moveToFirst()) {
                do {
                    SelectPeakInfo selectPeakInfo = new SelectPeakInfo();
                    selectPeakInfo.setMatchNum(c.getInt(0));
                    selectPeakInfo.setMustMatchNum(c.getInt(1));
                    selectPeakInfo.setWave(c.getDouble(2));
                    selectPeakInfo.setFtId(c.getInt(3));
                    selectPeakInfo.setPeakCount(c.getInt(4));
                    selectPeakInfo.setMustNum(c.getInt(5));
                    selectPeakInfo.setPeakMatch(c.getDouble(6));
                    selectPeakInfo.setSampleName(c.getString(7));
                    selectPeakInfo.setSampleTypeId(c.getInt(8));
                    list.add(selectPeakInfo);
                } while (c.moveToNext());

            }
            c.close();
            Collections.sort(list);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return list;
    }

    public List<SelectPeakInfo> getSelectPeakListByItemId(double[] wave, long itemId) {
        List<SelectPeakInfo> list = new ArrayList<>();
        if (wave.length <= 0) {
            return list;
        }
        try {
            String strSql = "select a.[MatchNum],a.[MustMatchNum],a.[Wave],b.[FtId],b.[PeakCount],b.[MustNum],b.[PeakMatch],b.[SampleName],b.[SampleTypeId] "
                    +
                    "from (SELECT count(Peak.[FtId]) as MatchNum,sum(Peak.[Must]) as MustMatchNum ,Peak.[FtId],Peak.[Wave],Ft.[SampleTypeId] FROM Peak,Ft,SampleType "
                    +
                    "where %%item_id_sql%% Ft.[SampleTypeId]==SampleType.[Id] and Peak.[FtId]==Ft.[Id] and (%%peak_match_sql%%) GROUP BY Peak.[FtId] ) a "
                    +
                    "left join (Select e.[FtId],e.MustNum,e.PeakCount, e.[iMatchNum],Ft.[MatchScale],Ft.[SampleName],Ft.[SampleTypeId],e.PeakCount*Ft.[MatchScale] as PeakMatch "
                    +
                    "from ( Select c.[FtId], c.[iMatchNum],d.PeakCount,d.MustNum from (SELECT Peak.[FtId],count(Peak.[FtId]) as iMatchNum,sum(Peak.[Must]) as iMustMatchNum FROM Peak "
                    +
                    "where (%%peak_match_sql2%%) group by Peak.[FtId]) c ,(select Peak.[FtId], count(Peak.[FtId]) as PeakCount,sum(Peak.[Must]) as MustNum  FROM Peak group by Peak.[FtId]) d where "
                    +
                    " d.[FtId]==c.FtId and c.[iMatchNum]>=(d.PeakCount*0.4)  and (c.iMustMatchNum == d.MustNum  or (d.MustNum is not null))) e,Ft "
                    +
                    "  where e.[FtId]==Ft.[Id]) b on a.[FtId]==b.[FtId] and a.[MatchNum]>=b.[PeakMatch] " +
                    "where a.MatchNum>=1 and (a.MustMatchNum == b.MustNum  or (b.MustNum is not null))";

            // 对于Item类型，直接匹配SampleType.Id
            StringBuilder item_id_sql = new StringBuilder();
            if (itemId == 0) {
                item_id_sql.append(" ");
            } else {
                item_id_sql.append("SampleType.[Id]==" + itemId + " and ");
            }

            StringBuilder peak_match_sql = new StringBuilder();
            peak_match_sql.append("abs(Peak.[Wave]-'" + wave[0] + "')<Peak.[MatchLimit] ");
            for (int i = 1; i < wave.length; i++) {
                peak_match_sql.append(" or abs(Peak.[Wave]-'" + wave[i] + "')<Peak.[MatchLimit]");
            }

            strSql = strSql.replace("%%item_id_sql%%", item_id_sql.toString());
            strSql = strSql.replace("%%peak_match_sql%%", peak_match_sql.toString());
            strSql = strSql.replace("%%peak_match_sql2%%", peak_match_sql.toString());

            Cursor c = mDaoSession.getDatabase().rawQuery(strSql.toString(), null);
            if (c.moveToFirst()) {
                do {
                    SelectPeakInfo selectPeakInfo = new SelectPeakInfo();
                    selectPeakInfo.setMatchNum(c.getInt(0));
                    selectPeakInfo.setMustMatchNum(c.getInt(1));
                    selectPeakInfo.setWave(c.getDouble(2));
                    selectPeakInfo.setFtId(c.getInt(3));
                    selectPeakInfo.setPeakCount(c.getInt(4));
                    selectPeakInfo.setMustNum(c.getInt(5));
                    selectPeakInfo.setPeakMatch(c.getDouble(6));
                    selectPeakInfo.setSampleName(c.getString(7));
                    selectPeakInfo.setSampleTypeId(c.getInt(8));
                    list.add(selectPeakInfo);
                } while (c.moveToNext());

            }
            c.close();
            Collections.sort(list);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return list;
    }

    public List<FtInfo> getFtListBySampleTypeId_lib(long id) {
        return ftDao.queryBuilder().where(FtInfoDao.Properties.SampleTypeId.eq(id), FtInfoDao.Properties.Type.eq(1))
                .list();
    }

    public void deleteFtByRe(FtInfo ftInfo) {
        ftDao.delete(ftInfo);
    }

}
