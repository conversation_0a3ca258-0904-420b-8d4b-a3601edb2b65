apply plugin: 'com.github.dcendents.android-maven'
apply plugin: 'com.jfrog.bintray'

version = rootProject.ext.versionName_okgo      // 数据仓库依赖第三部分

def siteUrl = 'https://github.com/jeasonlzy/OkGo'
def gitUrl = 'https://github.com/jeasonlzy/OkGo.git'
group = "com.lzy.net"       // 数据仓库依赖第一部分

install {
    repositories.mavenInstaller {
        pom {
            project {
                packaging 'aar'
                name 'OkGo For Android'     // 项目描述
                url siteUrl
                licenses {
                    license {
                        name 'The Apache Software License, Version 2.0'
                        url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                    }
                }
                developers {
                    developer {
                        id 'jeasonlzy'                // 开发者信息
                        name 'LiaoZiYao'              // 开发者信息
                        email '<EMAIL>'    // 开发者信息
                    }
                }
                scm {
                    connection gitUrl
                    developerConnection gitUrl
                    url siteUrl
                }
            }
        }
    }
}

task sourcesJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    classifier = 'sources'
}

task javadoc(type: Javadoc) {
    source = android.sourceSets.main.java.srcDirs
    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
}

task javadocJar(type: Jar, dependsOn: javadoc) {
    classifier = 'javadoc'
    from javadoc.destinationDir
}

javadoc {
    options {
        encoding "UTF-8"
        charSet 'UTF-8'
        author true
        version true
        links "http://docs.oracle.com/javase/7/docs/api"
        title 'OkGo For Android'   // 文档标题
    }
}

artifacts {
//    archives javadocJar
    archives sourcesJar
}

// 生成jar包
task releaseJar(type: Copy) {
    from( 'build/intermediates/bundles/default')
    into( '../jar')
    include('classes.jar')
    rename('classes.jar', 'okgo-' + version + '.jar')
}

Properties properties = new Properties()
properties.load(project.rootProject.file('local.properties').newDataInputStream())
bintray {
    user = properties.getProperty("bintray.user")
    key = properties.getProperty("bintray.apikey")
    configurations = ['archives']
    pkg {
        repo = "maven"
        name = "okgo"             // 数据仓库依赖第二部分
        websiteUrl = siteUrl
        vcsUrl = gitUrl
        licenses = ["Apache-2.0"]
        publish = true
    }
}
