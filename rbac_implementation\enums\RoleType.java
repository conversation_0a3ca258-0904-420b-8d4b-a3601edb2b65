package com.ssraman.drugraman.rbac.enums;

/**
 * 角色类型枚举
 * 定义系统中的5种标准角色
 */
public enum RoleType {
    
    /**
     * 操作员 - 仅可执行检测操作
     */
    OPERATOR("OPERATOR", "操作员", 1, "仅可执行药物拉曼光谱检测和查看检测结果"),
    
    /**
     * 审核员 - 检测 + 报告管理
     */
    REVIEWER("REVIEWER", "审核员", 2, "执行检测、发布检测报告、复审已发布的报告"),
    
    /**
     * 谱图管理员 - 检测 + 谱图库管理
     */
    SPECTRUM_MANAGER("SPECTRUM_MANAGER", "谱图管理员", 3, "执行检测、创建和管理拉曼谱图库、维护标准谱图数据"),
    
    /**
     * 用户管理员 - 仅用户账号管理
     */
    USER_ADMIN("USER_ADMIN", "用户管理员", 4, "创建、分配、管理用户账号，分配角色权限"),
    
    /**
     * 系统管理员 - 所有系统权限
     */
    SYSTEM_ADMIN("SYSTEM_ADMIN", "系统管理员", 5, "完整的系统管理权限，包括以上所有功能");
    
    private final String code;
    private final String name;
    private final int level;
    private final String description;
    
    RoleType(String code, String name, int level, String description) {
        this.code = code;
        this.name = name;
        this.level = level;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public int getLevel() {
        return level;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取角色类型
     */
    public static RoleType fromCode(String code) {
        for (RoleType roleType : values()) {
            if (roleType.getCode().equals(code)) {
                return roleType;
            }
        }
        throw new IllegalArgumentException("未知的角色代码: " + code);
    }
    
    /**
     * 根据级别获取角色类型
     */
    public static RoleType fromLevel(int level) {
        for (RoleType roleType : values()) {
            if (roleType.getLevel() == level) {
                return roleType;
            }
        }
        throw new IllegalArgumentException("未知的角色级别: " + level);
    }
    
    /**
     * 检查是否有更高的权限级别
     */
    public boolean hasHigherLevelThan(RoleType other) {
        return this.level > other.level;
    }
    
    /**
     * 检查是否有更低的权限级别
     */
    public boolean hasLowerLevelThan(RoleType other) {
        return this.level < other.level;
    }
    
    /**
     * 检查是否有相同的权限级别
     */
    public boolean hasSameLevelAs(RoleType other) {
        return this.level == other.level;
    }
    
    /**
     * 获取所有角色代码
     */
    public static String[] getAllCodes() {
        RoleType[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }
    
    /**
     * 获取所有角色名称
     */
    public static String[] getAllNames() {
        RoleType[] values = values();
        String[] names = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            names[i] = values[i].getName();
        }
        return names;
    }
    
    /**
     * 检查角色代码是否有效
     */
    public static boolean isValidCode(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 检查角色级别是否有效
     */
    public static boolean isValidLevel(int level) {
        try {
            fromLevel(level);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
