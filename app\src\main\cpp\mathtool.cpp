#include <stdlib.h>
#include "mathtool.h"
#include "malloc.h"
#include "string.h"

#define STRHEAD 300 
#define STRHEADSOFT 500 
#define swap(a,b)  {a+=b; b=a-b; a=a-b;}
char cpy[] = "NJJZYQSBYXGSBJFGS\0";

//�޶���2019/10/21���ӿ���������

/***************************************
��������gauss_solve
���أ��������ľ���ֵ
������num ������
˵������������ֵ�ľ���ֵ
�޶���
***************************************/

void __gauss_solve(int n, double* A, double* x, double* b)
{
	int i, j, k, r;
	double max;
	for (k = 0; k<n - 1; k++)
	{
		max = my_fabs(A[k*n + k]); /*find maxmum*/
		r = k;
		for (i = k + 1; i<n - 1; i++)
			if (max<my_fabs(A[i*n + i]))
			{
				max = my_fabs(A[i*n + i]);
				r = i;
			}
		if (r != k)
			for (i = 0; i<n; i++) /*change array:A[k]&A[r] */
			{
				max = A[k*n + i];
				A[k*n + i] = A[r*n + i];
				A[r*n + i] = max;
			}
		max = b[k]; /*change array:b[k]&b[r] */
		b[k] = b[r];
		b[r] = max;
		for (i = k + 1; i<n; i++)
		{
			for (j = k + 1; j<n; j++)
				A[i*n + j] -= A[i*n + k] * A[k*n + j] / A[k*n + k];
			b[i] -= A[i*n + k] * b[k] / A[k*n + k];
		}
	}
	for (i = n - 1; i >= 0; x[i] /= A[i*n + i], i--)
		for (j = i + 1, x[i] = b[i]; j<n; j++)
			x[i] -= A[i*n + j] * x[j];
}
/***************************************
��������QuickSort
���أ��ź����a��b
������a�� double*���ͣ� ��Ҫ����������ָ��
������h�� int���ͣ� ��Ҫ��������ݵ���߽�
������t�� int���ͣ� ��Ҫ��������ݵ��ұ߽�
������b�� double*���ͣ����������ָ�룬b������������a��Ĭ�Ͽ�ָ�룬�������򲻴���
˵��������
***************************************/
void quickSort(double *a, int h, int t, int* b = NULL) {
	if (h >= t)
		return;
	int mid = (h + t) / 2, i = h, j = t;
	double x;
	x = a[mid];
	while (1) {
		while (a[i] > x)
			i++;
		while (a[j] < x)
			j--;
		if (i >= j)
			break;
		swap(a[i], a[j]);
		if (b) {
			swap(b[i], b[j]);
		}
	}
	if (b) {
		quickSort(a, h, j - 1, b);
		quickSort(a, j + 1, t, b);
	}
	else {
		quickSort(a, h, j - 1);
		quickSort(a, j + 1, t);
	}
	return;
}

/***************************************
��������polyfit
���أ���Ͻ��
������n���ݳ���
������x��y���ݺ�Ŀ������
������poly_n ����
˵�������y=a0+a1*x+a2*x^2+����+apoly_n*x^poly_n
�޶���
***************************************/

void polyfit(int n, double* x, double* y, int poly_n, CALICOEF* result)
{  // CALICOEF coef;

	int i, j, k;
	double *tempx, *tempy, *sumxx, *sumxy, *ata, *a = result->pCoef;
	double offset_before = 0, offset_after = 0, temp = 1, temp1 = 0;
	double y_average = 0;

	result->idx = poly_n;

	for (i = 0; i<n; i++)
		y_average += y[i];
	y_average = y_average / n;
	tempx = (double*)malloc(n * sizeof(double));
	sumxx = (double*)malloc((poly_n * 2 + 1) * sizeof(double));
	tempy = (double*)malloc(n * sizeof(double));
	sumxy = (double*)malloc((poly_n + 1) * sizeof(double));
	ata = (double*)malloc((poly_n + 1)*(poly_n + 1) * sizeof(double));
	for (i = 0; i<n; i++)
	{
		tempx[i] = 1;
		tempy[i] = y[i];
	}
	for (i = 0; i<2 * poly_n + 1; i++)
		for (sumxx[i] = 0, j = 0; j<n; j++)
		{
			sumxx[i] += tempx[j];
			tempx[j] *= x[j];
		}
	for (i = 0; i<poly_n + 1; i++)
		for (sumxy[i] = 0, j = 0; j<n; j++)
		{
			sumxy[i] += tempy[j];
			tempy[j] *= x[j];
		}
	for (i = 0; i<poly_n + 1; i++)
		for (j = 0; j<poly_n + 1; j++)
			ata[i*(poly_n + 1) + j] = sumxx[i + j];
	__gauss_solve(poly_n + 1, ata, a, sumxy);
	for (i = 0; i<n; i++) {
		offset_before += (y[i] - y_average)*(y[i] - y_average);
		temp1 = 0;
		for (j = 0; j<poly_n; j++) {
			temp = 1;
			for (k = poly_n; k>j; k--) {
				temp *= x[i];
			}
			temp1 += temp * a[3 - j];
		}
		offset_after += (temp1 - y[i])*(temp1 - y[i]);
	}

	free(tempx);
	free(sumxx);
	free(tempy);
	free(sumxy);
	free(ata);
	result->idx = poly_n + 1;
	result->goodness = (1 - offset_after / offset_before);


	return;

}
/***************************************
��������my_fabs
���أ��������ľ���ֵ
������num ������
˵������������ֵ�ľ���ֵ
�޶���
***************************************/
double my_fabs(double num)
{
	if (num > 0)
		return num;
	else
		return 0 - num;

}
/***************************************
��������encode
���أ�1
������spBuff��16λ���ף�������
������len���ݳ���
˵������16Ϊ�޷������ݱ���
�޶���
***************************************/
int encode(unsigned short * spBuff, unsigned int len)
{
	unsigned int k;
	unsigned char j;
	unsigned short *spec16 = spBuff;

	srand((unsigned)time(NULL));
	k = rand() & 0xf;
	spec16[STRHEAD] = (spec16[STRHEAD] & 0xfff0) + k;
	k = STRHEAD + k;
	for (j = 0; j < strlen(&(cpy[0])); j++)
	{
		k += cpy[j];
		if (k >= len)
			return 1;
		spec16[k] = (spec16[k] & 0xfff0) + (cpy[j] & 0x0f);
	}
	return 0;
}
/***************************************
��������decode
���أ�1�ɹ���0ʧ��
������spBuff��16λ���ף�������
������len���ݳ���
˵������16Ϊ�޷���������֤
�޶���
***************************************/
int decode(unsigned short * spBuff, unsigned int len)
{
	unsigned int k = 0, i;
	unsigned short *spec16 = spBuff;

	k = STRHEAD + (spec16[STRHEAD] & 0xf) + cpy[0];

	for (i = 0; i < strlen(cpy); i++)
	{
		if (k >= len)
			break;
		if ((spec16[k] & 0xf) == (cpy[i] & 0xf))
		{
			if (i == strlen(cpy))
				continue;
			k += cpy[i + 1];
		}
		else
			return 0;
	}
	return 1;
}
/***************************************
��������xcode
���أ�1
������spBuff��16λ���ף�������
������opBuff ��double����������
������len���ݳ���
˵����
�޶���
***************************************/
int xcode(double * spBuff, double * opBuff, unsigned int len)
{
	unsigned int k;
	unsigned char j;
	double *spec16 = spBuff;
	unsigned int xk;
	srand((unsigned)time(NULL));
	xk = rand() / 15;
	k = xk & 0xf;
	opBuff[STRHEAD] = spec16[STRHEAD] + k;
	k = STRHEAD + k;
	for (j = 0; j < strlen(&(cpy[0])); j++)
	{
		k += cpy[j];
		if (k >= len)
			return 1;
		opBuff[k] = spec16[k] + xk;
	}
	return 0;
}
/***************************************
��������excode
���أ�1
������spBuff��16λ���ף�������
������len���ݳ���
˵������������
�޶���
***************************************/
void excode(unsigned short * spBuff, unsigned int len)
{
	unsigned int k = 0, i;
	unsigned short *spec16 = spBuff;

	k = STRHEAD + (spec16[STRHEAD] & 0xf) + cpy[0];
	spec16[STRHEAD] = spec16[STRHEAD] & 0xfff0;
	for (i = 0; i < strlen(cpy); i++)
	{
		spec16[k] = spec16[k] & 0xfff0;
	}
}
/***************************************
��������encodeSoft
���أ�1
������spBuff��16λ���ף�������
������len���ݳ���
˵����double���ݱ���
�޶���
***************************************/
int encodeSoft(double * spBuff, unsigned int len)
{
	unsigned int k;
	unsigned char j;
	double *spec16 = spBuff;

	srand((unsigned)time(NULL));
	k = rand() & 0xf;
	if (spec16[STRHEADSOFT]>0)
		spec16[STRHEADSOFT] = ((long)(spec16[STRHEADSOFT] / 16) * 16) + k;
	else {
		spec16[STRHEADSOFT] = (double)k;
	}
	k = STRHEADSOFT + k;
	for (j = 0; j < strlen(&(cpy[0])); j++)
	{
		k += cpy[j];
		if (k >= len)
			return 1;
		if (spec16[k] > 0)
			spec16[k] = ((long)(spec16[k] / 16) * 16) + (cpy[j] & 0x0f);
		else
			spec16[k] = (double)(cpy[j] & 0x0f);
	}
	return 0;
}
/***************************************
��������decodeSoft
���أ�1
������spBuff��double��������
������len���ݳ���
˵��������У��
�޶���
***************************************/
int decodeSoft(double * spBuff, unsigned int len)
{
	unsigned int k = 0, i;
	double *spec16 = spBuff;

	k = STRHEADSOFT + ((long)spec16[STRHEADSOFT] % 16) + cpy[0];
	//k = STRHEADSOFT+ (spec16[STRHEADSOFT]%16)+cpy[0];
	for (i = 0; i < strlen(cpy); i++)
	{
		if (k >= len)
			break;

		if (((long)spec16[k] & 0xf) == (cpy[i] & 0xf))
		{
			if (i == strlen(cpy))
				continue;
			k += cpy[i + 1];
		}
		else
			return 0;
	}
	return 1;
}
/***************************************
��������convertdata
���أ�1
������spBuff��unsigned short��������
������ double *pIntensity���
������len���ݳ���
������xs ���Ų���
˵����������תΪdouble
�޶���
***************************************/
int convertdata(unsigned short * spBuff, double *pIntensity, int len, int xs)
{
	unsigned short *spec16 = spBuff;
	int i;
	if (decode(spec16, len) == 0)
		return 0;
	excode(spec16, len);
	if (xs == 1)
	{
		for (i = 0; i < len; i++)
		{
			pIntensity[i] = spec16[i];
		}
	}
	else
	{
		for (i = 0; i < len; i++)
		{
			pIntensity[i] = spec16[i];
			pIntensity[i] = (double)(pIntensity[i] * 65535) / xs;
		}
	}
	encodeSoft(pIntensity, len);
	return 1;
}

/***************************************
������:correlation
���أ�1
������spBuff��unsigned short��������
������ double *pIntensity���
������len���ݳ���
������xs ���Ų���
˵�����������ϵ��
�޶���
***************************************/
double correlation(double* x, double* curl1, double* curl2, int len, double lowline, double topline) {
	double re = 0, sum1 = 0, sum2 = 0, max1 = 0, max2 = 0;

	int start = 0;
	for (int i = 0; i <len; i++) {
		if (x[i] < lowline) {
			continue;
		}
		sum1 += curl1[i];
		re += curl1[i] * curl2[i];
		sum2 += curl2[i];
		max1 += curl1[i] * curl1[i];
		max2 += curl2[i] * curl2[i];
		start += 1;
		if (x[i] > topline) {
			break;
		}

	}
	re = start * re - sum1 * sum2;

	sum1 = sqrt(start * max1 - sum1 * sum1);
	sum2 = sqrt(start*max2 - sum2 * sum2);
	return re / (sum1*sum2);
}

/***************************************
������:correlation2
���أ�1
������spBuff��unsigned short��������
������ double *pIntensity���
������len���ݳ���
������xs ���Ų���
˵�����������ϵ��,�����ǵײ���(1��2���鵹��)
�޶���
***************************************/
double correlation_r(double* curl1, double* curl2, int len) {
	double re = 0, sum1 = 0, sum2 = 0, max1 = 0, max2 = 0;

	int start = 0;
	for (int i = 0; i <len; i++) {		
		sum1 += curl1[i];
		re += curl1[i] * curl2[len-1-i];
		//re += curl1[i] * curl2[i];
		sum2 += curl2[i];
		max1 += curl1[i] * curl1[i];
		max2 += curl2[i] * curl2[i];
		start += 1;
	}
	re = start * re - sum1 * sum2;

	sum1 = sqrt(start * max1 - sum1 * sum1);
	sum2 = sqrt(start*max2 - sum2 * sum2);
	return re / (sum1*sum2);
}
/***************************************
������:convolution
���أ����ؾ�����ݳ���
������curl1��len_in������
������ double* x2, double* curl_re, int len_2 ����ʱ��ĳ����ݣ�����ʱ�Ľ������ ����Ӧ�ú�����1�ǵȼ��;��ȵ�
������double lowline, double topline
˵������������Է������ݳ���Ϊ׼
�޶���
***************************************/
int convolution(double* curl1, int len_in, double*x2, double* curl_re, int len_2, double lowline, double topline) {
	double re = 0, sum1 = 0;
	int start = 0, end = 0;
	for (int i = 0; i <len_2; i++) {
		if (x2[i] < lowline) {
			start++;
			continue;
		}
		if (x2[i] > topline) {
			end = i;
			break;
		}
		if (i < len_in) {
			sum1 += curl1[i];
		}
	}
	for (int i = 0; i < len_in; i++) {
		curl1[i] /= sum1;
	}
	int sampleNum = end - start + 1;
	double* result = (double*)malloc(sizeof(double)*sampleNum);
	int prePointCnt, backPointCnt;
	int k;
	for (int i = 0; i < sampleNum; i++) {
		if (i < len_in) {
			prePointCnt = len_in - 1 - i;
		}
		else {
			prePointCnt = 0;
		}

		if (i + len_in > sampleNum) {
			backPointCnt = sampleNum - i - 1;
		}
		else {
			backPointCnt = len_in - 1;
		}

		k = i;
		for (int j = prePointCnt; j < backPointCnt; j++) {
			result[i] += curl_re[k + sampleNum + start] * curl1[j];
			k = k + 1;
		}
	}	
	curl_re = result;
	memcpy(curl_re, result, sampleNum * sizeof(double));
	memset(&curl_re[sampleNum], 0, len_2 - sampleNum);
	free(result);
	return sampleNum;
}


/***************************************
������:split
���أ�1
������spBuff��unsigned short��������
������ double *pIntensity���
������len���ݳ���
������xs ���Ų���
˵�����������ϵ��
�޶���x:���ᣬcurl1������ϵĹ��ף�len�����ȣ�curl2�����׳��ȵ���������һ��Ϊ��ϵĹ��ף��ڶ���Ϊ������w
		curl1=curl2*score+w
***************************************/
int split(double* x, double* curl1, int len, double* curl2, int size, double* score, double lowline, double topline) {
	//׼��������Ϣ
	int head = 0, tail = 0, cnt;
	for (int i = 0; i < len; i++) {
		if (x[i] <= lowline && head == 0) {
			head = i;
		}
		else if (x[i] > topline)
			break;
		tail = i - 1;
	}
	cnt = tail - head + 1;
	int mt_len = (size + 1) * cnt;
	double* target = (double*)malloc(sizeof(double)*cnt);
	double* base = (double*)malloc(sizeof(double)*size*mt_len);
	double* mt = (double*)malloc(sizeof(double)*size*mt_len);
	double* mt_d = (double*)malloc(sizeof(double)*size*mt_len);
	double* last_mt = (double*)malloc(sizeof(double)*size*mt_len);
	double* re = (double*)malloc(sizeof(double)*cnt);
	double* h = (double*)malloc(sizeof(double)*cnt);
	memcpy(target, &curl1[head], sizeof(double)*cnt);
	double* p = base, *p2 = curl2 + head;
	double max = target[0];
	for (int i = 1; i < cnt; i++) {
		max = max > target[i] ? max : target[i];
	}
	for (int i = 0; i < cnt; i++) {
		target[i] = target[i] * 100 / max;
	}
	for (int i = 0; i < size; i++) {
		memcpy(p, p2, sizeof(double)*cnt);
		double max = p[0];
		for (int i = 1; i < cnt; i++) {
			max = max > p[i] ? max : p[i];
		}
		for (int i = 0; i < cnt; i++)
			p[i] = p[i] / max;
		p += cnt;
		p2 += len;
	}
	//�������
	for (int i = head; i <= tail; i++) {
		*p++ = 0.2;
	}

	for (int i = 0; i <mt_len - cnt; i++) {
		mt[i] = 1;
	}
	for (int i = mt_len - cnt; i <mt_len; i++) {
		mt[i] = 0;
	}
	//�任����

	int count = 0;  //ִ�д���
	double J = 5000000000000000, last_J;
	//time_start = time.time()
	double step = 1;

	while (J > 0.00000001 && step > 0.0001) {
		last_J = J;
		//re = np.sum(w * x, 0);
		J = 0;
		for (int i = 0; i < cnt; i++) {
			re[i] = mt[i] * base[i];
			for (int j = 1; j < size + 1; j++)
				re[i] += mt[i + j * cnt] * base[i + j * cnt];
			h[i] = re[i] - target[i];
			J += h[i] * h[i] / 2;
		}
		//h = re - y
		//delta = h * x
		for (int i = 0; i < mt_len; i++) {
			mt_d[i] = h[i%cnt] * base[i];
		}
		memcpy(last_mt, mt, mt_len * sizeof(double));

		for (int j = 0; j < size; j++) {
			int shift = j * cnt;
			/*for (int i = 1; i < cnt; i++) {
			mt_d[shift] += mt_d[i + shift];
			}
			mt_d[shift] /= cnt;*/
			for (int i = 1; i < cnt; i++) {
				mt_d[shift] += mt_d[i + shift];
			}
			mt_d[shift] = mt_d[shift] / cnt;
			mt[shift] -= step * mt_d[shift];
			mt[shift] = mt[shift] < 0 ? 0 : mt[shift];
			for (int i = 1; i < cnt; i++) {
				mt[shift + i] = mt[shift];
			}

		}

		int shift = size * cnt;
		for (int j = 0; j < cnt; j++) {
			mt[shift + j] -= step * mt_d[shift + j];
			mt[shift + j] = mt[shift + j] < 0 ? 0 : mt[shift + j];
		}
		//w = w - step * delta

		if (J > last_J &&count != 0) {
			step = step / 2;
			memcpy(mt, last_mt, mt_len * sizeof(double));
		}
		else if (fabs(J - last_J) < 0.0000001) {
			//slow
			break;
		}
		count += 1;
	}

	//��������
	memcpy(curl1, target, sizeof(double)*cnt);
	free(target);
	free(mt_d);
	free(last_mt);
	free(re);

	memcpy(h, &x[head], sizeof(double)*cnt);
	memcpy(x, h, sizeof(double)*cnt);
	memset(&x[cnt], 0, sizeof(double)*(len - cnt));
	free(h);
	count = cnt * size;
	for (int i = 0; i < cnt; i++) {
		mt[count + i] *= 0.2;
	}
	memcpy(&base[cnt*size], &mt[cnt*size], sizeof(double)*cnt);
	memcpy(curl2, base, sizeof(double)*mt_len);
	memset(&curl2[mt_len], 0, sizeof(double)*((size + 1)*len - mt_len));
	free(base);

	h = mt;
	for (int i = 0; i < size; i++) {
		score[i] = *h;
		h += cnt;
	}
	free(mt);
	return cnt;

}

/***************************************
��������getMax
���أ�arr��count�������ڵ����ֵ
������arr�� double*���ͣ���������
������count�� int���ͣ� ����
˵�������Բ�ֵΪһ����һ������
***************************************/
double getMax(double *arr, int count) {
	double temp = arr[0];
	for (int i = 1; i<count; i++) {
		if (temp<arr[i])
			temp = arr[i];
	}
	return temp;
}
/***************************************
��������array_normalization
���أ�arr��count�������ڵ����ݵĹ�һ��
������arr�� double*���ͣ� ���������
������count�� int���ͣ� ����
˵����ԭָ�뷵�ع�һ��������
***************************************/
int array_normalization(double *arr, int count) {
	double temp_max = getMax(arr, count);
	for (int i = 0; i < count; i++) {
		arr[i] /= temp_max;
	}
	return 0;
}
/***************************************
��������LinearInterpolate
���أ����Բ�ֵ��Ĺ���x��y
������n��int���ͣ��������Ҫ��ֵ�����ݵĳ���
������x��double*���ͣ� ������Ҫ��ֵ��x������
������y, double*���ͣ� ������Ҫ��ֵ��y������
������lowline�� int���ͣ� ��ֵ��Χ������
������topline�� int���ͣ� ��ֵ��Χ������
������s��double*���ͣ� ���ز�ֵ�õ����ݵ�x��
������r��double*���ͣ� ���ز�ֵ�õ����ݵ�y��
˵�������Բ�ֵΪһ����һ������
***************************************/
int LinearInterpolate(int n, double *x, double *y, int lowline, int topline, double *s, double *r)
{
	int m = topline - lowline;
	/*if (lowline < x[0] || topline>x[n - 1]) {
		return 1;
	}*/
	int j = 0;
	//lowline������Сǰ�������һ������
	while (lowline < x[0] && lowline + j<x[0]) {
		
		s[j] = lowline + j;
		r[j] = y[0];
		j++;
	}
	int tempint = j;
	//��д��󲿷֣�������x��ֵ
	for (; lowline + j<topline; j++) {
		s[j] = lowline + j;
		if (s[j]>=x[n-1])
			r[j] = y[n - 1];
	}
	int i = 1;
	
	if (tempint == 0) {		
		s[0] = lowline;
		r[0] = y[0];
		tempint = 1;
	}
	//i����lowline����ʼλ��
	i = tempint;
	//ԭʼ���ݵ�idx
	int begin_idx_org = 0;
	int end_idx_org = begin_idx_org + 1;
	int last_i=i-1;
	double a=0, b=0;
	a = (y[end_idx_org] - y[begin_idx_org]) / (x[end_idx_org] - x[begin_idx_org]);	
	b = y[begin_idx_org];
	for (; i<m; i++)
	{   //s[i]��ǰ��ֵx��x[end_idx_org]�յ�x,�˴�1�ǲ���
		/*if (i > 2190) {
			i = i;
		}*/
		if (s[i] == x[end_idx_org]) { //�������
			r[i] = y[end_idx_org];
			begin_idx_org = end_idx_org;
			end_idx_org += 1;
			if (end_idx_org >= n) {
				break;
			}
			a = (y[end_idx_org] - y[begin_idx_org]) / (x[end_idx_org] - x[begin_idx_org]);
			b = y[begin_idx_org];
		}
		else if (x[end_idx_org]< s[i]){//������Ѿ����ϵ����ߴ�
			//�ƶ�����
			while (x[end_idx_org] < s[i] && end_idx_org<n) {
				begin_idx_org = end_idx_org;
				end_idx_org += 1;
			}
			if (end_idx_org >= n) {
				break;
			}
			a = (y[end_idx_org] - y[begin_idx_org]) / (x[end_idx_org] - x[begin_idx_org]);			
			b = y[begin_idx_org];
			r[i] = a*(s[i]- x[begin_idx_org]) + b;
		}
		else if (x[end_idx_org] > s[i]) {//ֱ�Ӳ���			
			r[i] = a * (s[i] - x[begin_idx_org]) + b;
		}
		//else {//����1
		//	double a = (y[end_idx_org] - y[begin_idx_org]) / (x[end_idx_org] - x[begin_idx_org]);
		//	double b = y[begin_idx_org];
		//	r[i] = a * (s[i]- x[begin_idx_org]) + b;
		//}
		last_i = i;
	}
	return 0;
}
/***************************************
��������convolve_return
���أ�����������
����������Ĺ��ױ����ǲ�ֵ֮��Ĺ��ף�һ�����Ӧһ������
������Signal�� double*���ͣ� �����źŵ�ָ��
������SignalLen�� int���ͣ� �����źŵĳ���
������Kernel�� double*���ͣ� �������˵�ָ��
������KernelLen�� int���ͣ� �������˵ĳ���
������Result�� double*���ͣ� ���ؾ����Ľ��������ΪSignalLen
˵������һ���㷨��ͬ������������ͬ���
***************************************/
int convolve_return(double *Signal, int SignalLen, double *Kernel, int KernelLen, double *Result) {
	int kmin, kmax, k;
	for (int n = (KernelLen - 1) / 2, count = 0; n < SignalLen + (KernelLen - 1) / 2; n++, count++) {
		Result[count] = 0;
		kmin = (n >= KernelLen - 1) ? n - (KernelLen - 1) : 0;
		kmax = (n < SignalLen - 1) ? n : SignalLen - 1;
		for (k = kmin; k <= kmax; k++)
		{
			Result[count] += Signal[k] * Kernel[n - k];
		}
	}
	return 0;
}
/***************************************
��������atandard_Deviation
���أ�����ı�׼��
������length, int���ͣ� ����ĳ���
������arr, double*���ͣ� ����
˵��������arr�ı�׼��
�޸ļ������������㷶Χ���ڲ�ֵ�ǰ����ʱ����Щ���ȶ��ź� 20190701
***************************************/

double standard_Deviation(int length, double* arr) {
	double sum = 0, avg = 0, S_square = 0;
	for (int i = 20; i < length; i++) {
		sum += arr[i];
	}
	avg = sum / length;
	for (int i = 20; i < length; i++) {
		S_square += (arr[i] - avg)*(arr[i] - avg);
	}
	return sqrt(S_square / length);
}
/***************************************
��������make_spec_x_arrary
���أ�
������x0,��һ����ֵ
      buff,�ڴ��׵�ַ
	  len�����ݵ����
	  interval�����
˵��������ǰ�����ڴ棬�����߸��������ڴ�
***************************************/
void make_spec_x_arrary(double x0, double* buff, int len, double interval) {
	buff[0] = x0;
	for (int i = 1; i < len; i++) {
		buff[i] = buff[i - 1] + interval;
	}
}

/***************************************
��������getMax
���أ�arr��count�������ڵ����ֵ
������arr�� double*���ͣ���������
������count�� int���ͣ� ����
���أ���������
˵�������Բ�ֵΪһ����һ������
***************************************/
int get_arr_max(double *arr, int count) {
	double temp = arr[0];
	int idx = 0;
	for (int i = 1; i<count; i++) {
		if (temp < arr[i]){
			temp = arr[i];
			idx = i;
		}
	}
	return idx;
}
/***************************************
��������array_normalization
���أ�arr���ֵidx
������arr�� double*���ͣ� ���������
������count�� int���ͣ� ����
˵����ԭָ�뷵�ع�һ��������
***************************************/
int array_normalization_new(double *arr, int count) {
	int idx = get_arr_max(arr, count);
	double temp_max = arr[idx];
	for (int i = 0; i < count; i++) {
		arr[i] /= temp_max;
	}
	return idx;
}
/***************************************
��������array_normalization_area
���أ�arr���ֵidx
������arr�� double*���ͣ� ���������
������count�� int���ͣ� ����
˵��������������й�һ��
***************************************/

void array_normalization_area(double *arr, int count) {
	double sum_peak = 0;
	for (int i = 0; i<count; i++) {
		sum_peak += arr[i];
	}	
	for (int i = 0; i < count; i++) {
		arr[i] /= sum_peak;
	}
	return ;
}

/***************************************
��������get_pulse
���أ�����һ�����岨�Σ���������ֵ��һ����
������length�� int���ͣ� ���ڲ����������ĳ���
������pulse�� double*���ͣ� ���ڷ��ز������ݵ�ָ��
������dfactor�� double���ͣ� չ��
������shiftRight�� int���ͣ� λ��
ע�⣺���ھ�����˲�ע����Գ�
˵����ԭָ�뷵�������ķ�
***************************************/

int get_pulse(int length,double *pulse,double dfactor, double shiftRight){
	double x;
	double* xx = (double*)malloc(length * sizeof(double));
	xx[0] = 0 - length / 2;
	x = (xx[0] - shiftRight) / dfactor;
	pulse[0] = 2 / (exp(x) + exp(-x));
	double maxval = 0;
	for (int i = 1; i < length; i++) {
		xx[i] = xx[i - 1] + 1;
		x = (xx[i] - shiftRight) / dfactor;
		pulse[i] = 2 / (exp(x) + exp(-x));
		maxval = maxval>pulse[i] ? maxval : pulse[i];
	}
	for (int i = 0; i < length; i++) {
		pulse[i] /= maxval;
	}
	free(xx);
	return 0;
}