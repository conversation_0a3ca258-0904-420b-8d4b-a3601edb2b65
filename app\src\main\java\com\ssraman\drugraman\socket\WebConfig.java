package com.ssraman.drugraman.socket;

/**
 * Created by chen<PERSON><PERSON><PERSON><PERSON> on 2018/1/25.
 */

public class WebConfig {
    private int port;//端口
    private int maxParallels;//最大监听数
    private String hostName;

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public int getMaxParallels() {
        return maxParallels;
    }

    public void setMaxParallels(int maxParallels) {
        this.maxParallels = maxParallels;
    }
}
