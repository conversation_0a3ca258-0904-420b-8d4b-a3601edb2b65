<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="mpresenter"
            type="com.ssraman.drugraman.ui.record.BatchTemplateFragment.MPresenter" />

        <variable
            name="batchTemplateViewModel"
            type="com.ssraman.drugraman.ui.vm.BatchTemplateViewModel" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.record.BatchTemplateFragment">

        <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/activity_main_pdf"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="4dp">


                <LinearLayout
                    android:id="@+id/activity_main_table"
                    android:layout_width="match_parent"
                    android:layout_height="550dp"
                    android:orientation="horizontal">

                    <include
                        android:id="@+id/item_table1"
                        layout="@layout/layout_batch_table_template"
                        android:layout_width="match_parent"
                        android:layout_height="550dp"
                        android:layout_weight="1" />
                </LinearLayout>

            </LinearLayout>

            <Button
                android:id="@+id/btn_sign_pdf"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/audit_sign"
                android:background="@color/nc_sub"
                android:bottomLeftRadius="8dp"
                android:bottomRightRadius="118dp"
                android:onClick="@{(view)->mpresenter.SignPdfClick(view)}"
                android:padding="10dp"
                android:textColor="@color/report_btn_color"
                android:topLeftRadius="8dp"
                android:topRightRadius="8dp" />

            <Button
                android:id="@+id/btn_approve_pdf"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/audit_approve"
                android:background="@color/nc_sub"
                android:bottomLeftRadius="8dp"
                android:bottomRightRadius="118dp"
                android:onClick="@{(view)->mpresenter.ApprovePdfClick(view)}"
                android:padding="10dp"
                android:textColor="@color/report_btn_color"
                android:topLeftRadius="8dp"
                android:topRightRadius="8dp" />


            <Button
                android:id="@+id/btn_reject_pdf"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                android:background="@color/nc_sub"
                android:bottomLeftRadius="8dp"
                android:bottomRightRadius="118dp"
                android:onClick="@{(view)->mpresenter.RejectPdfClick(view)}"
                android:padding="10dp"
                android:text="@string/audit_reject"
                android:textColor="@color/report_btn_red_color"
                android:topLeftRadius="8dp"
                android:topRightRadius="8dp" />

            <Button
                android:id="@+id/btn_creat_pdf"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/nc_sub"
                android:bottomLeftRadius="8dp"
                android:bottomRightRadius="118dp"
                android:layout_marginTop="5dp"
                android:onClick="@{(view)->mpresenter.CreatPdfClick(view)}"
                android:padding="10dp"
                android:text="@string/create_pdf"
                android:textColor="@color/report_btn_color"
                android:topLeftRadius="8dp"
                android:topRightRadius="8dp" />

            <Button
                android:id="@+id/btn_save_pdf"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                android:background="@color/nc_sub"
                android:bottomLeftRadius="8dp"
                android:bottomRightRadius="118dp"
                android:onClick="@{(view)->mpresenter.SavePdfClick(view)}"
                android:padding="10dp"
                android:text="@string/save_pdf"
                android:textColor="@color/report_btn_color"
                android:topLeftRadius="8dp"
                android:topRightRadius="8dp" />

        </LinearLayout>


    </ScrollView>
</layout>