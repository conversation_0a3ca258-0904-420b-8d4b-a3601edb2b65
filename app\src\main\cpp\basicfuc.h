#pragma once
//���ʱ��˾�ر�
#include<list>
#include "mathtool.h"


# define _EXPORTING
#ifdef _EXPORTING
//#define API_DECLSPEC __declspec(dllexport)
//#define API_DECLSPEC __attribute__ ((visibility("default")))
#define API_DECLSPEC 
#else
#define API_DECLSPEC __declspec(dllimport)
#endif

#ifndef BASICFUC_H
#define BASICFUC_H


//#ifdef __cplusplus
//    extern "C" {
//#endif


typedef struct CSPECNEW {
	double* dWcnt;
	double* dIntensity;
	int len;
}SPEC;

typedef struct CPEAK {
	double dWcnt;
	double leftWcnt;
	double rightWcnt;
	int cnt;
	double dIntensity; //�Ѿ���һ��
	double rank;       //����߷�ı�ֵ
	double width;
	int mustbe;       //�������
}PEAK;
typedef struct CPEAKS {
	PEAK* peaks;
	int num;
}PEAKS;

typedef struct CMATCHDATA {
	char* title;
	int title_len;
	int stID;
	PEAK* peaks;
	int num;
	int matchingNum;
	double score;
	double score0;
	double score1;
	double score2;
}MATCHDATA;

typedef struct CPEAKS2 {
	double *peaks_wcnt;
	double *peaks_width;
	double *peaks_hight;
	int num;
}PEAKS2;

typedef struct CBACKUP {
	unsigned char* buff;
	int len;
}BACKUP;
//typedef struct CMATCHRESULT {
//	double score;           //�÷�
//	char* commentstr;       //����
//	PEAK* peaksStd;         //��׼�������б�
//	PEAK* peaksChk;         //�����������б�
//	int cnt;                //ƥ������������
//}MATCHRESULT;
typedef struct CANALIZERESULT {
	double* score;          //ϵ�������һ��Ϊ��������ϵ��
	double* dIntensity;     //��������

}ANALIZERESULT;
//ԭʼ�������ݶ�Ӧ�����ط�Χ
typedef struct CPRANGE {
	int lowCnt;
	int topCnt;
}PRANGE;
typedef struct CPEAKSSORT {
	double dIntensity;
	double dwCnt;
	int idx;
	//int matching_idx;
}PEAKSSORT;
typedef std::list<CMATCHDATA* > STDDATA;

API_DECLSPEC int seePeaks(SPEC* spec, double SNR, int inttime_ms, PEAKS2* result,int preserve);
API_DECLSPEC int seePeaks_idx(double* y, double SNR, int len, int* peak_idx, int peaks_size, int preserve);
API_DECLSPEC void calibration(double * stdpeaks, int size,//������׼ֵ
	double * coff,//����ֵ 
	double * pixels        //����������λ�ñ�,����Ҳ��size�����û�в�0
);
API_DECLSPEC void clear_feak_peaks(double* spec, int len, int width, double minSNR);
API_DECLSPEC int makeshortSpec(SPEC* spec, double* peaks, int peaks_num, SPEC* spec_s);
//��ȡָ��������idx
API_DECLSPEC int getPos(double* wCnt, int len, double dWcnt);
//��Ѱ����������ֵ��Ҫָ�����Ĳ����ͷ�Χ
API_DECLSPEC int getMaxPos(SPEC* spec, double dWcnt, double width);
API_DECLSPEC int key(BACKUP* backup);
API_DECLSPEC double SpecSNR(double*  specdata,    //16λԭʼ��������
	int      len,        //���ݳ���
	int      lowlineCnt, //�Ͳ���λ�ã����ص�λ
	int      boxZeroPos, //���������rawSpec�е��׵�ַ��
	int      boxWidth,   //����������е����ݳ���
	int      peakCenter, //������Ĳο�λ�ã���λ����,������������boxZeroPos
	int      FWHM       //�������������Χ
);
API_DECLSPEC int autobaseline_org(double *pSpc,       //���׻�����
	double *pBkgSpc,    //����������
	double *pWaveCnt,   //����������
	double *pIntensity, //�������֮���
	int nLength,        //���ݳ���
	double dLowWaveCnt, //�Ͳ���
						//double dHighWaveCnt, //�Ͳ���
	int nR,
	int mode);

API_DECLSPEC int getNewSpec(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode);

API_DECLSPEC void getNewWCnt(int lowline, int topline, double* newWcnt);
API_DECLSPEC void autoBaseline(SPEC* spec, double dLowWaveCnt, int nR);
//API_DECLSPEC int smooth(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode);
//����Ҫ������ƽ��Ѱ�Ҿֲ����ֵ��Ҫ����ױ���Ҫ�нϺõ������������岻��̫�ܼ�,�����ÿ��2boxwidth��Χ����1��������
API_DECLSPEC int seePeaks_local(SPEC* spec, int boxwidth,   //����
	PEAKS2* result);
//���ʱ��˾��
API_DECLSPEC int encodeSoft(double * spBuff, unsigned int len);
//API_DECLSPEC int matching(CPEAKS2* peaks, int isAccu, double lowWcnt, STDDATA* pstdata);
//20201026���亯��
API_DECLSPEC double get_centerline(SPEC* spec, SPEC* spec_r, double para=0.8);
API_DECLSPEC double get_baseline(SPEC* spec, SPEC* spec_r, double para=7);
//�������߷�
API_DECLSPEC int getNewSpec_20201030(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode);
//���ڻ���
API_DECLSPEC double getNewSpec_20201101(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode);
API_DECLSPEC double getNewSpec_20210308(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode);
API_DECLSPEC double getNewSpecSmooth_20210308(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode);
API_DECLSPEC double getNewSpecAutobase_20210308(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode);
API_DECLSPEC double getNewSpecInterpolate_20220909(SPEC* spec, int lowline, int topline, SPEC* spec_r, int mode);
API_DECLSPEC void kmfilter(double* spec,int len,double noise);

API_DECLSPEC double match_cosd(double wavenum1[], double signal1[], int length1,double wavenum2[], double signal2[], int length2);
API_DECLSPEC double match_hqi(double wavenum1[], double signal1[], int length1,double wavenum2[], double signal2[], int length2);
API_DECLSPEC double match_cosd_w(double wavenum1[], double signal1[], int length1,double wavenum2[], double signal2[], int length2);

API_DECLSPEC int convertdata_org(unsigned short* spBuff, double* pIntensity, unsigned int len,double xs);
//#ifdef __cplusplus
//    }
//#endif
#endif