package com.ssraman.drugraman.sortableview;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.TextView;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.newentiry.MatchResultNodeInfo;

import java.util.List;

import de.codecrafters.tableview.TableView;
import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * @author: Administrator
 * @date: 2021/6/29
 */
public class CorrelationStandardTableDataAdapter extends LongPressAwareTableDataAdapter<MatchResultNodeInfo> {
    private static final int TEXT_SIZE = 16;
    private static final int TEXT_D_SIZE = 14;

    public CorrelationStandardTableDataAdapter(Context context, List data, TableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final MatchResultNodeInfo matchResultInfo = getRowData(rowIndex);
        View renderedView = null;
        switch (columnIndex) {
            case 0:
                renderedView = renderString(String.valueOf(rowIndex+1));
                break;
            case 1:
                double correlation = matchResultInfo.getExRatio();
                renderedView = renderString(String.format("%.2f",correlation));
                break;
            case 2:
                String sampleName = matchResultInfo.getSampleName();
                renderedView = renderString(sampleName);
                break;
            case 3:
                String detail = "详情";
                renderedView = renderDString(matchResultInfo,rowIndex);
                break;
        }
        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final MatchResultNodeInfo peakInfo = getRowData(rowIndex);
        View renderedView = null;

        renderedView = getDefaultCellView(rowIndex, columnIndex, parentView);

        return renderedView;
    }

    private OnItemClickListener mOnItemClickListener;

    public void setOnItemClickListener(OnItemClickListener mOnItemClickListener) {
        this.mOnItemClickListener = mOnItemClickListener;
    }

    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        if(value==null||value.equals("null"))
        {
            textView.setText("");
        }
        else
        {
            textView.setText(value);
        }
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setTextColor(Color.WHITE);
        return textView;
    }


    private View renderDString(final MatchResultNodeInfo matchResultInfo,int position) {
        String value = "详情";
        final TextView textView = new TextView(getContext());
        if(value==null||value.equals("null"))
        {
            textView.setText("");
        }
        else
        {
            textView.setText(value);
        }
        Drawable dra= getResources().getDrawable(R.drawable.ic_details_12);
        textView.setCompoundDrawables(null, dra,null,null);
        //textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_D_SIZE);
        textView.setTextColor(Color.WHITE);
        if (mOnItemClickListener != null)
        {
            textView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mOnItemClickListener.OnItemClick(v,matchResultInfo,position);
                }
            });
        }
        return textView;
    }

    public interface OnItemClickListener {

        //单点监听
        void OnItemClick(View view,MatchResultNodeInfo item, int position);
        //选择改变监听
        void OnItemCheckedChangeClick(View view,MatchResultNodeInfo item, int position);
    }


}
