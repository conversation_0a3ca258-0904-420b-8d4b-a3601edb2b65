package com.ssraman.drugraman.typeface;

/**
 * @author: Administrator
 * @date: 2021/10/15
 */
public enum EvaluationModeType {
    验证(0), // 验证
    辨识(1);// 识别

    private int value = 0;

    private EvaluationModeType(int value) { // 必须是private的，否则编译错误
        this.value = value;
    }

    public static EvaluationModeType valueOf(int value) { // 手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return 验证;
            case 1:
                return 辨识;
            default:
                return 验证;
        }
    }

    public int value() {
        return this.value;
    }
}
