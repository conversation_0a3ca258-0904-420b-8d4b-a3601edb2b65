package com.ssraman.drugraman.network.entity.response;


import com.ssraman.lib_common.entity.response.CommResponse;

/**
 * Created by chen<PERSON><PERSON><PERSON><PERSON> on 2018/1/30.
 */

public class VersionResponse extends CommResponse {


    /**
     * data : {"fileSize":1205486,"isEnabled":true,"description":"r800000049","fileUrl":"http://ssraman-rms.oss-cn-qingdao.aliyuncs.com/upload/2d20796d-cebe-4254-8dc8-0e8df42cba9e.zip","fileDigest":"07a1e9320ff0f9034e7ce521dcd8dc62","id":"1325705166741180416","deviceSoftwareModule":{"code":"program","name":"program","id":"1325346805738770432"},"fileVersion":"***************","createDate":1519979720000}
     * message : null
     */

    private DataBean data;

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * fileSize : 1205486
         * isEnabled : true
         * description : r800000049
         * fileUrl : http://ssraman-rms.oss-cn-qingdao.aliyuncs.com/upload/2d20796d-cebe-4254-8dc8-0e8df42cba9e.zip
         * fileDigest : 07a1e9320ff0f9034e7ce521dcd8dc62
         * id : 1325705166741180416
         * deviceSoftwareModule : {"code":"program","name":"program","id":"1325346805738770432"}
         * fileVersion : ***************
         * createDate : 1519979720000
         */

        private int fileSize;
        private boolean isEnabled;
        private String description;
        private String fileUrl;
        private String fileDigest;
        private String id;
        private DeviceSoftwareModuleBean deviceSoftwareModule;
        private String fileVersion;
        private long createDate;

        public int getFileSize() {
            return fileSize;
        }

        public void setFileSize(int fileSize) {
            this.fileSize = fileSize;
        }

        public boolean isIsEnabled() {
            return isEnabled;
        }

        public void setIsEnabled(boolean isEnabled) {
            this.isEnabled = isEnabled;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getFileDigest() {
            return fileDigest;
        }

        public void setFileDigest(String fileDigest) {
            this.fileDigest = fileDigest;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public DeviceSoftwareModuleBean getDeviceSoftwareModule() {
            return deviceSoftwareModule;
        }

        public void setDeviceSoftwareModule(DeviceSoftwareModuleBean deviceSoftwareModule) {
            this.deviceSoftwareModule = deviceSoftwareModule;
        }

        public String getFileVersion() {
            return fileVersion;
        }

        public void setFileVersion(String fileVersion) {
            this.fileVersion = fileVersion;
        }

        public long getCreateDate() {
            return createDate;
        }

        public void setCreateDate(long createDate) {
            this.createDate = createDate;
        }

        public static class DeviceSoftwareModuleBean {
            /**
             * code : program
             * name : program
             * id : 1325346805738770432
             */

            private String code;
            private String name;
            private String id;

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }
        }
    }
}
