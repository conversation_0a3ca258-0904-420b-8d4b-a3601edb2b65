package com.ssraman.drugraman.rbac.service.impl;

import android.content.Context;
import android.util.Log;

import com.ssraman.drugraman.db.DBController;
import com.ssraman.drugraman.db.entity.RbacUser;
import com.ssraman.drugraman.db.entity.RbacUserSession;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.gen.RbacUserDao;
import com.ssraman.drugraman.db.gen.RbacUserSessionDao;
import com.ssraman.drugraman.rbac.dto.AuthResult;
import com.ssraman.drugraman.rbac.service.IRbacUserService;
import com.ssraman.drugraman.rbac.service.IRbacRoleService;
import com.ssraman.drugraman.rbac.service.IRbacPermissionService;

import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * RBAC用户服务实现
 */
public class RbacUserServiceImpl implements IRbacUserService {
    
    private static final String TAG = "RbacUserServiceImpl";
    private static final int MAX_LOGIN_ATTEMPTS = 5; // 最大登录尝试次数
    private static final int LOCK_DURATION_MINUTES = 30; // 锁定时长（分钟）
    private static final int SESSION_DURATION_HOURS = 8; // 会话时长（小时）
    
    private final Context context;
    private final DaoSession daoSession;
    private final RbacUserDao userDao;
    private final RbacUserSessionDao sessionDao;
    private final IRbacRoleService roleService;
    private final IRbacPermissionService permissionService;
    
    public RbacUserServiceImpl(Context context, IRbacRoleService roleService, IRbacPermissionService permissionService) {
        this.context = context;
        this.daoSession = DBController.getInstance(context).getDaoSession();
        this.userDao = daoSession.getRbacUserDao();
        this.sessionDao = daoSession.getRbacUserSessionDao();
        this.roleService = roleService;
        this.permissionService = permissionService;
    }
    
    @Override
    public AuthResult authenticate(String username, String password) {
        try {
            // 查找用户
            RbacUser user = getUserByUsername(username);
            if (user == null) {
                return AuthResult.invalidCredentials();
            }
            
            // 检查用户状态
            if (user.isDeleted()) {
                return AuthResult.userDeleted();
            }
            
            if (!user.isActive()) {
                return AuthResult.userInactive();
            }
            
            if (user.isLocked()) {
                return AuthResult.userLocked();
            }
            
            // 检查登录尝试次数
            if (user.getLoginAttempts() != null && user.getLoginAttempts() >= MAX_LOGIN_ATTEMPTS) {
                // 锁定用户
                user.lockUser(LOCK_DURATION_MINUTES);
                userDao.update(user);
                return AuthResult.tooManyAttempts();
            }
            
            // 验证密码
            if (!verifyPassword(password, user.getPasswordHash(), user.getSalt())) {
                // 增加登录尝试次数
                user.incrementLoginAttempts();
                userDao.update(user);
                return AuthResult.invalidCredentials();
            }
            
            // 登录成功，重置登录尝试次数
            user.resetLoginAttempts();
            user.updateLastLogin();
            userDao.update(user);
            
            // 创建会话
            String sessionToken = generateSessionToken();
            Date expiresAt = new Date(System.currentTimeMillis() + SESSION_DURATION_HOURS * 60 * 60 * 1000);
            RbacUserSession session = new RbacUserSession(user.getId(), sessionToken, getDeviceInfo(), expiresAt);
            sessionDao.insert(session);
            
            // 获取用户角色和权限
            return AuthResult.success(user, sessionToken, 
                    roleService.getUserRoles(user.getId()),
                    permissionService.getUserPermissions(user.getId()),
                    expiresAt.getTime());
            
        } catch (Exception e) {
            Log.e(TAG, "用户认证失败", e);
            return AuthResult.failure("认证过程中发生错误");
        }
    }
    
    @Override
    public RbacUser validateSession(String sessionToken) {
        try {
            RbacUserSession session = sessionDao.queryBuilder()
                    .where(RbacUserSessionDao.Properties.SessionToken.eq(sessionToken))
                    .unique();
            
            if (session == null || !session.isValid()) {
                return null;
            }
            
            return getUserById(session.getUserId());
        } catch (Exception e) {
            Log.e(TAG, "会话验证失败", e);
            return null;
        }
    }
    
    @Override
    public boolean logout(String sessionToken) {
        try {
            RbacUserSession session = sessionDao.queryBuilder()
                    .where(RbacUserSessionDao.Properties.SessionToken.eq(sessionToken))
                    .unique();
            
            if (session != null) {
                session.invalidate();
                sessionDao.update(session);
            }
            return true;
        } catch (Exception e) {
            Log.e(TAG, "用户登出失败", e);
            return false;
        }
    }
    
    @Override
    public String refreshSession(String sessionToken) {
        try {
            RbacUserSession oldSession = sessionDao.queryBuilder()
                    .where(RbacUserSessionDao.Properties.SessionToken.eq(sessionToken))
                    .unique();
            
            if (oldSession == null || !oldSession.isValid()) {
                return null;
            }
            
            // 创建新会话
            String newSessionToken = generateSessionToken();
            Date expiresAt = new Date(System.currentTimeMillis() + SESSION_DURATION_HOURS * 60 * 60 * 1000);
            RbacUserSession newSession = new RbacUserSession(oldSession.getUserId(), newSessionToken, getDeviceInfo(), expiresAt);
            sessionDao.insert(newSession);
            
            // 失效旧会话
            oldSession.invalidate();
            sessionDao.update(oldSession);
            
            return newSessionToken;
        } catch (Exception e) {
            Log.e(TAG, "会话刷新失败", e);
            return null;
        }
    }
    
    @Override
    public RbacUser createUser(String username, String password, String email, String fullName) {
        try {
            // 检查用户名是否已存在
            if (existsUsername(username)) {
                throw new IllegalArgumentException("用户名已存在");
            }
            
            // 检查邮箱是否已存在
            if (email != null && !email.isEmpty() && existsEmail(email)) {
                throw new IllegalArgumentException("邮箱已存在");
            }
            
            // 生成密码哈希
            String salt = generateSalt();
            String passwordHash = hashPassword(password, salt);
            
            // 创建用户
            RbacUser user = new RbacUser(username, passwordHash, salt, email, fullName);
            userDao.insert(user);
            
            return user;
        } catch (Exception e) {
            Log.e(TAG, "创建用户失败", e);
            throw new RuntimeException("创建用户失败", e);
        }
    }
    
    @Override
    public RbacUser updateUser(Long userId, String email, String fullName) {
        try {
            RbacUser user = getUserById(userId);
            if (user == null) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            user.setEmail(email);
            user.setFullName(fullName);
            user.setUpdatedAt(new Date());
            userDao.update(user);
            
            return user;
        } catch (Exception e) {
            Log.e(TAG, "更新用户失败", e);
            throw new RuntimeException("更新用户失败", e);
        }
    }
    
    @Override
    public boolean deleteUser(Long userId) {
        try {
            RbacUser user = getUserById(userId);
            if (user == null) {
                return false;
            }
            
            // 软删除
            user.setStatus(-2);
            user.setUpdatedAt(new Date());
            userDao.update(user);
            
            return true;
        } catch (Exception e) {
            Log.e(TAG, "删除用户失败", e);
            return false;
        }
    }
    
    @Override
    public RbacUser getUserById(Long userId) {
        try {
            return userDao.load(userId);
        } catch (Exception e) {
            Log.e(TAG, "获取用户失败", e);
            return null;
        }
    }
    
    @Override
    public RbacUser getUserByUsername(String username) {
        try {
            return userDao.queryBuilder()
                    .where(RbacUserDao.Properties.Username.eq(username))
                    .unique();
        } catch (Exception e) {
            Log.e(TAG, "根据用户名获取用户失败", e);
            return null;
        }
    }
    
    @Override
    public List<RbacUser> getAllUsers() {
        try {
            return userDao.queryBuilder()
                    .where(RbacUserDao.Properties.Status.notEq(-2)) // 排除已删除的用户
                    .list();
        } catch (Exception e) {
            Log.e(TAG, "获取所有用户失败", e);
            return null;
        }
    }
    
    @Override
    public List<RbacUser> searchUsers(String keyword) {
        try {
            return userDao.queryBuilder()
                    .where(RbacUserDao.Properties.Status.notEq(-2))
                    .whereOr(
                            RbacUserDao.Properties.Username.like("%" + keyword + "%"),
                            RbacUserDao.Properties.FullName.like("%" + keyword + "%"),
                            RbacUserDao.Properties.Email.like("%" + keyword + "%")
                    )
                    .list();
        } catch (Exception e) {
            Log.e(TAG, "搜索用户失败", e);
            return null;
        }
    }
    
    @Override
    public boolean existsUsername(String username) {
        try {
            Long count = userDao.queryBuilder()
                    .where(RbacUserDao.Properties.Username.eq(username))
                    .count();
            return count > 0;
        } catch (Exception e) {
            Log.e(TAG, "检查用户名是否存在失败", e);
            return false;
        }
    }
    
    @Override
    public boolean existsEmail(String email) {
        try {
            Long count = userDao.queryBuilder()
                    .where(RbacUserDao.Properties.Email.eq(email))
                    .count();
            return count > 0;
        } catch (Exception e) {
            Log.e(TAG, "检查邮箱是否存在失败", e);
            return false;
        }
    }
    
    // 其他方法的实现...
    // 由于篇幅限制，这里只实现了核心方法
    // 完整实现需要包含所有接口方法
    
    /**
     * 生成密码哈希
     */
    private String hashPassword(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(salt.getBytes());
            byte[] hashedPassword = md.digest(password.getBytes());
            return android.util.Base64.encodeToString(hashedPassword, android.util.Base64.DEFAULT);
        } catch (Exception e) {
            throw new RuntimeException("密码哈希失败", e);
        }
    }
    
    /**
     * 验证密码
     */
    private boolean verifyPassword(String password, String storedHash, String salt) {
        String hashedPassword = hashPassword(password, salt);
        return hashedPassword.equals(storedHash);
    }
    
    /**
     * 生成随机盐值
     */
    private String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[16];
        random.nextBytes(salt);
        return android.util.Base64.encodeToString(salt, android.util.Base64.DEFAULT);
    }
    
    /**
     * 生成会话令牌
     */
    private String generateSessionToken() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * 获取设备信息
     */
    private String getDeviceInfo() {
        return android.os.Build.MODEL + " " + android.os.Build.VERSION.RELEASE;
    }
    
    // 为了简化，其他接口方法暂时返回默认值或抛出未实现异常
    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        // TODO: 实现密码修改逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public boolean resetPassword(Long userId, String newPassword) {
        // TODO: 实现密码重置逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public boolean validatePasswordStrength(String password) {
        // TODO: 实现密码强度验证
        return password != null && password.length() >= 6;
    }
    
    @Override
    public boolean activateUser(Long userId) {
        // TODO: 实现用户激活逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public boolean deactivateUser(Long userId) {
        // TODO: 实现用户停用逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public boolean lockUser(Long userId, int lockDurationMinutes) {
        // TODO: 实现用户锁定逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public boolean unlockUser(Long userId) {
        // TODO: 实现用户解锁逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public boolean resetLoginAttempts(Long userId) {
        // TODO: 实现重置登录尝试次数逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public long getUserCount() {
        // TODO: 实现获取用户总数逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public long getActiveUserCount() {
        // TODO: 实现获取活跃用户数逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public long getLockedUserCount() {
        // TODO: 实现获取锁定用户数逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
    
    @Override
    public List<RbacUser> getRecentLoginUsers(int limit) {
        // TODO: 实现获取最近登录用户逻辑
        throw new UnsupportedOperationException("方法未实现");
    }
}
