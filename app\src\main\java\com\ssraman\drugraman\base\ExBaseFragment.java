package com.ssraman.drugraman.base;

import android.os.Bundle;
import android.view.KeyEvent;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import com.ssraman.lib_common.base.BaseFragment;
import com.ssraman.lib_common.basemodel.BaseViewModel;

public abstract class ExBaseFragment<V extends ViewDataBinding, VM extends BaseViewModel> extends BaseFragment<V, VM> {

    @Override
    public void initParam() {
        super.initParam();
    }

    @Override
    public void initData() {
        super.initData();
    }

    @Override
    public void initToolbar() {
        super.initToolbar();
    }

    @Override
    public void initViewObservable() {
        super.initViewObservable();
    }

    @Override
    public void restoreInstanceState(@Nullable Bundle savedInstanceState) {
        super.restoreInstanceState(savedInstanceState);
    }

    @Override
    public void onContentReload() {
        super.onContentReload();
    }

}
