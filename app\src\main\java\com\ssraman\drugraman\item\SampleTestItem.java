package com.ssraman.drugraman.item;

import android.widget.Switch;

import com.ssraman.drugraman.R;

/**
 * @author: Administrator
 * @date: 2021/6/17
 */
public class SampleTestItem {
    private String name;
    private int ImageRes;
    private String libType;

    public SampleTestItem(String name, int imageIndex,int lib_type) {
        this.name = name;
        this.ImageRes = R.drawable.item_bg_lib_g;
        switch(lib_type)
        {
            case 0:
                this.libType="自定义";
                break;
            case 1:
                this.libType="标准";
                break;
            default:
                this.libType="自定义";
                break;
        }
    }


    public String getName() {
        return name;
    }

    public int getImageRes() {
        return ImageRes;
    }

    public String getLibType() {
        return libType;
    }

}
