package com.ssraman.drugraman.socket;

//import com.mls.baseProject.util.UIUtils;
//import com.orhanobut.logger.Logger;
//import com.raman.explode.util.NetUtils;

import android.util.Log;

import com.ssraman.drugraman.util.NetUtils;
import com.ssraman.lib_common.utils.ToastUtils;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by chenxiuxiang on 2018/1/25.
 */

public class MySocketServer {
    public final String TAG = getClass().getSimpleName();

    private boolean isEnable;
    private final WebConfig webConfig;//配置信息类
    private final ExecutorService threadPool;//线程池
    private ServerSocket serverSocket;
    private List<Socket> connectSockets;
    private Socket currentSocket;
    private ServerConnectInterface serverConnectInterface;


    public MySocketServer(WebConfig webConfig) {
        this.webConfig = webConfig;
        threadPool = Executors.newCachedThreadPool();
    }

    /**
     * 开启server
     */
    public void startServerAsync(ServerConnectInterface serverConnectInterface) {
        this.serverConnectInterface = serverConnectInterface;
        connectSockets = new ArrayList<>();
        isEnable = true;
        new Thread(() -> doProcSync()).start();
    }

    /**
     * 关闭server
     */
    public void stopServerAsync() throws IOException {
        if (!isEnable) {
            return;
        }
        isEnable = true;
        serverSocket.close();
        serverSocket = null;
    }

    private void doProcSync() {
        try {
            serverSocket = new ServerSocket();
            if (NetUtils.getLocalIPAddress() == null) {
                ToastUtils.showShortSafe("请打开Wifi");
                return;
            }
            String hostName = NetUtils.getLocalIPAddress().getHostName();
            Log.d(TAG,"hostName:" + hostName);
            serverSocket.bind(new InetSocketAddress(hostName, webConfig.getPort()));
            if (serverConnectInterface != null) {
                serverConnectInterface.success(serverSocket);
            }

            while (isEnable) {
                final Socket remotePeer = serverSocket.accept();
                connectSockets.add(remotePeer);
                threadPool.submit(() -> {
                    Log.d(TAG,"remotePeer..............." + remotePeer.getRemoteSocketAddress().toString());
                    onAcceptRemotePeer(remotePeer);
                });
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void onAcceptRemotePeer(Socket remotePeer) {
        currentSocket = remotePeer;
        try {
            remotePeer.getOutputStream().write("connected successful".getBytes());//告诉客户端连接成功
            // 从Socket当中得到InputStream对象
            InputStream inputStream = remotePeer.getInputStream();
            byte buffer[] = new byte[1024 * 4];
            int temp = 0;
            String receiveData = "";
            byte[] receiveByte = null;
            // 从InputStream当中读取客户端所发送的数据
            while ((temp = inputStream.read(buffer)) != -1) {
                receiveByte = new byte[temp];
                for (int i = 0; i < temp; i++) {
                    receiveByte[i] = buffer[i];
                }
//                receiveData = new String(buffer, 0, temp, "UTF-8");
//                byte[] bytes = TransitionUtil.hexStringToBytes(receiveData);
                if (this.serverConnectInterface != null) {
                    this.serverConnectInterface.onMessage(remotePeer, receiveByte);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    public List<Socket> getConnectSockets() {
        return connectSockets;
    }

    public Socket getCurrentSocket() {
        return currentSocket;
    }

    public ServerSocket getServerSocket() {
        return serverSocket;
    }


    public interface ServerConnectInterface {
        public void success(ServerSocket serverSocket);

        public void onMessage(Socket remoteSocket, byte[] message);
    }
}
