package com.ssraman.drugraman.item;

import com.ssraman.drugraman.R;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/6/17
 */
public class SampleItem {
    private String name;
    private int ImageRes;
    private int[] imageList=new int[]{R.drawable.item_other,R.drawable.item_other,R.drawable.item_other,R.drawable.item_other,R.drawable.item_other,R.drawable.item_other,R.drawable.item_other,R.drawable.item_other};

    public SampleItem() {
    }

    public SampleItem(String name, int imageIndex) {
        switch (imageIndex)
        {
            case 1:
                //this.name = "";
                this.name = name;
                ImageRes = imageList[1];
                break;
            case 2:
                //this.name = "";
                this.name = name;
                ImageRes = imageList[2];
                break;
            case 3:
                //this.name = "";
                this.name = name;
                ImageRes = imageList[3];
                break;
            case 4:
                //this.name = "";
                this.name = name;
                ImageRes = imageList[4];
                break;
            case 5:
                //this.name = "";
                this.name = name;
                ImageRes = imageList[5];
                break;
            case 6:
                //this.name = "";
                this.name = name;
                ImageRes = imageList[6];
                break;
            case 7:
                //this.name = "";
                this.name = name;
                ImageRes = imageList[7];
                break;
            default:
                this.name = name;
                ImageRes = imageList[0];
                break;
        }

    }

    public String getName() {
        return name;
    }

    public int getImageRes() {
        return ImageRes;
    }

    public List<SampleItem> getAllImage()
    {
        List<SampleItem> re_image_list = new ArrayList<>();
        for(int i=0;i<imageList.length;i++)
        {
            SampleItem t_sample_item = new SampleItem("",i);
            re_image_list.add(t_sample_item);
        }
        return re_image_list;
    }
}
