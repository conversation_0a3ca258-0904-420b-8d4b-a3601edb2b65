package com.ssraman.drugraman.rbac.security;

import com.ssraman.drugraman.rbac.entity.User;
import com.ssraman.drugraman.rbac.entity.Permission;
import com.ssraman.drugraman.rbac.entity.Role;

import java.util.List;
import java.util.Set;
import java.util.HashSet;

/**
 * 安全上下文管理器
 * 管理当前线程的用户信息和权限信息
 */
public class SecurityContext {
    
    private static final ThreadLocal<SecurityContextHolder> contextHolder = new ThreadLocal<>();
    
    /**
     * 设置当前用户
     * @param user 用户信息
     */
    public static void setCurrentUser(User user) {
        SecurityContextHolder holder = getOrCreateHolder();
        holder.user = user;
    }
    
    /**
     * 获取当前用户
     * @return 当前用户，如果未设置返回null
     */
    public static User getCurrentUser() {
        SecurityContextHolder holder = contextHolder.get();
        return holder != null ? holder.user : null;
    }
    
    /**
     * 获取当前用户ID
     * @return 当前用户ID，如果未设置返回null
     */
    public static Long getCurrentUserId() {
        User user = getCurrentUser();
        return user != null ? user.getId() : null;
    }
    
    /**
     * 获取当前用户名
     * @return 当前用户名，如果未设置返回null
     */
    public static String getCurrentUsername() {
        User user = getCurrentUser();
        return user != null ? user.getUsername() : null;
    }
    
    /**
     * 设置当前用户权限
     * @param permissions 权限列表
     */
    public static void setCurrentPermissions(List<Permission> permissions) {
        SecurityContextHolder holder = getOrCreateHolder();
        holder.permissions = permissions;
        
        // 同时更新权限代码集合
        if (permissions != null) {
            Set<String> permissionCodes = new HashSet<>();
            for (Permission permission : permissions) {
                if (permission.isEnabled()) {
                    permissionCodes.add(permission.getPermissionCode());
                }
            }
            holder.permissionCodes = permissionCodes;
        } else {
            holder.permissionCodes = null;
        }
    }
    
    /**
     * 获取当前用户权限
     * @return 权限列表，如果未设置返回null
     */
    public static List<Permission> getCurrentPermissions() {
        SecurityContextHolder holder = contextHolder.get();
        return holder != null ? holder.permissions : null;
    }
    
    /**
     * 获取当前用户权限代码集合
     * @return 权限代码集合，如果未设置返回null
     */
    public static Set<String> getCurrentPermissionCodes() {
        SecurityContextHolder holder = contextHolder.get();
        return holder != null ? holder.permissionCodes : null;
    }
    
    /**
     * 设置当前用户角色
     * @param roles 角色列表
     */
    public static void setCurrentRoles(List<Role> roles) {
        SecurityContextHolder holder = getOrCreateHolder();
        holder.roles = roles;
        
        // 同时更新角色代码集合和最高级别
        if (roles != null) {
            Set<String> roleCodes = new HashSet<>();
            int maxLevel = 0;
            for (Role role : roles) {
                if (role.isEnabled()) {
                    roleCodes.add(role.getRoleCode());
                    if (role.getLevel() != null && role.getLevel() > maxLevel) {
                        maxLevel = role.getLevel();
                    }
                }
            }
            holder.roleCodes = roleCodes;
            holder.maxRoleLevel = maxLevel;
        } else {
            holder.roleCodes = null;
            holder.maxRoleLevel = 0;
        }
    }
    
    /**
     * 获取当前用户角色
     * @return 角色列表，如果未设置返回null
     */
    public static List<Role> getCurrentRoles() {
        SecurityContextHolder holder = contextHolder.get();
        return holder != null ? holder.roles : null;
    }
    
    /**
     * 获取当前用户角色代码集合
     * @return 角色代码集合，如果未设置返回null
     */
    public static Set<String> getCurrentRoleCodes() {
        SecurityContextHolder holder = contextHolder.get();
        return holder != null ? holder.roleCodes : null;
    }
    
    /**
     * 获取当前用户最高角色级别
     * @return 最高角色级别
     */
    public static int getCurrentMaxRoleLevel() {
        SecurityContextHolder holder = contextHolder.get();
        return holder != null ? holder.maxRoleLevel : 0;
    }
    
    /**
     * 设置会话令牌
     * @param sessionToken 会话令牌
     */
    public static void setSessionToken(String sessionToken) {
        SecurityContextHolder holder = getOrCreateHolder();
        holder.sessionToken = sessionToken;
    }
    
    /**
     * 获取会话令牌
     * @return 会话令牌，如果未设置返回null
     */
    public static String getSessionToken() {
        SecurityContextHolder holder = contextHolder.get();
        return holder != null ? holder.sessionToken : null;
    }
    
    /**
     * 检查当前用户是否具有指定权限
     * @param permissionCode 权限代码
     * @return 是否具有权限
     */
    public static boolean hasPermission(String permissionCode) {
        Set<String> permissionCodes = getCurrentPermissionCodes();
        return permissionCodes != null && permissionCodes.contains(permissionCode);
    }
    
    /**
     * 检查当前用户是否具有指定角色
     * @param roleCode 角色代码
     * @return 是否具有角色
     */
    public static boolean hasRole(String roleCode) {
        Set<String> roleCodes = getCurrentRoleCodes();
        return roleCodes != null && roleCodes.contains(roleCode);
    }
    
    /**
     * 检查当前用户角色级别是否满足要求
     * @param requiredLevel 要求的最低级别
     * @return 是否满足级别要求
     */
    public static boolean hasRoleLevel(int requiredLevel) {
        return getCurrentMaxRoleLevel() >= requiredLevel;
    }
    
    /**
     * 检查是否已认证（是否有当前用户）
     * @return 是否已认证
     */
    public static boolean isAuthenticated() {
        return getCurrentUser() != null;
    }
    
    /**
     * 清除当前线程的安全上下文
     */
    public static void clear() {
        contextHolder.remove();
    }
    
    /**
     * 获取或创建上下文持有者
     * @return 上下文持有者
     */
    private static SecurityContextHolder getOrCreateHolder() {
        SecurityContextHolder holder = contextHolder.get();
        if (holder == null) {
            holder = new SecurityContextHolder();
            contextHolder.set(holder);
        }
        return holder;
    }
    
    /**
     * 安全上下文持有者内部类
     */
    private static class SecurityContextHolder {
        User user;
        List<Permission> permissions;
        Set<String> permissionCodes;
        List<Role> roles;
        Set<String> roleCodes;
        int maxRoleLevel;
        String sessionToken;
    }
}
