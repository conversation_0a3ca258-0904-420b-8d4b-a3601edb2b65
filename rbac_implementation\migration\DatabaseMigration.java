package com.ssraman.drugraman.rbac.migration;

import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.enums.PermissionType;

import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 数据库迁移工具类
 * 负责从旧权限系统迁移到新RBAC系统
 */
public class DatabaseMigration {
    
    private static final String TAG = "DatabaseMigration";
    private static final int CURRENT_VERSION = 2;
    private static final int OLD_VERSION = 1;
    
    /**
     * 执行数据库迁移
     * @param db 数据库实例
     * @param oldVersion 旧版本号
     * @param newVersion 新版本号
     */
    public static void migrate(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "开始数据库迁移，从版本 " + oldVersion + " 到版本 " + newVersion);
        
        try {
            if (oldVersion < 2) {
                migrateToVersion2(db);
            }
            
            Log.i(TAG, "数据库迁移完成");
        } catch (Exception e) {
            Log.e(TAG, "数据库迁移失败", e);
            throw new RuntimeException("数据库迁移失败", e);
        }
    }
    
    /**
     * 迁移到版本2（RBAC系统）
     * @param db 数据库实例
     */
    private static void migrateToVersion2(SQLiteDatabase db) {
        Log.i(TAG, "开始迁移到RBAC系统");
        
        // 1. 备份原始用户表
        backupOriginalUserTable(db);
        
        // 2. 创建新的权限系统表
        createNewTables(db);
        
        // 3. 初始化角色和权限数据
        initializeRolesAndPermissions(db);
        
        // 4. 迁移用户数据
        migrateUserData(db);
        
        // 5. 分配默认角色
        assignDefaultRoles(db);
        
        // 6. 清理临时数据
        cleanup(db);
        
        Log.i(TAG, "RBAC系统迁移完成");
    }
    
    /**
     * 备份原始用户表
     */
    private static void backupOriginalUserTable(SQLiteDatabase db) {
        Log.i(TAG, "备份原始用户表");
        
        String backupSql = "CREATE TABLE tb_uesr_backup AS SELECT * FROM tb_uesr";
        db.execSQL(backupSql);
    }
    
    /**
     * 创建新的权限系统表
     */
    private static void createNewTables(SQLiteDatabase db) {
        Log.i(TAG, "创建新的权限系统表");
        
        // 创建新用户表
        String createUserTable = """
            CREATE TABLE tb_user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                email TEXT,
                full_name TEXT,
                status INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                login_attempts INTEGER DEFAULT 0,
                locked_until DATETIME
            )
        """;
        db.execSQL(createUserTable);
        
        // 创建角色表
        String createRoleTable = """
            CREATE TABLE tb_role (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                role_code TEXT UNIQUE NOT NULL,
                role_name TEXT NOT NULL,
                description TEXT,
                level INTEGER NOT NULL,
                status INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """;
        db.execSQL(createRoleTable);
        
        // 创建权限表
        String createPermissionTable = """
            CREATE TABLE tb_permission (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                permission_code TEXT UNIQUE NOT NULL,
                permission_name TEXT NOT NULL,
                resource TEXT NOT NULL,
                action TEXT NOT NULL,
                description TEXT,
                status INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """;
        db.execSQL(createPermissionTable);
        
        // 创建用户角色关联表
        String createUserRoleTable = """
            CREATE TABLE tb_user_role (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                role_id INTEGER NOT NULL,
                assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                assigned_by INTEGER,
                expires_at DATETIME,
                status INTEGER DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES tb_user(id),
                FOREIGN KEY (role_id) REFERENCES tb_role(id)
            )
        """;
        db.execSQL(createUserRoleTable);
        
        // 创建角色权限关联表
        String createRolePermissionTable = """
            CREATE TABLE tb_role_permission (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                role_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                assigned_by INTEGER,
                status INTEGER DEFAULT 1,
                FOREIGN KEY (role_id) REFERENCES tb_role(id),
                FOREIGN KEY (permission_id) REFERENCES tb_permission(id)
            )
        """;
        db.execSQL(createRolePermissionTable);
        
        // 创建用户会话表
        String createUserSessionTable = """
            CREATE TABLE tb_user_session (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                device_info TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                status INTEGER DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES tb_user(id)
            )
        """;
        db.execSQL(createUserSessionTable);
        
        // 创建审计日志表
        String createAuditLogTable = """
            CREATE TABLE tb_audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                resource TEXT,
                details TEXT,
                ip_address TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES tb_user(id)
            )
        """;
        db.execSQL(createAuditLogTable);
        
        // 创建索引
        createIndexes(db);
    }
    
    /**
     * 创建数据库索引
     */
    private static void createIndexes(SQLiteDatabase db) {
        Log.i(TAG, "创建数据库索引");
        
        String[] indexes = {
            "CREATE INDEX idx_user_username ON tb_user(username)",
            "CREATE INDEX idx_user_status ON tb_user(status)",
            "CREATE INDEX idx_role_code ON tb_role(role_code)",
            "CREATE INDEX idx_permission_code ON tb_permission(permission_code)",
            "CREATE INDEX idx_user_role_user_id ON tb_user_role(user_id)",
            "CREATE INDEX idx_user_role_role_id ON tb_user_role(role_id)",
            "CREATE INDEX idx_role_permission_role_id ON tb_role_permission(role_id)",
            "CREATE INDEX idx_role_permission_permission_id ON tb_role_permission(permission_id)",
            "CREATE INDEX idx_user_session_user_id ON tb_user_session(user_id)",
            "CREATE INDEX idx_user_session_token ON tb_user_session(session_token)",
            "CREATE INDEX idx_audit_log_user_id ON tb_audit_log(user_id)",
            "CREATE INDEX idx_audit_log_timestamp ON tb_audit_log(timestamp)"
        };
        
        for (String index : indexes) {
            db.execSQL(index);
        }
    }
    
    /**
     * 初始化角色和权限数据
     */
    private static void initializeRolesAndPermissions(SQLiteDatabase db) {
        Log.i(TAG, "初始化角色和权限数据");
        
        // 插入角色数据
        insertRoles(db);
        
        // 插入权限数据
        insertPermissions(db);
        
        // 建立角色权限关联
        assignPermissionsToRoles(db);
    }
    
    /**
     * 插入角色数据
     */
    private static void insertRoles(SQLiteDatabase db) {
        String insertRoleSql = "INSERT INTO tb_role (role_code, role_name, description, level) VALUES (?, ?, ?, ?)";
        
        for (RoleType roleType : RoleType.values()) {
            db.execSQL(insertRoleSql, new Object[]{
                roleType.getCode(),
                roleType.getName(),
                roleType.getDescription(),
                roleType.getLevel()
            });
        }
    }
    
    /**
     * 插入权限数据
     */
    private static void insertPermissions(SQLiteDatabase db) {
        String insertPermissionSql = "INSERT INTO tb_permission (permission_code, permission_name, resource, action, description) VALUES (?, ?, ?, ?, ?)";
        
        for (PermissionType permissionType : PermissionType.values()) {
            db.execSQL(insertPermissionSql, new Object[]{
                permissionType.getCode(),
                permissionType.getName(),
                permissionType.getResource(),
                permissionType.getAction(),
                permissionType.getDescription()
            });
        }
    }
    
    /**
     * 为角色分配权限
     */
    private static void assignPermissionsToRoles(SQLiteDatabase db) {
        // 这里需要根据业务需求为每个角色分配相应的权限
        // 示例：为操作员分配基础权限
        assignPermissionsToRole(db, RoleType.OPERATOR.getCode(), new String[]{
            PermissionType.DETECTION_EXECUTE.getCode(),
            PermissionType.DETECTION_VIEW_RESULT.getCode(),
            PermissionType.DETECTION_EXPORT_BASIC.getCode()
        });
        
        // 为审核员分配权限（包含操作员权限 + 报告权限）
        assignPermissionsToRole(db, RoleType.REVIEWER.getCode(), new String[]{
            PermissionType.DETECTION_EXECUTE.getCode(),
            PermissionType.DETECTION_VIEW_RESULT.getCode(),
            PermissionType.DETECTION_EXPORT_BASIC.getCode(),
            PermissionType.REPORT_CREATE.getCode(),
            PermissionType.REPORT_PUBLISH.getCode(),
            PermissionType.REPORT_REVIEW.getCode(),
            PermissionType.REPORT_APPROVE.getCode(),
            PermissionType.REPORT_REJECT.getCode(),
            PermissionType.REPORT_SIGN.getCode(),
            PermissionType.ARCHIVE_VIEW.getCode(),
            PermissionType.ARCHIVE_SEARCH.getCode()
        });
        
        // 继续为其他角色分配权限...
    }
    
    /**
     * 为指定角色分配权限
     */
    private static void assignPermissionsToRole(SQLiteDatabase db, String roleCode, String[] permissionCodes) {
        String getRoleIdSql = "SELECT id FROM tb_role WHERE role_code = ?";
        String getPermissionIdSql = "SELECT id FROM tb_permission WHERE permission_code = ?";
        String insertRolePermissionSql = "INSERT INTO tb_role_permission (role_id, permission_id) VALUES (?, ?)";
        
        // 获取角色ID
        android.database.Cursor roleCursor = db.rawQuery(getRoleIdSql, new String[]{roleCode});
        if (!roleCursor.moveToFirst()) {
            roleCursor.close();
            return;
        }
        long roleId = roleCursor.getLong(0);
        roleCursor.close();
        
        // 为角色分配权限
        for (String permissionCode : permissionCodes) {
            android.database.Cursor permissionCursor = db.rawQuery(getPermissionIdSql, new String[]{permissionCode});
            if (permissionCursor.moveToFirst()) {
                long permissionId = permissionCursor.getLong(0);
                db.execSQL(insertRolePermissionSql, new Object[]{roleId, permissionId});
            }
            permissionCursor.close();
        }
    }
    
    /**
     * 迁移用户数据
     */
    private static void migrateUserData(SQLiteDatabase db) {
        Log.i(TAG, "迁移用户数据");
        
        String selectOldUsersSql = "SELECT id, login_name, login_pwd, priority, status FROM tb_uesr";
        String insertNewUserSql = "INSERT INTO tb_user (username, password_hash, salt, full_name, status, created_at) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
        
        android.database.Cursor cursor = db.rawQuery(selectOldUsersSql, null);
        
        while (cursor.moveToNext()) {
            String username = cursor.getString(1);
            String oldPassword = cursor.getString(2);
            int priority = cursor.getInt(3);
            String status = cursor.getString(4);
            
            // 生成新的密码哈希和盐值
            String salt = generateSalt();
            String passwordHash = hashPassword(oldPassword, salt);
            
            // 转换状态
            int newStatus = "active".equals(status) ? 1 : 0;
            
            db.execSQL(insertNewUserSql, new Object[]{
                username,
                passwordHash,
                salt,
                username, // 使用用户名作为全名
                newStatus
            });
        }
        
        cursor.close();
    }
    
    /**
     * 分配默认角色
     */
    private static void assignDefaultRoles(SQLiteDatabase db) {
        Log.i(TAG, "分配默认角色");
        
        String selectUsersSql = "SELECT u_new.id, u_old.priority FROM tb_user u_new JOIN tb_uesr u_old ON u_new.username = u_old.login_name";
        String insertUserRoleSql = "INSERT INTO tb_user_role (user_id, role_id, assigned_at) VALUES (?, ?, CURRENT_TIMESTAMP)";
        
        android.database.Cursor cursor = db.rawQuery(selectUsersSql, null);
        
        while (cursor.moveToNext()) {
            long userId = cursor.getLong(0);
            int priority = cursor.getInt(1);
            
            // 根据旧的priority映射到新的角色
            String roleCode = mapPriorityToRole(priority);
            long roleId = getRoleId(db, roleCode);
            
            if (roleId > 0) {
                db.execSQL(insertUserRoleSql, new Object[]{userId, roleId});
            }
        }
        
        cursor.close();
    }
    
    /**
     * 映射旧的priority到新的角色
     */
    private static String mapPriorityToRole(int priority) {
        switch (priority) {
            case 1: return RoleType.OPERATOR.getCode();
            case 2: return RoleType.REVIEWER.getCode();
            case 3: return RoleType.SYSTEM_ADMIN.getCode();
            default: return RoleType.OPERATOR.getCode(); // 默认为操作员
        }
    }
    
    /**
     * 获取角色ID
     */
    private static long getRoleId(SQLiteDatabase db, String roleCode) {
        String sql = "SELECT id FROM tb_role WHERE role_code = ?";
        android.database.Cursor cursor = db.rawQuery(sql, new String[]{roleCode});
        
        long roleId = 0;
        if (cursor.moveToFirst()) {
            roleId = cursor.getLong(0);
        }
        cursor.close();
        
        return roleId;
    }
    
    /**
     * 生成随机盐值
     */
    private static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[16];
        random.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }
    
    /**
     * 哈希密码
     */
    private static String hashPassword(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(salt.getBytes());
            byte[] hashedPassword = md.digest(password.getBytes());
            return Base64.getEncoder().encodeToString(hashedPassword);
        } catch (Exception e) {
            throw new RuntimeException("密码哈希失败", e);
        }
    }
    
    /**
     * 清理临时数据
     */
    private static void cleanup(SQLiteDatabase db) {
        Log.i(TAG, "清理临时数据");
        
        // 可以选择保留备份表用于回滚，或者删除以节省空间
        // db.execSQL("DROP TABLE tb_uesr_backup");
    }
}
