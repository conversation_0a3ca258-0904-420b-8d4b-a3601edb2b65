package com.ssraman.drugraman.business;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.newentiry.MatchResultNodeInfo;
import com.ssraman.drugraman.newentiry.SampleCollectionInfo;
import com.ssraman.drugraman.newentiry.SaveMatchResultNodeInfo;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/7/30
 */
public class SaveDataProcessUtil {

    public byte[] creatMatchResultStream(List<MatchResultNodeInfo> sourceMatchResult) throws Exception {
        List<SaveMatchResultNodeInfo> newSourceMatchResult = new ArrayList<>();
        for (MatchResultNodeInfo info : sourceMatchResult) {
            SaveMatchResultNodeInfo newnode = ConvertMatchResult(info);
            newSourceMatchResult.add(newnode);
        }
        byte[] buf = ObjectToByteArray(newSourceMatchResult);
        return buf;
    }

    public List<SaveMatchResultNodeInfo> restoreMatchResult(byte[] byteArray) throws Exception {
        if (byteArray == null)
            return null;
        List<SaveMatchResultNodeInfo> resultMatchResult = ByteArrayToObject(byteArray,new TypeToken<List<SaveMatchResultNodeInfo>>() {}.getType());
        return resultMatchResult;
    }

    private SaveMatchResultNodeInfo ConvertMatchResult(MatchResultNodeInfo info) {
        SaveMatchResultNodeInfo outfo = new SaveMatchResultNodeInfo();
        outfo.setCompareRate(info.getCompareRate());
        outfo.setExRatio(info.getExRatio());
        outfo.setEvaluationRate(info.getEvaluationRate());
        outfo.setStrRatio(info.getStrRatio());
        outfo.setComparePointList(info.getComparePointList());
        outfo.setCompareCount(info.getCompareCount());
        outfo.setMatchResult(info.getMatchResult());
        outfo.setNeiBiao(info.isNeiBiao());
        outfo.setNeibiaoValue(info.getNeibiaoValue());
        outfo.setSampleName(info.getSampleName());
        outfo.setFtId(info.getFtId());
        outfo.setFtSampleName(info.getFtSampleName());
        return outfo;
    }

    public MatchResultNodeInfo ConvertSaveMatchResult(SaveMatchResultNodeInfo info) {
        MatchResultNodeInfo outfo = new MatchResultNodeInfo();
        outfo.setCompareRate(info.getCompareRate());
        outfo.setExRatio(info.getExRatio());
        outfo.setEvaluationRate(info.getEvaluationRate());
        outfo.setStrRatio(info.getStrRatio());
        outfo.setComparePointList(info.getComparePointList());
        outfo.setCompareCount(info.getCompareCount());
        outfo.setMatchResult(info.getMatchResult());
        outfo.setNeiBiao(info.isNeiBiao());
        outfo.setNeibiaoValue(info.getNeibiaoValue());
        outfo.setSampleName(info.getSampleName());
        outfo.setFtId(info.getFtId());
        outfo.setFtSampleName(info.getFtSampleName());
        return outfo;
    }


    public byte[] creatSampleCollectionStream(SampleCollectionInfo sampleCollectionInfo) throws Exception {
        byte[] buf = ObjectToByteArray(sampleCollectionInfo);
        return buf;
    }

    public SampleCollectionInfo restoreSampleCollection(byte[] byteArray) throws Exception {
        if (byteArray == null)
            return null;
        SampleCollectionInfo sampleCollectionInfo = ByteArrayToObject(byteArray,SampleCollectionInfo.class);
        return sampleCollectionInfo;
    }

    public byte[] creatPeakListStream(List<PeakInfo> _peakList) throws Exception {
        List<PeakInfo> t_peaklist = new ArrayList<>();
        for (PeakInfo info : _peakList) {
            t_peaklist.add(info);
        }
        byte[] buf = ObjectToByteArray(_peakList);
        return buf;
    }

    public List<PeakInfo> restorePeakList(byte[] byteArray) throws Exception {
        if (byteArray == null)
            return null;
        List<PeakInfo> _peakList = ByteArrayToObject(byteArray,new TypeToken<List<PeakInfo>>() {}.getType());
        return _peakList;
    }

    private <T> byte[] ObjectToByteArray(T i_object) throws Exception {
        Gson gson = new GsonBuilder().create();
        String txt = gson.toJson(i_object);
        byte[] buf = txt.getBytes();
        return txt.getBytes();
    }

    private <T> T ByteArrayToObject(byte[] byteArray ,Type  classOfT) throws Exception {

            String txt = new String(byteArray);
            Gson gson = new GsonBuilder().create();
            T r_object = gson.fromJson(txt, classOfT);
            return r_object;

    }

}
