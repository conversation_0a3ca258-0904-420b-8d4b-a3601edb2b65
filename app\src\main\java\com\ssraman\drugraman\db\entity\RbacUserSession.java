package com.ssraman.drugraman.db.entity;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.ToOne;
import org.greenrobot.greendao.annotation.Unique;
import org.greenrobot.greendao.annotation.Generated;

import java.util.Date;

/**
 * RBAC用户会话实体类
 * 管理用户登录会话信息
 */
@Entity(nameInDb = "tb_rbac_user_session")
public class RbacUserSession {
    
    @Id(autoincrement = true)
    @Property(nameInDb = "id")
    private Long id;
    
    @Property(nameInDb = "user_id")
    private Long userId;
    
    @Property(nameInDb = "session_token")
    @Unique
    private String sessionToken;
    
    @Property(nameInDb = "device_info")
    private String deviceInfo;
    
    @Property(nameInDb = "created_at")
    private Date createdAt;
    
    @Property(nameInDb = "expires_at")
    private Date expiresAt;
    
    @Property(nameInDb = "status")
    private Integer status; // 0:已失效, 1:有效
    
    @ToOne(joinProperty = "userId")
    private RbacUser user;

    @Generated(hash = 1186799675)
    public RbacUserSession(Long id, Long userId, String sessionToken,
            String deviceInfo, Date createdAt, Date expiresAt, Integer status) {
        this.id = id;
        this.userId = userId;
        this.sessionToken = sessionToken;
        this.deviceInfo = deviceInfo;
        this.createdAt = createdAt;
        this.expiresAt = expiresAt;
        this.status = status;
    }

    @Generated(hash = 1906849798)
    public RbacUserSession() {
    }
    
    // 构造函数
    public RbacUserSession(Long userId, String sessionToken, String deviceInfo, Date expiresAt) {
        this.userId = userId;
        this.sessionToken = sessionToken;
        this.deviceInfo = deviceInfo;
        this.createdAt = new Date();
        this.expiresAt = expiresAt;
        this.status = 1; // 默认有效
    }
    
    // 业务方法
    public boolean isValid() {
        return status != null && status == 1 && !isExpired();
    }
    
    public boolean isExpired() {
        return expiresAt != null && expiresAt.before(new Date());
    }
    
    public void invalidate() {
        this.status = 0;
    }
    
    public void extend(int minutes) {
        this.expiresAt = new Date(System.currentTimeMillis() + minutes * 60 * 1000);
    }
    
    public long getMinutesUntilExpiration() {
        if (expiresAt == null) {
            return Long.MAX_VALUE;
        }
        long diffInMillies = expiresAt.getTime() - new Date().getTime();
        return diffInMillies / (60 * 1000);
    }

    // Getters and Setters
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSessionToken() {
        return this.sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    public String getDeviceInfo() {
        return this.deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public Date getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getExpiresAt() {
        return this.expiresAt;
    }

    public void setExpiresAt(Date expiresAt) {
        this.expiresAt = expiresAt;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public RbacUser getUser() {
        return this.user;
    }

    public void setUser(RbacUser user) {
        this.user = user;
    }
}
