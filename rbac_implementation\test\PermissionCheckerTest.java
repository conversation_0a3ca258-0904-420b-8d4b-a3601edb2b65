package com.ssraman.drugraman.rbac.test;

import com.ssraman.drugraman.rbac.entity.User;
import com.ssraman.drugraman.rbac.entity.Role;
import com.ssraman.drugraman.rbac.entity.Permission;
import com.ssraman.drugraman.rbac.entity.UserRole;
import com.ssraman.drugraman.rbac.entity.RolePermission;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.security.PermissionChecker;
import com.ssraman.drugraman.rbac.security.PermissionCache;
import com.ssraman.drugraman.rbac.service.IPermissionService;
import com.ssraman.drugraman.rbac.service.IRoleService;

import org.junit.Before;
import org.junit.Test;
import org.junit.After;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;
import java.util.Date;

import io.reactivex.Observable;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 权限检查器测试类
 * 测试权限验证的各种场景
 */
public class PermissionCheckerTest {
    
    @Mock
    private IPermissionService mockPermissionService;
    
    @Mock
    private IRoleService mockRoleService;
    
    private PermissionCache permissionCache;
    private PermissionChecker permissionChecker;
    
    // 测试数据
    private User testUser;
    private Role operatorRole;
    private Role reviewerRole;
    private Role systemAdminRole;
    private Permission detectionExecutePermission;
    private Permission reportCreatePermission;
    private Permission userCreatePermission;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 初始化权限缓存和检查器
        permissionCache = new PermissionCache(5); // 5分钟缓存
        permissionChecker = new PermissionChecker(mockPermissionService, mockRoleService, permissionCache);
        
        // 初始化测试数据
        setupTestData();
    }
    
    @After
    public void tearDown() {
        permissionCache.shutdown();
    }
    
    private void setupTestData() {
        // 创建测试用户
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setStatus(1); // 激活状态
        
        // 创建测试角色
        operatorRole = new Role();
        operatorRole.setId(1L);
        operatorRole.setRoleCode(RoleType.OPERATOR.getCode());
        operatorRole.setRoleName(RoleType.OPERATOR.getName());
        operatorRole.setLevel(RoleType.OPERATOR.getLevel());
        operatorRole.setStatus(1);
        
        reviewerRole = new Role();
        reviewerRole.setId(2L);
        reviewerRole.setRoleCode(RoleType.REVIEWER.getCode());
        reviewerRole.setRoleName(RoleType.REVIEWER.getName());
        reviewerRole.setLevel(RoleType.REVIEWER.getLevel());
        reviewerRole.setStatus(1);
        
        systemAdminRole = new Role();
        systemAdminRole.setId(3L);
        systemAdminRole.setRoleCode(RoleType.SYSTEM_ADMIN.getCode());
        systemAdminRole.setRoleName(RoleType.SYSTEM_ADMIN.getName());
        systemAdminRole.setLevel(RoleType.SYSTEM_ADMIN.getLevel());
        systemAdminRole.setStatus(1);
        
        // 创建测试权限
        detectionExecutePermission = new Permission();
        detectionExecutePermission.setId(1L);
        detectionExecutePermission.setPermissionCode(PermissionType.DETECTION_EXECUTE.getCode());
        detectionExecutePermission.setPermissionName(PermissionType.DETECTION_EXECUTE.getName());
        detectionExecutePermission.setResource(PermissionType.DETECTION_EXECUTE.getResource());
        detectionExecutePermission.setAction(PermissionType.DETECTION_EXECUTE.getAction());
        detectionExecutePermission.setStatus(1);
        
        reportCreatePermission = new Permission();
        reportCreatePermission.setId(2L);
        reportCreatePermission.setPermissionCode(PermissionType.REPORT_CREATE.getCode());
        reportCreatePermission.setPermissionName(PermissionType.REPORT_CREATE.getName());
        reportCreatePermission.setResource(PermissionType.REPORT_CREATE.getResource());
        reportCreatePermission.setAction(PermissionType.REPORT_CREATE.getAction());
        reportCreatePermission.setStatus(1);
        
        userCreatePermission = new Permission();
        userCreatePermission.setId(3L);
        userCreatePermission.setPermissionCode(PermissionType.USER_CREATE.getCode());
        userCreatePermission.setPermissionName(PermissionType.USER_CREATE.getName());
        userCreatePermission.setResource(PermissionType.USER_CREATE.getResource());
        userCreatePermission.setAction(PermissionType.USER_CREATE.getAction());
        userCreatePermission.setStatus(1);
    }
    
    @Test
    public void testHasPermission_WithValidPermission_ShouldReturnTrue() {
        // 准备测试数据
        List<Permission> userPermissions = Arrays.asList(detectionExecutePermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 执行测试
        boolean result = permissionChecker.hasPermission(testUser.getId(), PermissionType.DETECTION_EXECUTE.getCode());
        
        // 验证结果
        assertTrue("用户应该具有检测执行权限", result);
        verify(mockPermissionService, times(1)).getUserPermissions(testUser.getId());
    }
    
    @Test
    public void testHasPermission_WithInvalidPermission_ShouldReturnFalse() {
        // 准备测试数据
        List<Permission> userPermissions = Arrays.asList(detectionExecutePermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 执行测试
        boolean result = permissionChecker.hasPermission(testUser.getId(), PermissionType.USER_CREATE.getCode());
        
        // 验证结果
        assertFalse("用户不应该具有用户创建权限", result);
    }
    
    @Test
    public void testHasPermission_WithResourceAndAction_ShouldReturnTrue() {
        // 准备测试数据
        List<Permission> userPermissions = Arrays.asList(detectionExecutePermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 执行测试
        boolean result = permissionChecker.hasPermission(testUser.getId(), "DETECTION", "EXECUTE");
        
        // 验证结果
        assertTrue("用户应该具有检测执行权限", result);
    }
    
    @Test
    public void testHasRoleLevel_WithSufficientLevel_ShouldReturnTrue() {
        // 准备测试数据
        List<Role> userRoles = Arrays.asList(reviewerRole);
        when(mockRoleService.getUserRoles(testUser.getId()))
                .thenReturn(Observable.just(userRoles));
        
        // 执行测试
        boolean result = permissionChecker.hasRoleLevel(testUser.getId(), RoleType.OPERATOR.getLevel());
        
        // 验证结果
        assertTrue("审核员应该满足操作员级别要求", result);
    }
    
    @Test
    public void testHasRoleLevel_WithInsufficientLevel_ShouldReturnFalse() {
        // 准备测试数据
        List<Role> userRoles = Arrays.asList(operatorRole);
        when(mockRoleService.getUserRoles(testUser.getId()))
                .thenReturn(Observable.just(userRoles));
        
        // 执行测试
        boolean result = permissionChecker.hasRoleLevel(testUser.getId(), RoleType.SYSTEM_ADMIN.getLevel());
        
        // 验证结果
        assertFalse("操作员不应该满足系统管理员级别要求", result);
    }
    
    @Test
    public void testHasRole_WithValidRole_ShouldReturnTrue() {
        // 准备测试数据
        List<Role> userRoles = Arrays.asList(operatorRole);
        when(mockRoleService.getUserRoles(testUser.getId()))
                .thenReturn(Observable.just(userRoles));
        
        // 执行测试
        boolean result = permissionChecker.hasRole(testUser.getId(), RoleType.OPERATOR);
        
        // 验证结果
        assertTrue("用户应该具有操作员角色", result);
    }
    
    @Test
    public void testHasAnyPermission_WithOneValidPermission_ShouldReturnTrue() {
        // 准备测试数据
        List<Permission> userPermissions = Arrays.asList(detectionExecutePermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 执行测试
        boolean result = permissionChecker.hasAnyPermission(testUser.getId(), 
                PermissionType.DETECTION_EXECUTE.getCode(), 
                PermissionType.USER_CREATE.getCode());
        
        // 验证结果
        assertTrue("用户应该具有任一权限", result);
    }
    
    @Test
    public void testHasAllPermissions_WithAllValidPermissions_ShouldReturnTrue() {
        // 准备测试数据
        List<Permission> userPermissions = Arrays.asList(detectionExecutePermission, reportCreatePermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 执行测试
        boolean result = permissionChecker.hasAllPermissions(testUser.getId(), 
                PermissionType.DETECTION_EXECUTE.getCode(), 
                PermissionType.REPORT_CREATE.getCode());
        
        // 验证结果
        assertTrue("用户应该具有所有权限", result);
    }
    
    @Test
    public void testHasAllPermissions_WithMissingPermission_ShouldReturnFalse() {
        // 准备测试数据
        List<Permission> userPermissions = Arrays.asList(detectionExecutePermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 执行测试
        boolean result = permissionChecker.hasAllPermissions(testUser.getId(), 
                PermissionType.DETECTION_EXECUTE.getCode(), 
                PermissionType.USER_CREATE.getCode());
        
        // 验证结果
        assertFalse("用户不应该具有所有权限", result);
    }
    
    @Test
    public void testPermissionCache_ShouldReduceDatabaseCalls() {
        // 准备测试数据
        List<Permission> userPermissions = Arrays.asList(detectionExecutePermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 第一次调用
        boolean result1 = permissionChecker.hasPermission(testUser.getId(), PermissionType.DETECTION_EXECUTE.getCode());
        
        // 第二次调用（应该使用缓存）
        boolean result2 = permissionChecker.hasPermission(testUser.getId(), PermissionType.DETECTION_EXECUTE.getCode());
        
        // 验证结果
        assertTrue("第一次调用应该返回true", result1);
        assertTrue("第二次调用应该返回true", result2);
        
        // 验证只调用了一次数据库
        verify(mockPermissionService, times(1)).getUserPermissions(testUser.getId());
    }
    
    @Test
    public void testGetUserMaxRoleLevel_ShouldReturnHighestLevel() {
        // 准备测试数据
        List<Role> userRoles = Arrays.asList(operatorRole, reviewerRole, systemAdminRole);
        when(mockRoleService.getUserRoles(testUser.getId()))
                .thenReturn(Observable.just(userRoles));
        
        // 执行测试
        int maxLevel = permissionChecker.getUserMaxRoleLevel(testUser.getId());
        
        // 验证结果
        assertEquals("应该返回最高角色级别", RoleType.SYSTEM_ADMIN.getLevel(), maxLevel);
    }
    
    @Test
    public void testHasPermission_WithNullUserId_ShouldReturnFalse() {
        // 执行测试
        boolean result = permissionChecker.hasPermission(null, PermissionType.DETECTION_EXECUTE.getCode());
        
        // 验证结果
        assertFalse("空用户ID应该返回false", result);
    }
    
    @Test
    public void testHasPermission_WithNullPermissionCode_ShouldReturnFalse() {
        // 执行测试
        boolean result = permissionChecker.hasPermission(testUser.getId(), null);
        
        // 验证结果
        assertFalse("空权限代码应该返回false", result);
    }
    
    @Test
    public void testHasPermission_WithDisabledPermission_ShouldReturnFalse() {
        // 准备测试数据 - 禁用权限
        Permission disabledPermission = new Permission();
        disabledPermission.setId(4L);
        disabledPermission.setPermissionCode(PermissionType.DETECTION_EXECUTE.getCode());
        disabledPermission.setStatus(0); // 禁用状态
        
        List<Permission> userPermissions = Arrays.asList(disabledPermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 执行测试
        boolean result = permissionChecker.hasPermission(testUser.getId(), PermissionType.DETECTION_EXECUTE.getCode());
        
        // 验证结果
        assertFalse("禁用的权限应该返回false", result);
    }
    
    @Test
    public void testClearUserPermissionCache_ShouldForceReload() {
        // 准备测试数据
        List<Permission> userPermissions = Arrays.asList(detectionExecutePermission);
        when(mockPermissionService.getUserPermissions(testUser.getId()))
                .thenReturn(Observable.just(userPermissions));
        
        // 第一次调用建立缓存
        permissionChecker.hasPermission(testUser.getId(), PermissionType.DETECTION_EXECUTE.getCode());
        
        // 清除缓存
        permissionChecker.clearUserPermissionCache(testUser.getId());
        
        // 第二次调用应该重新查询数据库
        permissionChecker.hasPermission(testUser.getId(), PermissionType.DETECTION_EXECUTE.getCode());
        
        // 验证调用了两次数据库
        verify(mockPermissionService, times(2)).getUserPermissions(testUser.getId());
    }
}
