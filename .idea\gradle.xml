<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="GRADLE" />
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleHome" value="$USER_HOME$/.gradle/wrapper/dists/gradle-4.10.1-all/455itskqi2qtf0v2sja68alqd/gradle-4.10.1" />
        <option name="gradleJvm" value="1.8" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/android-pdf-viewer" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/control" />
            <option value="$PROJECT_DIR$/dialog" />
            <option value="$PROJECT_DIR$/jninative" />
            <option value="$PROJECT_DIR$/lib_common" />
            <option value="$PROJECT_DIR$/okgo" />
            <option value="$PROJECT_DIR$/printerlibs" />
            <option value="$PROJECT_DIR$/qrcodecore" />
            <option value="$PROJECT_DIR$/tableview" />
            <option value="$PROJECT_DIR$/update-app" />
            <option value="$PROJECT_DIR$/wifip2p" />
            <option value="$PROJECT_DIR$/wifip2papp" />
            <option value="$PROJECT_DIR$/zxing" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>