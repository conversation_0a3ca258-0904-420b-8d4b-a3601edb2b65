package com.ssraman.drugraman.typeface;

public enum OperationType {
    所有(0),
    更改文件(1),
    登出(2),
    登录(3),
    签名登录(4),
    签名登录失败(5),
    编辑用户(6),
    编辑用户签名(7),
    进行测量(8),
    删除谱图(9);

    private final int value;

    OperationType(int value) {
        this.value = value;
    }

    public int value() {
        return this.value;
    }

    public static OperationType valueOf(int value) {
        switch (value) {
            case 0:
                return 所有;
            case 1:
                return 更改文件;
            case 2:
                return 登出;
            case 3:
                return 登录;
            case 4:
                return 签名登录;
            case 5:
                return 签名登录失败;
            case 6:
                return 编辑用户;
            case 7:
                return 编辑用户签名;
            case 8:
                return 进行测量;
            case 9:
                return 删除谱图;
            default:
                return 所有;
        }
    }
}
