package com.ssraman.drugraman.typeface;

import androidx.annotation.IntDef;
import androidx.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @author: Administrator
 * @date: 2021/11/12
 */
public class SampleModeType {
    public static final String SAMPLEHOME = "复位值";// 复位值0
    public static final String SAMPLEDEFAULT = "默认值";// 默认值 50
    public static final String SAMPLETHICK = "厚包装";// 厚包装 100
    public static final String SAMPLETHIN = "薄包装";// 薄包装 150

    @StringDef({ SAMPLEDEFAULT, SAMPLETHICK, SAMPLETHIN })
    @Retention(RetentionPolicy.SOURCE) // 源码级别
    public @interface SampleMode_Enum {

    }

    public static String getSampleMode(@SampleMode_Enum String sample_mode) {
        switch (sample_mode) {
            case SAMPLEHOME:
                return "复位值";
            case SAMPLEDEFAULT:
                return "默认值";
            case SAMPLETHICK:
                return "厚包装";
            case SAMPLETHIN:
                return "薄包装";
        }
        return "复位值";
    }
}
