package com.ssraman.drugraman.newentiry;

import java.io.Serializable;

/**
 * @author: Administrator
 * @date: 2021/10/15
 */
public class CollectInfo implements Serializable {
    //物质类别
    private String material_type;
    //物质
    private String material_item;
    //评估方法 辨识 1、验证 0
    private int evaluation_mode;
    //样品名称
    private String sample_name="";
    //样品ID
    private long sample_Id;
    //序列号
    private String series_number="";
    //批次扫描次数
    private int batch;
    //批次扫描序号
    private int batch_no=0;
    //容器类别 厚0，薄1
    private String samlpe_controller;
    //积分时间
    private int integration_time;
    //激光功率
    private LaserInfo laser_power;
    //位置(货柜)
    private String container;
    //样品描述
    private String description;
    //样品形态
    private String sample_morphology;
    //比对方法
    private int core_method;
    //起始波数
    private int start_wave;
    //批次扫描模式
    private boolean Batch_mode;

    //批检测名称
    private String BatchName="";
    //样本容器
    private String SampleContainer;
    //批检测样品数量
    private Integer SampleNumber=0;

    private int samlpeController_index=0;
    private int integrationTime_index=2;
    private int integrationUnit_index=1;
    private int laserPower_index=4;
    private int coreMethod_index_v=0;
    private int coreMethod_index_nov=0;
    private int sampleContainer_index=0;

    private boolean reTest=false;

    public String getMaterial_type() {
        return material_type;
    }

    public void setMaterial_type(String material_type) {
        this.material_type = material_type;
    }

    public String getMaterial_item() {
        return material_item;
    }

    public void setMaterial_item(String material_item) {
        this.material_item = material_item;
    }

    public int getEvaluation_mode() {
        return evaluation_mode;
    }

    public void setEvaluation_mode(int evaluation_mode) {
        this.evaluation_mode = evaluation_mode;
    }

    public String getSample_name() {
        return sample_name;
    }

    public void setSample_name(String sample_name) {
        this.sample_name = sample_name;
    }

    public long getSample_Id() {
        return sample_Id;
    }

    public void setSample_Id(long sample_Id) {
        this.sample_Id = sample_Id;
    }

    public String getSeries_number() {
        return series_number;
    }

    public void setSeries_number(String series_number) {
        this.series_number = series_number;
    }

    public int getBatch() {
        return batch;
    }

    public void setBatch(int batch) {
        this.batch = batch;
    }

    public int getBatch_no() {
        return batch_no;
    }

    public void setBatch_no(int batch_no) {
        this.batch_no = batch_no;
    }

    public String getSamlpe_controller() {
        return samlpe_controller;
    }

    public void setSamlpe_controller(String samlpe_controller) {
        this.samlpe_controller = samlpe_controller;
    }

    public int getIntegration_time() {
        return integration_time;
    }

    public void setIntegration_time(int integration_time) {
        this.integration_time = integration_time;
    }

    public LaserInfo getLaser_power() {
        return laser_power;
    }

    public void setLaser_power(LaserInfo laser_power) {
        this.laser_power = laser_power;
    }

    public String getContainer() {
        return container;
    }

    public void setContainer(String container) {
        this.container = container;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSample_morphology() {
        return sample_morphology;
    }

    public void setSample_morphology(String sample_morphology) {
        this.sample_morphology = sample_morphology;
    }

    public int getCore_method() {
        return core_method;
    }

    public void setCore_method(int core_method) {
        this.core_method = core_method;
    }

    public int getStart_wave() {
        return start_wave;
    }

    public void setStart_wave(int start_wave) {
        this.start_wave = start_wave;
    }

    public boolean isBatch_mode() {
        return Batch_mode;
    }

    public void setBatch_mode(boolean batch_mode) {
        Batch_mode = batch_mode;
    }

    public int getSamlpeController_index() {
        return samlpeController_index;
    }

    public void setSamlpeController_index(int samlpeController_index) {
        this.samlpeController_index = samlpeController_index;
    }

    public int getIntegrationTime_index() {
        return integrationTime_index;
    }

    public void setIntegrationTime_index(int integrationTime_index) {
        this.integrationTime_index = integrationTime_index;
    }

    public int getIntegrationUnit_index() {
        return integrationUnit_index;
    }

    public void setIntegrationUnit_index(int integrationUnit_index) {
        this.integrationUnit_index = integrationUnit_index;
    }

    public int getLaserPower_index() {
        return laserPower_index;
    }

    public void setLaserPower_index(int laserPower_index) {
        this.laserPower_index = laserPower_index;
    }

    public int getCoreMethod_index() {
        if(evaluation_mode==0)
           return coreMethod_index_v;
        else
            return coreMethod_index_nov;
    }

    public void setCoreMethod_index(int coreMethod_index) {
        if(evaluation_mode==0)
        this.coreMethod_index_v = coreMethod_index;
        else
            this.coreMethod_index_nov=coreMethod_index;
    }

    public String getBatchName() {
        return BatchName;
    }

    public void setBatchName(String batchName) {
        BatchName = batchName;
    }

    public String getSampleContainer() {
        return SampleContainer;
    }

    public void setSampleContainer(String sampleContainer) {
        SampleContainer = sampleContainer;
    }

    public Integer getSampleNumber() {
        return SampleNumber;
    }

    public void setSampleNumber(Integer sampleNumber) {
        SampleNumber = sampleNumber;
    }

    public boolean isReTest() {
        return reTest;
    }

    public void setReTest(boolean reTest) {
        this.reTest = reTest;
    }

    public int getSampleContainer_index() {
        return sampleContainer_index;
    }

    public void setSampleContainer_index(int sampleContainer_index) {
        this.sampleContainer_index = sampleContainer_index;
    }
}
