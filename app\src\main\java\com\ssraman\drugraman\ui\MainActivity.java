package com.ssraman.drugraman.ui;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.view.GravityCompat;
import androidx.databinding.DataBindingUtil;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Color;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.os.SystemClock;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;

import android.widget.TextView;

import com.google.android.material.navigation.NavigationView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.gson.Gson;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.kongzue.dialog.interfaces.OnDialogButtonClickListener;
import com.kongzue.dialog.interfaces.OnDismissListener;
import com.kongzue.dialog.util.BaseDialog;
import com.kongzue.dialog.util.MessageBoxIcon;
import com.kongzue.dialog.v3.CustomDialog;
import com.kongzue.dialog.v3.MessageDialog;
import com.kongzue.dialog.v3.TipDialog;
import com.kongzue.dialog.v3.WaitDialog;
import com.ssraman.control.customview.BatteryView;
import com.ssraman.control.spinner.MaterialSpinner;
import com.ssraman.drugraman.BR;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.BuildConfig;
import com.ssraman.drugraman.base.GlobalViewModel;
import com.ssraman.drugraman.business.MyCustomException;
import com.ssraman.drugraman.common.CommonParameter;
import com.ssraman.drugraman.common.SendBroadcast;
import com.ssraman.drugraman.common.ioService;
import com.ssraman.drugraman.constant.Constants;
import com.ssraman.drugraman.constant.InterFaceConst;
import com.ssraman.drugraman.constant.MqttConstant;
import com.ssraman.drugraman.databinding.ActivityMainBinding;
import com.ssraman.drugraman.databinding.NavHeaderMainBinding;
import com.ssraman.drugraman.db.entity.User_info;
import com.ssraman.drugraman.network.entity.request.VersionRequest;
import com.ssraman.drugraman.network.entity.response.VersionResponse;
import com.ssraman.drugraman.network.http.OkGoUpdateHttpUtil;
import com.ssraman.drugraman.typeface.OperationType;
import com.ssraman.drugraman.ui.detection.CollectInfoFragment;
import com.ssraman.drugraman.ui.detection.DetectionResultFragment;
import com.ssraman.drugraman.ui.detection.DetectionSpecFragment;
import com.ssraman.drugraman.ui.other.AddLibCollectInfoFragment;
import com.ssraman.drugraman.ui.other.AddLibDetectionSpecFragment;
import com.ssraman.drugraman.ui.other.CalibrationFragment;
import com.ssraman.drugraman.ui.other.DevicePassthroughFragment;
import com.ssraman.drugraman.ui.vm.MainViewModel;
import com.ssraman.drugraman.util.LauncherUtils;
import com.ssraman.drugraman.util.OperatingAuthority;
import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.util.PermissionUtils;
import com.ssraman.drugraman.rbac.manager.RbacManager;
import com.ssraman.drugraman.rbac.ui.RbacLoginManager;
import com.ssraman.drugraman.rbac.ui.UiPermissionController;
import com.ssraman.drugraman.rbac.dto.AuthResult;
import com.ssraman.lib_common.base.BaseActivity;
import com.ssraman.lib_common.mac.DrawerSwichBean;
import com.ssraman.lib_common.mac.LoginStatusBean;
import com.ssraman.lib_common.mac.OrientationSwichBean;
import com.ssraman.lib_common.mac.SettingPre;
import com.ssraman.lib_common.mac.SvrStatusBean;
import com.ssraman.lib_common.mac.TimeSwichBean;
import com.ssraman.lib_common.mac.ToolBarSwichBean;
import com.ssraman.lib_common.utils.SubscribeInterface;
import com.ssraman.lib_common.utils.SubscribeWholeInterface;
import com.ssraman.lib_common.utils.TimeoutService;
import com.ssraman.lib_common.utils.ToastUtils;
import com.ssraman.lib_common.utils.Utils;
import com.vector.update_app.UpdateAppBean;
import com.vector.update_app.UpdateAppManager;
import com.vector.update_app.UpdateCallback;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

import static com.ssraman.lib_common.utils.Utils.getContext;

public class MainActivity extends BaseActivity<ActivityMainBinding, MainViewModel> {
    private AppCompatActivity me = null;
    private LauncherUtils LauncherUtil;

    private BatteryView verticalBattery;
    private Thread batteryAnimationThread; // 用于控制电池动画的线程
    private boolean isAnimating = false; // 标记电池动画是否正在进行中

    private DrawerLayout mDrawer;
    private NavigationView mNavigationView;
    private Toolbar mToolbar;
    private int drawer_swich_id = 0;
    private boolean connet_svr_status = false;

    private TimeoutService ts;
    private boolean time_disenable = false;
    private boolean isLandscape;
    private boolean first_read = true;
    private int read_time = 3;

    // 需要的权限
    public int numbPermissions = 8;
    // private String[] NEED_PERMISSIONS =
    // {Manifest.permission.WRITE_EXTERNAL_STORAGE,
    // Manifest.permission.READ_CONTACTS};
    private String[] NEED_PERMISSIONS;
    private static final int NEED_PERMISSIONS_CODE = 231;

    private ResolveInfo appInfo[] = new ResolveInfo[2];

    private SendBroadcast laserControl = null;

    // RBAC相关组件
    private RbacLoginManager rbacLoginManager;
    private UiPermissionController uiPermissionController;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        me = this;
        ts = new TimeoutService();
        LauncherUtil = new LauncherUtils(this);
        NEED_PERMISSIONS = new String[numbPermissions];
        NEED_PERMISSIONS[0] = android.Manifest.permission.ACCESS_NETWORK_STATE;
        NEED_PERMISSIONS[1] = android.Manifest.permission.ACCESS_WIFI_STATE;
        NEED_PERMISSIONS[2] = android.Manifest.permission.CHANGE_WIFI_STATE;
        NEED_PERMISSIONS[3] = android.Manifest.permission.INTERNET;
        NEED_PERMISSIONS[4] = android.Manifest.permission.READ_EXTERNAL_STORAGE;
        NEED_PERMISSIONS[5] = android.Manifest.permission.CHANGE_NETWORK_STATE;
        NEED_PERMISSIONS[6] = android.Manifest.permission.WRITE_EXTERNAL_STORAGE;

        NEED_PERMISSIONS[7] = android.Manifest.permission.READ_CONTACTS;
        requestPermission();
        setUp();

        appInfo[0] = LauncherUtil.getPackageName(Constants.appStr3);

        verticalBattery = (BatteryView) findViewById(R.id.verticalBattery);
        verticalBattery.setColor(Color.GREEN);
        // 注册一个接受广播类型
        registerReceiver(batteryChangedReceiver, new IntentFilter(Intent.ACTION_BATTERY_CHANGED));
        StartServices();// 开启电源板服务
        laserControl = SendBroadcast.getInstance(me);
        laserControl.sendOpenPower();
        laserControl.sendOpenIO();

        initDeviceId();
    }

    private void readUID() {

        // 尝试3次
        int MAX_RETRY = 3;
        Disposable disposable = Observable.create(new ObservableOnSubscribe<Object>() {
            @Override
            public void subscribe(ObservableEmitter<Object> emitter) throws Exception {
                int retry = 0;
                while (retry < MAX_RETRY) {
                    if (emitter.isDisposed()) {
                        return;
                    }
                    emitter.onNext(retry);
                    retry++;
                    Thread.sleep(1000);
                }
                emitter.onComplete();
            }
        }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(retry -> {
                    if (retry instanceof Integer) {
                        int r = (int) retry;
                        if (r == 0) {
                            viewModel.readDeviceUID(read_time, new SubscribeWholeInterface() {
                                @Override
                                public void onSubscribe() {
                                    viewModel.setIsLoading(true);
                                    Utils.postDelayed(() -> {
                                        viewModel.setIsLoading(false);
                                    }, 10000);
                                }

                                @Override
                                public void onNext(Object data) {

                                }

                                @Override
                                public void onComplete() {
                                    // InterFaceConst.is_Read_Param_Flag = true;
                                    viewModel.setIsLoading(false);
                                }

                                @Override
                                public void onError(Throwable e) {
                                    viewModel.setIsLoading(false);
                                    // if (e instanceof MyCustomException) {
                                    // MyCustomException myCustomException = (MyCustomException) e;
                                    //
                                    // switch (myCustomException.getCode()) {
                                    // case 201:
                                    // //MessageDialog.show(me, "自动校准失败", myCustomException.getDisplayMessage(),
                                    // "确定", MessageBoxIcon.Error);
                                    // break;
                                    // }
                                    // } else {
                                    // //MessageDialog.show(me, "自动校准失败1", e.getMessage(), "确定",
                                    // MessageBoxIcon.Error);
                                    // }
                                    // 当失败的时候,重试3次

                                }
                            });
                        }
                    }
                });

        // viewModel.readDeviceUID(read_time, new SubscribeWholeInterface() {
        // @Override
        // public void onSubscribe() {
        // viewModel.setIsLoading(true);
        // Utils.postDelayed(() -> {
        // viewModel.setIsLoading(false);
        // }, 10000);
        // }
        //
        // @Override
        // public void onNext(Object data) {
        //
        // }
        //
        // @Override
        // public void onComplete() {
        // //InterFaceConst.is_Read_Param_Flag = true;
        // viewModel.setIsLoading(false);
        // }
        //
        // @Override
        // public void onError(Throwable e) {
        // viewModel.setIsLoading(false);
        //// if (e instanceof MyCustomException) {
        //// MyCustomException myCustomException = (MyCustomException) e;
        ////
        //// switch (myCustomException.getCode()) {
        //// case 201:
        //// //MessageDialog.show(me, "自动校准失败", myCustomException.getDisplayMessage(),
        // "确定", MessageBoxIcon.Error);
        //// break;
        //// }
        //// } else {
        //// //MessageDialog.show(me, "自动校准失败1", e.getMessage(), "确定",
        // MessageBoxIcon.Error);
        //// }
        // // 当失败的时候,重试3次
        //
        // }
        // });
    }

    @Override
    public void initParam() {
        super.initParam();
    }

    @Override
    public int initContentView(Bundle savedInstanceState) {
        return R.layout.activity_main;
    }

    @Override
    public int initVariableId() {
        return BR.mainViewModel;
    }

    @Override
    public void initData() {
        super.initData();

        viewModel.shareViewModel = new ViewModelProvider(this).get(GlobalViewModel.class);

        viewModel.openIoPower(this);

        LiveEventBus.get("toolbar_swich", ToolBarSwichBean.class)
                .observe(this, new Observer<ToolBarSwichBean>() {
                    @Override
                    public void onChanged(ToolBarSwichBean toolBarSwichBean) {
                        menu_swich_id = toolBarSwichBean.getSwich_id_value();
                        if (menu_swich_id == 0) {
                            unlockDrawer();
                        } else {
                            lockDrawer();
                        }
                        invalidateOptionsMenu();

                    }
                });

        LiveEventBus.get("drawer_swich", DrawerSwichBean.class)
                .observe(this, new Observer<DrawerSwichBean>() {
                    @Override
                    public void onChanged(DrawerSwichBean drawerSwichBean) {
                        drawer_swich_id = drawerSwichBean.getSwich_id_value();
                    }
                });

        LiveEventBus.get("svr_swich", SvrStatusBean.class)
                .observe(this, new Observer<SvrStatusBean>() {
                    @Override
                    public void onChanged(SvrStatusBean svrStatusBean) {
                        if (svrStatusBean.getStatus_id_value() == 1) {
                            connet_svr_status = true;
                            MqttConstant.connet_svr_status = true;
                        } else {
                            connet_svr_status = false;
                            MqttConstant.connet_svr_status = false;
                        }
                        invalidateOptionsMenu();
                    }
                });
        LiveEventBus.get("login_swich", LoginStatusBean.class)
                .observe(this, new Observer<LoginStatusBean>() {
                    @Override
                    public void onChanged(LoginStatusBean loginStatusBean) {
                        if (loginStatusBean.getStatus_id_value() == 1) {
                            NavController navController = Navigation.findNavController(MainActivity.this,
                                    R.id.fragment1);
                            navController.navigate(R.id.mainFragment);
                            viewModel.shareViewModel.post_Login_data(new User_info("", "", 0, ""));
                        }
                    }
                });
        LiveEventBus.get("time_swich", TimeSwichBean.class)
                .observe(this, new Observer<TimeSwichBean>() {
                    @Override
                    public void onChanged(TimeSwichBean timeSwichBean) {
                        if (timeSwichBean.getStatus_id_value() == 1) {
                            time_disenable = true;
                            if (ts != null) {
                                ts.stop();
                            }
                        } else {
                            time_disenable = false;
                        }
                    }
                });

        viewModel.shareViewModel.getDetectioning();

        binding.tvAppVersion.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                continuousClick(COUNTS, DURATION);
            }
        });

        viewModel.getMacAdress(this);
        viewModel.connectMqttService();

        if (!InterFaceConst.is_Read_Param_Flag) {
            readUID();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        getMenuInflater().inflate(R.menu.main, menu);
        return true;
    }

    int menu_swich_id = 0;

    @Override
    public boolean onPrepareOptionsMenu(@NonNull Menu menu) {
        // 动态设置ToolBar状态
        switch (menu_swich_id) {
            case 0:
                menu.findItem(R.id.action_back).setVisible(false);
                menu.findItem(R.id.action_home).setVisible(true);
                menu.findItem(R.id.action_logout).setVisible(false);
                break;
            case 1:
                menu.findItem(R.id.action_back).setVisible(true);
                menu.findItem(R.id.action_home).setVisible(true);
                menu.findItem(R.id.action_logout).setVisible(false);
                break;
            case 2:
                menu.findItem(R.id.action_back).setVisible(false);
                menu.findItem(R.id.action_home).setVisible(false);
                menu.findItem(R.id.action_logout).setVisible(false);
                break;
            case 3:
                menu.findItem(R.id.action_back).setVisible(false);
                menu.findItem(R.id.action_home).setVisible(true);
                menu.findItem(R.id.action_logout).setVisible(false);
                break;
        }
        menu.findItem(R.id.action_connet_svr)
                .setIcon(connet_svr_status ? ContextCompat.getDrawable(this, R.drawable.bar_lan_connected_48)
                        : ContextCompat.getDrawable(this, R.drawable.bar_lan_connect_48));

        super.onPrepareOptionsMenu(menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        Drawable drawable = item.getIcon();
        if (drawable instanceof Animatable) {
            ((Animatable) drawable).start();
        }

        if (viewModel.shareViewModel.getDetectioning().getValue()) {
            ToastUtils.showLongSafe("正在检测中，请等待！");
            return true;
        }
        NavController navController = Navigation.findNavController(this, R.id.fragment1);
        switch (item.getItemId()) {
            case R.id.action_connet_svr:
                if (connet_svr_status) {
                    // 断开MQTT服务器
                    MessageDialog.build(this)
                            .setTitle("提示")
                            .setMessage("是否确定要断开MQTT服务器连接！")
                            .setOkButton("确定", new OnDialogButtonClickListener() {
                                @Override
                                public boolean onClick(BaseDialog baseDialog, View v) {
                                    viewModel.disconnectMqttService();
                                    return false;
                                }
                            })
                            .setCancelButton("取消", new OnDialogButtonClickListener() {
                                @Override
                                public boolean onClick(BaseDialog baseDialog, View v) {
                                    return false;
                                }
                            })
                            .show();
                } else {
                    // 连接MQTT服务器
                    viewModel.connectMqttService();
                }

                return true;
            case R.id.action_back:
                navController.popBackStack();
                return true;
            case R.id.action_home:
                navController.navigate(R.id.mainFragment);
                return true;
            case R.id.action_logout:
                MessageDialog.build(this)
                        .setTitle("提示")
                        .setMessage("是否确定要关闭设备！")
                        .setOkButton("确定", new OnDialogButtonClickListener() {
                            @Override
                            public boolean onClick(BaseDialog baseDialog, View v) {
                                viewModel.closeIoPower();
                                viewModel.setIsLoading(true);
                                try {
                                    Runtime.getRuntime().exec("reboot -p"); // 关机
                                } catch (IOException e) {
                                    Log.e("MainActivity", "关机失败");
                                    e.printStackTrace();
                                }

                                return false;
                            }
                        })
                        .setCancelButton("取消", new OnDialogButtonClickListener() {
                            @Override
                            public boolean onClick(BaseDialog baseDialog, View v) {

                                return false;
                            }
                        })
                        .show();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!InterFaceConst.is_Read_Param_Flag) {
            readUID();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 确保停止电池动画线程
        stopBatteryAnimation();

        // 注销电池状态广播接收器
        try {
            unregisterReceiver(batteryChangedReceiver);
        } catch (Exception e) {
            // 处理可能的异常，如接收器未注册
        }

        StopServices();// 关闭电源板服务
        viewModel.closeMqttService();
    }

    private void lockDrawer() {
        if (mDrawer != null) {
            mDrawer.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        }
    }

    private void unlockDrawer() {
        if (mDrawer != null) {
            mDrawer.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED);
        }
    }

    private void setUp() {
        mDrawer = binding.drawerView;
        mToolbar = binding.toolbar;
        mNavigationView = binding.navigationView;

        setSupportActionBar(mToolbar);

        // 初始化RBAC权限管理器
        RbacManager.getInstance(this);

        // 初始化RBAC登录管理器和UI权限控制器
        rbacLoginManager = new RbacLoginManager(this);
        uiPermissionController = new UiPermissionController();
        ActionBarDrawerToggle mDrawerToggle = new ActionBarDrawerToggle(
                this,
                mDrawer,
                mToolbar,
                R.string.open_drawer,
                R.string.close_drawer) {
            @Override
            public void onDrawerClosed(View drawerView) {
                super.onDrawerClosed(drawerView);
            }

            @Override
            public void onDrawerOpened(View drawerView) {
                super.onDrawerOpened(drawerView);
                viewModel.getUserData();
                hideKeyboard();
            }
        };
        mDrawerToggle.setDrawerIndicatorEnabled(false);// 侧滑菜单默认图标
        mToolbar.setNavigationIcon(R.drawable.homepersonal);
        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (drawer_swich_id == 0) {
                    mDrawer.openDrawer(GravityCompat.START);
                }
            }
        });
        viewModel.getUserData();
        mDrawer.addDrawerListener(mDrawerToggle);
        mDrawerToggle.syncState();
        setupNavMenu();
        String version = getString(R.string.version) + " " + BuildConfig.VERSION_NAME;
        viewModel.updateAppVersion(version);
        viewModel.updateUserName("未登陆");
        viewModel.updateUserDescription("");
        viewModel.shareViewModel.get_Login_data().observe(this, new Observer<User_info>() {
            @Override
            public void onChanged(User_info user_info) {
                if (user_info == null || user_info.getLogin_name().trim().equals("")) {
                    viewModel.updateUserName("未登陆");
                    viewModel.updateUserDescription("");
                    mToolbar.setTitle("未登陆");
                } else {
                    viewModel.updateUserName(user_info.getLogin_name());
                    viewModel.updateUserDescription(OperatingAuthority.valueOf(user_info.getPriority()).name());
                    mToolbar.setTitle(user_info.getLogin_name());
                }
            }
        });
    }

    private void setupNavMenu() {
        NavHeaderMainBinding navHeaderMainBinding = DataBindingUtil.inflate(getLayoutInflater(),
                R.layout.nav_header_main, binding.navigationView, false);
        binding.navigationView.addHeaderView(navHeaderMainBinding.getRoot());
        navHeaderMainBinding.setMainViewModel(viewModel);

        mNavigationView.setNavigationItemSelectedListener(
                item -> {
                    mDrawer.closeDrawer(GravityCompat.START);
                    NavController navController = Navigation.findNavController(MainActivity.this, R.id.fragment1);
                    Bundle args = null;
                    switch (item.getItemId()) {
                        case R.id.navItemLogin:// 登录
                            CustomDialog.build(me, R.layout.layout_login, new CustomDialog.OnBindView() {
                                @Override
                                public void onBind(final CustomDialog dialog, View v) {
                                    TextView txt_title = v.findViewById(R.id.txt_login_title);
                                    TextInputEditText etPassword = v.findViewById(R.id.etPassword);
                                    MaterialSpinner etuser = v.findViewById(R.id.etuser);
                                    TextView btnCancel = v.findViewById(R.id.btn_login_cancel);
                                    TextView btnOk = v.findViewById(R.id.btn_login_ok);

                                    txt_title.setText("用户登录");
                                    etPassword.setText("");
                                    List<String> user_name_list = viewModel.all_user_name_list.getValue();
                                    Log.d(TAG, user_name_list.toString());
                                    if ((user_name_list != null) && (user_name_list.size() > 0)) {
                                        etuser.setItems(user_name_list);
                                        etuser.setSelectedIndex(0);
                                    }
                                    Log.d(TAG, etuser.getItems().toString());
                                    btnOk.setOnClickListener(new View.OnClickListener() {
                                        @Override
                                        public void onClick(View v) {
                                            String user_name = etuser.getText().toString();
                                            String password = etPassword.getText().toString();
                                            if (viewModel.isUserNameValid(user_name)) {
                                                hideKeyboard();
                                                WaitDialog.show(me, "登陆中，请稍候...");

                                                // 使用新的RBAC登录管理器
                                                AuthResult authResult = rbacLoginManager.login(user_name, password);

                                                WaitDialog.dismiss();

                                                if (authResult.isSuccess()) {
                                                    // 登录成功，更新UI
                                                    viewModel.updateUserName(authResult.getUser().getUsername());
                                                    viewModel.updateUserDescription(rbacLoginManager.getCurrentUserRoleDescription());
                                                    mToolbar.setTitle(authResult.getUser().getUsername());

                                                    // 应用UI权限控制
                                                    uiPermissionController.applyAllViewPermissions();

                                                    TipDialog.show(me, "登陆成功！", TipDialog.TYPE.SUCCESS)
                                                            .setOnDismissListener(new OnDismissListener() {
                                                                @Override
                                                                public void onDismiss() {
                                                                    // 可以在这里添加登录成功后的逻辑
                                                                }
                                                            });
                                                    dialog.doDismiss();
                                                } else {
                                                    // 登录失败，显示错误信息
                                                    etPassword.setText("");
                                                    MessageDialog.show(me, "登录失败", authResult.getMessage(), "确定", MessageBoxIcon.Information);
                                                }
                                            } else {
                                                ToastUtils.showShortSafe("用户名不能为空！");
                                            }
                                        }
                                    });
                                    btnCancel.setOnClickListener(new View.OnClickListener() {
                                        @Override
                                        public void onClick(View v) {
                                            dialog.doDismiss();
                                        }
                                    });
                                }
                            }).setAlign(CustomDialog.ALIGN.DEFAULT).setCancelable(false).show();
                            return true;
                        case R.id.navItemSpeclib:// 谱图管理
                            // 检查谱图库管理权限
                            if (PermissionUtils.checkPermissionWithMessage(me, PermissionType.LIBRARY_MANAGE, "您没有管理谱图库的权限")) {
                                args = new Bundle();
                                args.putString("name", "");
                                args.putLong("parentId", 0);
                                args.putInt("type", 0);
                                navController.navigate(R.id.libraryMenuFragment, args);
                            }
                            return true;
                        case R.id.navItemUsermanage:// 用户管理
                            // 检查用户管理权限
                            if (PermissionUtils.checkPermissionWithMessage(me, PermissionType.USER_VIEW, "您没有查看用户的权限")) {
                                args = new Bundle();
                                navController.navigate(R.id.userManageFragment, args);
                            }
                            return true;
                        case R.id.navItemLogmanage:// 操作日志
                            // 检查日志查看权限
                            if (PermissionUtils.checkPermissionWithMessage(me, PermissionType.LOG_VIEW, "您没有查看日志的权限")) {
                                args = new Bundle();
                                navController.navigate(R.id.operatorRecordFragment, args);
                            }
                            return true;
                        case R.id.navItemFileManagement:// 资源管理器
                            LauncherUtil.startApplication(appInfo[0]);
                            return true;
                        case R.id.navItemOperationHelp:// 操作帮助
                            args = new Bundle();
                            navController.navigate(R.id.operatingManualFragment, args);
                            return true;
                        case R.id.navItemUpdateApp:// 软件更新
                            update_myapp();
                            return true;
                        case R.id.navItemAbout:// 关于
                            args = new Bundle();
                            navController.navigate(R.id.aboutFragment, args);
                            return true;
                        case R.id.navItemLogout:// 退出
                            // 使用新的RBAC登录管理器进行登出
                            if (rbacLoginManager.isLoggedIn()) {
                                String currentUserName = rbacLoginManager.getCurrentUserDisplayName();
                                rbacLoginManager.logout();

                                // 更新UI
                                viewModel.updateUserName("未登陆");
                                viewModel.updateUserDescription("");
                                mToolbar.setTitle("未登陆");

                                // 清除UI权限控制
                                uiPermissionController.clearAllPermissions();

                                // 记录登出日志
                                if (currentUserName != null) {
                                    viewModel.insertLog(currentUserName, OperationType.登出.value(), "");
                                }
                            }
                            return true;
                        default:
                            return false;
                    }
                });
    }

    long cilcked_time = 0;
    int click_count = 0;
    final static int COUNTS = 7;// 点击次数
    final static long DURATION = 2000;
    final static long MAX_DURATION = 5000;
    long[] mHits;

    private void continuousClick(int count, long time) {
        if (mHits == null) {
            mHits = new long[COUNTS];// 重新初始化数组
            click_count = 0;
            cilcked_time = SystemClock.uptimeMillis();
        }
        System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
        mHits[mHits.length - 1] = SystemClock.uptimeMillis();
        click_count++;

        if (cilcked_time <= (SystemClock.uptimeMillis() - MAX_DURATION)) {
            mHits = null;
            return;
        }

        long click_millis = SystemClock.uptimeMillis() - DURATION;
        if ((mHits[0] != 0) && (mHits[0] >= click_millis)) {
            mHits = null; // 这里说明一下，我们在进来以后需要还原状态，否则如果点击过快，第六次，第七次 都会不断进来触发该效果。重新开始计数即可
            // Toast.makeText(this, "连续点击了7次", Toast.LENGTH_LONG).show();
            appInfo[1] = LauncherUtil.getLauncherPackageName(Constants.oldHome);
            LauncherUtil.startLauncherApplication(appInfo[1]);
        } else {
            if (mHits[0] != 0) {
                if (click_count >= COUNTS) {
                    mHits = null;
                    return;
                }
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Log.d(TAG, "onKeyDown: " + keyCode);
        boolean ret = false;
        ret = activityParseOnkey(keyCode);
        if (!ret) {
            Fragment mMainNavFragment = getSupportFragmentManager().findFragmentById(R.id.fragment1);

            Fragment fragment = mMainNavFragment.getChildFragmentManager().getPrimaryNavigationFragment();
            if (fragment instanceof CollectInfoFragment) {
                ret = ((CollectInfoFragment) fragment).onKeyDown(keyCode, event);
            }
            if (fragment instanceof DetectionResultFragment) {
                ret = ((DetectionResultFragment) fragment).onKeyDown(keyCode, event);
            }
            if (fragment instanceof DetectionSpecFragment) {
                ret = ((DetectionSpecFragment) fragment).onKeyDown(keyCode, event);
            }
            if (fragment instanceof AddLibCollectInfoFragment) {
                ret = ((AddLibCollectInfoFragment) fragment).onKeyDown(keyCode, event);
            }
            if (fragment instanceof AddLibDetectionSpecFragment) {
                ret = ((AddLibDetectionSpecFragment) fragment).onKeyDown(keyCode, event);
            }
            if (fragment instanceof CalibrationFragment) {
                ret = ((CalibrationFragment) fragment).onKeyDown(keyCode, event);
            }
            if (fragment instanceof DevicePassthroughFragment) {
                ret = ((DevicePassthroughFragment) fragment).onKeyDown(keyCode, event);
            } else {
                ret = true;
            }
        }
        return ret;
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        boolean ret = false;
        ret = activityParseOnkey(keyCode);
        if (!ret) {
            Fragment mMainNavFragment = getSupportFragmentManager().findFragmentById(R.id.fragment1);

            Fragment fragment = mMainNavFragment.getChildFragmentManager().getPrimaryNavigationFragment();
            if (fragment instanceof CollectInfoFragment) {
                ret = ((CollectInfoFragment) fragment).onKeyUp(keyCode, event);
            }
            if (fragment instanceof DetectionResultFragment) {
                ret = ((DetectionResultFragment) fragment).onKeyUp(keyCode, event);
            }
            if (fragment instanceof DetectionSpecFragment) {
                ret = ((DetectionSpecFragment) fragment).onKeyUp(keyCode, event);
            }
            if (fragment instanceof AddLibCollectInfoFragment) {
                ret = ((AddLibCollectInfoFragment) fragment).onKeyUp(keyCode, event);
            }
            if (fragment instanceof AddLibDetectionSpecFragment) {
                ret = ((AddLibDetectionSpecFragment) fragment).onKeyUp(keyCode, event);
            }
            if (fragment instanceof CalibrationFragment) {
                ret = ((CalibrationFragment) fragment).onKeyUp(keyCode, event);
            }
            if (fragment instanceof DevicePassthroughFragment) {
                ret = ((DevicePassthroughFragment) fragment).onKeyUp(keyCode, event);
            } else {
                ret = true;
            }
        }
        return ret;
    }

    private boolean activityParseOnkey(int keyCode) {
        boolean ret = false;
        Log.d(TAG, "activityParseOnkey: " + keyCode);
        switch (keyCode) {
            case KeyEvent.KEYCODE_VOLUME_UP:// 24
            case KeyEvent.KEYCODE_VOLUME_DOWN:// 25
            case KeyEvent.KEYCODE_DPAD_UP:// 19
            case KeyEvent.KEYCODE_BACK:
            case KeyEvent.KEYCODE_F1:
                break;
            default:
                ret = true;
                break;
        }
        return ret;
    }

    // 接受广播
    // private BroadcastReceiver batteryChangedReceiver = new BroadcastReceiver() {
    //
    // public void onReceive(Context context, Intent intent) {
    // if (Intent.ACTION_BATTERY_CHANGED.equals(intent.getAction())) {
    // int level = intent.getIntExtra("level", 0);
    // int scale = intent.getIntExtra("scale", 100);
    // //tvBatteryChanged.setText("电池电量：" + (level * 100 / scale) + "%");
    // verticalBattery.setPower(level * 100 / scale);
    // }
    // }
    // };

    private BroadcastReceiver batteryChangedReceiver = new BroadcastReceiver() {

        public void onReceive(Context context, Intent intent) {
            if (Intent.ACTION_BATTERY_CHANGED.equals(intent.getAction())) {
                int level = intent.getIntExtra("level", 0);
                int scale = intent.getIntExtra("scale", 100);
                int status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1);

                if (status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL) {
                    // 如果正在充电或已充满，则显示动态电池
                    if (!isAnimating) {
                        animateBattery(level * 100 / scale);
                    }
                } else {
                    // 如果未充电，则显示静态电池
                    // 停止任何可能正在运行的动画
                    stopBatteryAnimation();
                    verticalBattery.setPower(level * 100 / scale);
                }
            }
        }
    };

    private void stopBatteryAnimation() {
        isAnimating = false;
        if (batteryAnimationThread != null) {
            batteryAnimationThread.interrupt();
            batteryAnimationThread = null;
        }
    }

    private void animateBattery(int level) {
        // 停止之前的动画（如果有）
        stopBatteryAnimation();

        // 创建新的动画线程
        isAnimating = true;
        batteryAnimationThread = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    int startValue = level;
                    while (isAnimating && !Thread.currentThread().isInterrupted()) {
                        // 电量从当前值平滑变化
                        for (int i = 0; i <= 100 && isAnimating; i++) {
                            if (Thread.currentThread().isInterrupted())
                                break;

                            final int currentLevel = (startValue + i) % 101; // 循环变化
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    if (isAnimating) {
                                        verticalBattery.setPower(currentLevel);
                                    }
                                }
                            });
                            Thread.sleep(50); // 调整动画速度，让效果更平滑
                        }
                    }
                } catch (InterruptedException e) {
                    // 线程被中断，正常退出
                } finally {
                    isAnimating = false;
                }
            }
        });
        batteryAnimationThread.start();
    }

    // 申请权限
    private void requestPermission() {
        // 当API大于 23 时，才动态申请权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean checkPermissionResult = true;
            for (int i = 0; i < NEED_PERMISSIONS.length; i++) {
                if (checkSelfPermission(NEED_PERMISSIONS[i]) != PackageManager.PERMISSION_GRANTED) {
                    checkPermissionResult = false;
                    break;
                }
            }
            if (!checkPermissionResult) {
                ActivityCompat.requestPermissions(MainActivity.this, NEED_PERMISSIONS, NEED_PERMISSIONS_CODE);
            }
        }
    }

    /**
     * 用户权限 申请 的回调方法
     *
     * @param requestCode
     * @param permissions
     * @param grantResults
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
            @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case NEED_PERMISSIONS_CODE:
                // 权限请求失败
                if (grantResults.length == NEED_PERMISSIONS.length) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        for (int i = 0; i < grantResults.length; i++) {
                            if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                                // 弹出对话框引导用户去设置
                                showDialogTipUserGoToAppSettting(permissions[i]);
                                break;
                            }
                        }
                    }
                } else {
                }
                break;
        }
    }

    /**
     * 提示用户去应用设置界面手动开启权限
     */
    private void showDialogTipUserGoToAppSettting(String title) {
        MessageDialog.build(this)
                .setTitle("提示")
                .setMessage("请在-应用设置-权限-中，允许应用使用此权限")
                .setOkButton("立即开启", new OnDialogButtonClickListener() {
                    @Override
                    public boolean onClick(BaseDialog baseDialog, View v) {
                        // 跳转到应用设置界面
                        goToAppSetting();
                        return false;
                    }
                })
                .setCancelButton("取消", new OnDialogButtonClickListener() {
                    @Override
                    public boolean onClick(BaseDialog baseDialog, View v) {
                        finish();
                        return false;
                    }
                })
                .show();
    }

    // 跳转到当前应用的设置界面
    private void goToAppSetting() {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getPackageName(), null);
        intent.setData(uri);
        startActivity(intent);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        // 权限管理
        if (requestCode == NEED_PERMISSIONS_CODE) {
            if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                for (int i = 0; i < NEED_PERMISSIONS.length; i++) {
                    // 检查该权限是否已经获取
                    // 权限是否已经 授权 GRANTED---授权 DINIED---拒绝
                    if (checkSelfPermission(NEED_PERMISSIONS[i]) != PackageManager.PERMISSION_GRANTED) {
                        // 提示用户应该去应用设置界面手动开启权限
                        showDialogTipUserGoToAppSettting(NEED_PERMISSIONS[i]);
                    } else {

                    }
                }
            }
        }
    }

    /////////////////////////////////////////////////////////////////////////////////

    private ArrayList<FragmentTouchListener> mFragmentTouchListeners = new ArrayList<>();

    public void registerFragmentTouchListener(FragmentTouchListener listener) {
        mFragmentTouchListeners.add(listener);
    }

    public void unRegisterFragmentTouchListener(FragmentTouchListener listener) {
        mFragmentTouchListeners.remove(listener);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        for (FragmentTouchListener listener : mFragmentTouchListeners) {
            listener.onTouchEvent(event);
        }
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                Log.i("dispatchTouchEvent", "按下");
                if (!time_disenable) {
                    if (ts != null) {
                        ts.stop();
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
                Log.i("dispatchTouchEvent", "抬起");
                if (!time_disenable) {
                    if (ts != null) {
                        ts.start();
                    }
                }
                break;
        }
        return super.dispatchTouchEvent(event);
    }

    public interface FragmentTouchListener {

        boolean onTouchEvent(MotionEvent event);
    }

    private void update_myapp() {
        Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) throws Exception {
                getVersion();
                emitter.onNext(true);
                emitter.onComplete();
            }
        }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new io.reactivex.Observer<Boolean>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(Boolean aBoolean) {

                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showShortSafe("更新仪器软件异常出错！" + e.getMessage());
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    private void getVersion() {
        VersionRequest versionRequest = new VersionRequest();
        versionRequest.setDeviceUid(SettingPre.getDeviceId());
        versionRequest.setModuleCode("appexplode");
        versionRequest.setVersion(Utils.getAppVersionName(this));
        String params = new Gson().toJson(versionRequest);
        String mUpdateUrl = SettingPre.getHost() + "/api/extra/v1/device/softwareUpdate";
        new UpdateAppManager.Builder()
                // 必须设置，当前Activity
                .setActivity(this)
                // 必须设置，实现httpManager接口的对象
                .setHttpManager(new OkGoUpdateHttpUtil())
                // 必须设置，更新地址
                .setUpdateUrl(mUpdateUrl)
                .setParamsJson(params)
                // 以下设置，都是可选
                // 设置请求方式，默认get
                .setPost(true)
                // 不显示通知栏进度条
                // .dismissNotificationProgress()
                // 是否忽略版本
                // .showIgnoreVersion()

                // 设置点击升级后，消失对话框，默认点击升级后，对话框显示下载进度
                .hideDialogOnDownloading(false)
                // 设置头部，不设置显示默认的图片，设置图片后自动识别主色调，然后为按钮，进度条设置颜色
                .setTopPic(R.mipmap.top_3)
                // 为按钮，进度条设置颜色。
                .setThemeColor(0xffffac5d)
                // 设置apk下砸路径，默认是在下载到sd卡下/Download/1.0.0/test.apk
                // .setTargetPath(path)
                // 设置appKey，默认从AndroidManifest.xml获取，如果，使用自定义参数，则此项无效
                // .setAppKey("ab55ce55Ac4bcP408cPb8c1Aaeac179c5f6f")

                .build()
                // 检测是否有新版本
                .checkNewApp(new UpdateCallback() {
                    /**
                     * 解析json,自定义协议
                     * 
                     * @param json 服务器返回的json
                     * @return UpdateAppBean
                     */
                    @Override
                    protected UpdateAppBean parseJson(String json) {
                        Log.d("update_app", "version:" + json);
                        UpdateAppBean updateAppBean = new UpdateAppBean();
                        try {
                            VersionResponse versionResponse = new Gson().fromJson(json, VersionResponse.class);
                            String update = "No";
                            boolean force = false;
                            if (versionResponse.getState().equals(CommonParameter.ERROR)) {

                            } else {
                                if (versionResponse.getState().equals(CommonParameter.SUCCESS)) {
                                    force = false;
                                }
                                if (versionResponse.getState().equals(CommonParameter.SUCCESS)) {
                                    update = "Yes";
                                }

                                updateAppBean
                                        // （必须）是否更新Yes,No
                                        .setUpdate(update)
                                        // （必须）新版本号，
                                        .setNewVersion(versionResponse.getData().getFileVersion())
                                        // （必须）下载地址
                                        .setApkFileUrl(versionResponse.getData().getFileUrl())
                                        // 测试内容过度
                                        .setUpdateLog(versionResponse.getData().getDescription())
                                        // .setUpdateLog("今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说相对于其他行业来说今天我们来聊一聊程序员枯燥的编程生活，相对于其他行业来说\r\n")
                                        // 是否强制更新，可以不设置
                                        .setConstraint(force);
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        return updateAppBean;
                    }

                    @Override
                    protected void hasNewApp(UpdateAppBean updateApp, UpdateAppManager updateAppManager) {
                        updateAppManager.showDialogFragment();
                    }
                });

    }

    /////////////////////////////////

    // 开启服务
    private void StartServices() {
        Intent timeService = new Intent(this, ioService.class);
        startService(timeService);
    }

    // 关闭服务
    private void StopServices() {
        stopService(new Intent(this, ioService.class));// 停止服务
    }

    private void initDeviceId() {
        if (TextUtils.isEmpty(SettingPre.getDeviceId())) {
            String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
            if (!TextUtils.isEmpty(androidId)) {
                SettingPre.setDeviceId("Drug_" + androidId);
            } else {
                SettingPre.setDeviceId("Drug_" + UUID.randomUUID().toString());
            }
        }
    }
}