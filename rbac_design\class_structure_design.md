# RBAC权限系统类结构设计

## 1. 实体类 (Entity Classes)

### User 实体
```java
@Entity(nameInDb = "tb_user")
public class User {
    @Id(autoincrement = true)
    private Long id;
    
    @Property(nameInDb = "username")
    @Unique
    private String username;
    
    @Property(nameInDb = "password_hash")
    private String passwordHash;
    
    @Property(nameInDb = "salt")
    private String salt;
    
    @Property(nameInDb = "email")
    private String email;
    
    @Property(nameInDb = "full_name")
    private String fullName;
    
    @Property(nameInDb = "status")
    private UserStatus status;
    
    @Property(nameInDb = "created_at")
    private Date createdAt;
    
    @Property(nameInDb = "updated_at")
    private Date updatedAt;
    
    @Property(nameInDb = "last_login")
    private Date lastLogin;
    
    @ToMany(referencedJoinProperty = "userId")
    private List<UserRole> userRoles;
}
```

### Role 实体
```java
@Entity(nameInDb = "tb_role")
public class Role {
    @Id(autoincrement = true)
    private Long id;
    
    @Property(nameInDb = "role_code")
    @Unique
    private String roleCode;
    
    @Property(nameInDb = "role_name")
    private String roleName;
    
    @Property(nameInDb = "description")
    private String description;
    
    @Property(nameInDb = "level")
    private Integer level;
    
    @Property(nameInDb = "status")
    private RoleStatus status;
    
    @Property(nameInDb = "created_at")
    private Date createdAt;
    
    @Property(nameInDb = "updated_at")
    private Date updatedAt;
    
    @ToMany(referencedJoinProperty = "roleId")
    private List<RolePermission> rolePermissions;
}
```

### Permission 实体
```java
@Entity(nameInDb = "tb_permission")
public class Permission {
    @Id(autoincrement = true)
    private Long id;
    
    @Property(nameInDb = "permission_code")
    @Unique
    private String permissionCode;
    
    @Property(nameInDb = "permission_name")
    private String permissionName;
    
    @Property(nameInDb = "resource")
    private String resource;
    
    @Property(nameInDb = "action")
    private String action;
    
    @Property(nameInDb = "description")
    private String description;
    
    @Property(nameInDb = "status")
    private PermissionStatus status;
    
    @Property(nameInDb = "created_at")
    private Date createdAt;
    
    @Property(nameInDb = "updated_at")
    private Date updatedAt;
}
```

### UserRole 关联实体
```java
@Entity(nameInDb = "tb_user_role")
public class UserRole {
    @Id(autoincrement = true)
    private Long id;
    
    @Property(nameInDb = "user_id")
    private Long userId;
    
    @Property(nameInDb = "role_id")
    private Long roleId;
    
    @Property(nameInDb = "assigned_at")
    private Date assignedAt;
    
    @Property(nameInDb = "assigned_by")
    private Long assignedBy;
    
    @Property(nameInDb = "expires_at")
    private Date expiresAt;
    
    @ToOne(joinProperty = "userId")
    private User user;
    
    @ToOne(joinProperty = "roleId")
    private Role role;
}
```

### UserSession 会话实体
```java
@Entity(nameInDb = "tb_user_session")
public class UserSession {
    @Id(autoincrement = true)
    private Long id;
    
    @Property(nameInDb = "user_id")
    private Long userId;
    
    @Property(nameInDb = "session_token")
    @Unique
    private String sessionToken;
    
    @Property(nameInDb = "device_info")
    private String deviceInfo;
    
    @Property(nameInDb = "created_at")
    private Date createdAt;
    
    @Property(nameInDb = "expires_at")
    private Date expiresAt;
    
    @Property(nameInDb = "status")
    private SessionStatus status;
    
    @ToOne(joinProperty = "userId")
    private User user;
}
```

## 2. 枚举类 (Enum Classes)

### UserStatus 枚举
```java
public enum UserStatus {
    ACTIVE(1, "激活"),
    INACTIVE(0, "未激活"),
    LOCKED(-1, "锁定"),
    DELETED(-2, "已删除");
    
    private final int code;
    private final String description;
    
    UserStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
```

### RoleType 枚举
```java
public enum RoleType {
    OPERATOR("OPERATOR", "操作员", 1),
    REVIEWER("REVIEWER", "审核员", 2),
    SPECTRUM_MANAGER("SPECTRUM_MANAGER", "谱图管理员", 3),
    USER_ADMIN("USER_ADMIN", "用户管理员", 4),
    SYSTEM_ADMIN("SYSTEM_ADMIN", "系统管理员", 5);
    
    private final String code;
    private final String name;
    private final int level;
    
    RoleType(String code, String name, int level) {
        this.code = code;
        this.name = name;
        this.level = level;
    }
}
```

### PermissionType 枚举
```java
public enum PermissionType {
    // 检测相关权限
    DETECTION_EXECUTE("DETECTION_EXECUTE", "执行检测", "DETECTION", "EXECUTE"),
    DETECTION_VIEW_RESULT("DETECTION_VIEW_RESULT", "查看检测结果", "DETECTION", "VIEW"),
    
    // 报告相关权限
    REPORT_CREATE("REPORT_CREATE", "创建报告", "REPORT", "CREATE"),
    REPORT_PUBLISH("REPORT_PUBLISH", "发布报告", "REPORT", "PUBLISH"),
    REPORT_REVIEW("REPORT_REVIEW", "复审报告", "REPORT", "REVIEW"),
    
    // 谱图相关权限
    SPECTRUM_CREATE("SPECTRUM_CREATE", "创建谱图", "SPECTRUM", "CREATE"),
    SPECTRUM_EDIT("SPECTRUM_EDIT", "编辑谱图", "SPECTRUM", "EDIT"),
    SPECTRUM_DELETE("SPECTRUM_DELETE", "删除谱图", "SPECTRUM", "DELETE"),
    
    // 用户相关权限
    USER_CREATE("USER_CREATE", "创建用户", "USER", "CREATE"),
    USER_EDIT("USER_EDIT", "编辑用户", "USER", "EDIT"),
    USER_DELETE("USER_DELETE", "删除用户", "USER", "DELETE"),
    
    // 系统相关权限
    SYSTEM_CONFIG("SYSTEM_CONFIG", "系统配置", "SYSTEM", "CONFIG"),
    SYSTEM_BACKUP("SYSTEM_BACKUP", "系统备份", "SYSTEM", "BACKUP");
    
    private final String code;
    private final String name;
    private final String resource;
    private final String action;
    
    PermissionType(String code, String name, String resource, String action) {
        this.code = code;
        this.name = name;
        this.resource = resource;
        this.action = action;
    }
}
```

## 3. 服务接口 (Service Interfaces)

### IUserService 用户服务接口
```java
public interface IUserService {
    // 用户认证
    AuthResult authenticate(String username, String password);
    
    // 用户管理
    User createUser(CreateUserRequest request);
    User updateUser(Long userId, UpdateUserRequest request);
    boolean deleteUser(Long userId);
    User getUserById(Long userId);
    User getUserByUsername(String username);
    List<User> getAllUsers();
    
    // 密码管理
    boolean changePassword(Long userId, String oldPassword, String newPassword);
    boolean resetPassword(Long userId, String newPassword);
    
    // 用户状态管理
    boolean activateUser(Long userId);
    boolean deactivateUser(Long userId);
    boolean lockUser(Long userId);
}
```

### IRoleService 角色服务接口
```java
public interface IRoleService {
    // 角色管理
    Role createRole(CreateRoleRequest request);
    Role updateRole(Long roleId, UpdateRoleRequest request);
    boolean deleteRole(Long roleId);
    Role getRoleById(Long roleId);
    Role getRoleByCode(String roleCode);
    List<Role> getAllRoles();
    
    // 用户角色关联
    boolean assignRoleToUser(Long userId, Long roleId, Long assignedBy);
    boolean revokeRoleFromUser(Long userId, Long roleId);
    List<Role> getUserRoles(Long userId);
    List<User> getRoleUsers(Long roleId);
}
```

### IPermissionService 权限服务接口
```java
public interface IPermissionService {
    // 权限管理
    Permission createPermission(CreatePermissionRequest request);
    Permission updatePermission(Long permissionId, UpdatePermissionRequest request);
    boolean deletePermission(Long permissionId);
    Permission getPermissionById(Long permissionId);
    Permission getPermissionByCode(String permissionCode);
    List<Permission> getAllPermissions();
    
    // 角色权限关联
    boolean assignPermissionToRole(Long roleId, Long permissionId);
    boolean revokePermissionFromRole(Long roleId, Long permissionId);
    List<Permission> getRolePermissions(Long roleId);
    
    // 用户权限查询
    List<Permission> getUserPermissions(Long userId);
    boolean hasPermission(Long userId, String permissionCode);
    boolean hasPermission(Long userId, String resource, String action);
}
```

## 4. 权限验证相关类

### PermissionChecker 权限检查器
```java
public class PermissionChecker {
    public boolean checkPermission(Long userId, String permissionCode);
    public boolean checkPermission(Long userId, String resource, String action);
    public boolean checkRoleLevel(Long userId, int requiredLevel);
    public List<String> getUserPermissionCodes(Long userId);
}
```

### SecurityContext 安全上下文
```java
public class SecurityContext {
    private static final ThreadLocal<User> currentUser = new ThreadLocal<>();
    private static final ThreadLocal<List<Permission>> currentPermissions = new ThreadLocal<>();
    
    public static void setCurrentUser(User user);
    public static User getCurrentUser();
    public static void setCurrentPermissions(List<Permission> permissions);
    public static List<Permission> getCurrentPermissions();
    public static void clear();
}
```
