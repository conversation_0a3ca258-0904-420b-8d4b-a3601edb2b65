package com.ssraman.drugraman.newentiry;

import androidx.annotation.NonNull;

import java.io.Serializable;

/**
 * @author: Administrator
 * @date: 2021/6/29
 */
public class LaserInfo implements Serializable {
    private String power_name;
    private int power_value;

    public LaserInfo(String power_name, int power_value) {
        this.power_name = power_name;
        this.power_value = power_value;
    }

    public String getPower_name() {
        return power_name;
    }

    public void setPower_name(String power_name) {
        this.power_name = power_name;
    }

    public int getPower_value() {
        return power_value;
    }

    public void setPower_value(int power_value) {
        this.power_value = power_value;
    }

    @NonNull
    @Override
    public String toString() {
        return power_name.toString();
    }
}
