package com.ssraman.drugraman.common;


import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;



/**
 * Created by Administrator on 2018/7/24.
 */

public class iotrue extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
       /* String  iowritereply=intent.getExtras().getString("ramantimetrue");

        if (iowritereply.equals("true")){
            Log.d(TAG, "返回数据成功！！---true");
            SettingPre.setAccept("true");
        }*/
       // SettingPre.setAccept("true");
//        if(intent.getAction().equals("com.raman.timeflagtrue")) {
//            SettingPre.setIsOpen(true);
//            Log.d("IOopen", "返回数据成功！！---true");
//        }
        if(intent.getAction().equals("RET")) {
            CommonParameter.OPENBroadcast=true;
            CommonParameter.BroadcastStatus=true;
            CommonParameter.BroadcastStatus2=0;
            //SettingPre.setIsOpen(true);
            Log.d("IOopen", "返回数据成功！！---true");
        }
    }
}
