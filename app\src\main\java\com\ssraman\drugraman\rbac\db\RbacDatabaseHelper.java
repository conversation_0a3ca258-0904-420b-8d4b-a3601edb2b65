package com.ssraman.drugraman.rbac.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.ssraman.drugraman.db.gen.DaoMaster;
import com.ssraman.drugraman.rbac.migration.RbacDatabaseMigration;

import org.greenrobot.greendao.database.Database;

/**
 * RBAC数据库升级助手
 * 处理数据库版本升级和RBAC系统迁移
 */
public class RbacDatabaseHelper extends DaoMaster.DevOpenHelper {
    
    private static final String TAG = "RbacDatabaseHelper";
    
    public RbacDatabaseHelper(Context context, String name) {
        super(context, name);
    }
    
    public RbacDatabaseHelper(Context context, String name, SQLiteDatabase.CursorFactory factory) {
        super(context, name, factory);
    }
    
    @Override
    public void onUpgrade(Database db, int oldVersion, int newVersion) {
        Log.i(TAG, "数据库升级: " + oldVersion + " -> " + newVersion);
        
        try {
            if (oldVersion < 2) {
                // 升级到版本2：引入RBAC权限系统
                Log.i(TAG, "开始RBAC系统迁移");
                RbacDatabaseMigration.migrateToRbac(db.getSQLiteDatabase());
                Log.i(TAG, "RBAC系统迁移完成");
            }
            
            // 如果有更多版本升级，在这里添加
            
        } catch (Exception e) {
            Log.e(TAG, "数据库升级失败", e);
            // 如果升级失败，可以选择删除所有表重新创建
            // 但这会丢失数据，所以要谨慎使用
            super.onUpgrade(db, oldVersion, newVersion);
        }
    }
    
    @Override
    public void onCreate(Database db) {
        Log.i(TAG, "创建数据库");
        super.onCreate(db);
        
        // 如果是全新安装，直接创建RBAC表和初始数据
        try {
            RbacDatabaseMigration.migrateToRbac(db.getSQLiteDatabase());
            Log.i(TAG, "新数据库RBAC初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "新数据库RBAC初始化失败", e);
        }
    }
}
