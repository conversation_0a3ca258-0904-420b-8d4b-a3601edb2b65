package com.ssraman.drugraman.ui.adapter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.databinding.ItemClassifyBinding;
import com.ssraman.drugraman.db.entity.SampleTypeInfo;
import com.ssraman.drugraman.item.SampleItem;
import com.ssraman.lib_common.adapter.SimpleDataBindingAdapter;

/**
 * @author: Administrator
 * @date: 2021/6/17
 */
public class ClassifyAdapter extends SimpleDataBindingAdapter<SampleTypeInfo, ItemClassifyBinding> {
    public ClassifyAdapter(Context context) {
        super(context,R.layout.item_classify, new DiffUtil.ItemCallback<SampleTypeInfo>() {
            @Override
            public boolean areItemsTheSame(@NonNull SampleTypeInfo oldItem, @NonNull SampleTypeInfo newItem) {
                return oldItem.equals(newItem);
            }

            @Override
            public boolean areContentsTheSame(@NonNull SampleTypeInfo oldItem, @NonNull SampleTypeInfo newItem) {
                return oldItem.getId().equals(newItem.getId());
            }
        });

    }

    @Override
    protected void onBindItem(ItemClassifyBinding binding, SampleTypeInfo item, RecyclerView.ViewHolder holder) {
        SampleItem sampleItem=new SampleItem(item.getName(),item.getImageIndex());
        binding.setSampleItem(sampleItem);
    }
}
