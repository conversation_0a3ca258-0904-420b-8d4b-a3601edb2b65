package com.ssraman.drugraman.business;

import com.bestvike.linq.Linq;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.newentiry.MatchFtInfo;
import com.ssraman.drugraman.newentiry.MatchNodeInfo;
import com.ssraman.drugraman.newentiry.MatchResultNodeInfo;
import com.ssraman.drugraman.newentiry.MatchResultPeakType;
import com.ssraman.drugraman.newentiry.VerificationResultInfo;
import com.ssraman.lib_common.mac.DetectionStateBean;
import com.ssraman.lib_common.utils.Utils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/10/8
 */
public class MatchSample {

    public List<MatchResultNodeInfo> MatchFunction(List<PeakInfo> source, List<MatchNodeInfo> destList,boolean verification_mode) {
        List<MatchResultNodeInfo> matchResultlist = new ArrayList<>();

        if (destList != null) {
            int sum = destList.size();
            int matchNumber = 0;
            int press_step = 80;
            if (sum != 0) {
                press_step = 80 / (sum + 1);
            }
            int index = 0;
            for (MatchNodeInfo item : destList) {
                MatchResultNodeInfo matchNode = MathcType(source, item,verification_mode);
                if (matchNode != null) {
                    //matchNode.setMatchResult(1);
                    matchResultlist.add(matchNode);
                }
                index++;
                int press_v = 20 + press_step * index;
                if(press_v>100)
                {
                    press_v=100;
                }
                LiveEventBus.get("test_progress").postAcrossProcess(new DetectionStateBean(press_v, Utils.getContext().getResources().getString(com.ssraman.lib_common.R.string.lib_searching)));
            }
        } else {
            //AppContext.showToast("不存在的样品库，未找到谱图 !");
            //throw new ArgumentOutOfRangeException("不存在的样品库，数据库不存在");
        }
        //return matchResultlist.OrderByDescending(x => x.compareRate).ThenByDescending(x => x.compareCount).ToList();
        return Linq.of(matchResultlist).orderByDescending(x -> x.getEvaluationRate()).thenByDescending(x -> x.getCompareRate()).thenByDescending(x -> x.getCompareCount()).toList();

    }

    private MatchResultNodeInfo MathcType(List<PeakInfo> source, MatchNodeInfo dest,boolean verification_mode) {
        boolean isNeiBiao = false;
        double neibiaoValue = 0.0;
        if (dest.getMatchSampleFt() == null)
            return null;
        int index = 0;
        int matchCount = 0;
        //string comparePoint = "";
        int match_index=0;
        int match_size=dest.getMatchSampleFt().size();

        int MustNumber = 0;
        int matchMust = 0;
        if(dest.getMatchSampleFt().size()==0)
        {
            return null;
        }
        MatchFtInfo old_destInfo=dest.getMatchSampleFt().get(0);
        List<MatchResultPeakType> MatchPeakList = new ArrayList<>();
        for (MatchFtInfo destInfo : dest.getMatchSampleFt()) {
            old_destInfo=destInfo;
            match_index++;
            matchMust = 0;
            if (destInfo.getUsePeakList().size() == 0)
                continue;
            if (destInfo.getFtInfo().getMatchScale() == 0)
                continue;
            MatchPeakList.clear();
            //List<PeakInfo> MustList = new ArrayList<>();
            //MustList.addAll(Linq.of(destInfo.getUsePeakList()).where(p -> p.getMust() == 1).toList())    ;
            MustNumber = Linq.of(destInfo.getUsePeakList()).where(p -> p.getMust() == 1).count();
            matchCount = 0;
            index = 0;
            List<PeakInfo> destPeaks = destInfo.getUsePeakList();

            for (int i = 0; i < destPeaks.size(); i++) {
                for (int j = index; j < source.size(); j++) {
                    MatchResultPeakType matchPeak = new MatchResultPeakType();
                    //double a = SpectrumTool.ChangeLumbdaToWave(source.get(j).getLumbda(), source.get(j).getSendWave());
//                    double b = SpectrumTool.ChangeLumbdaToWave(destPeaks.get(i).getLumbda(), destPeaks.get(i).getSendWave());//系统库数据
                    double a = source.get(j).getWave();//
                    double b = destPeaks.get(i).getWave();//系统库数据
                    if (b < destInfo.getFtInfo().getStartWave()) {
                        destPeaks.remove(i);
                        continue;
                    }
                    //  ExportTxt(source.UsePeakList, destInfo.UsePeakList);
                    if (Math.abs(a - b) <= destPeaks.get(i).getMatchLimit()) {
                        if ((destPeaks.get(i).getEnableCalibration() == 1) && (destPeaks.get(i).getCalibration() != 0.0))//使能内标法时，只要标准谱图存在内标峰，且内标峰在检测物中存在，则对匹配成功的特征峰做归一化后的比对
                        {
                            if (destPeaks.get(i).getStartIntensity() != 0.0) {
                                PeakInfo destpeak = destPeaks.get(i);
                                try {

                                    PeakInfo findpeak = Linq.of(source).first(p -> Math.abs(p.getWave() - destpeak.getCalibration()) <= destpeak.getMatchLimit());
                                    //TPeaks findpeak = SpectrumTool.findPeak(source, destPeaks.get(i).getCalibration(), destInfo.getMatchLimit(), true);//false 改为波数，因为填的是波数
                                    if (findpeak != null) {
                                        matchPeak.setCalibration(true);//内标
                                        matchPeak.setCalibrationValue(source.get(j).getIntensity() / findpeak.getIntensity());
                                        isNeiBiao = true;
                                        neibiaoValue = matchPeak.getCalibrationValue();
                                        if (source.get(j).getIntensity() / findpeak.getIntensity() < destPeaks.get(i).getStartIntensity()) {
                                            continue;
                                        }
                                    }
                                } catch (Exception ex) {
                                    //找不到会抛异常
                                }

                            }
                        }
                        index = j + 1;
                        matchCount++;
                        if (destPeaks.get(i).getMust() == 1) {
                            matchMust += 1;
                        }
                        matchPeak.setPeakWave(destPeaks.get(i).getWave());
                        matchPeak.setIntensity(destPeaks.get(i).getIntensity());
                        matchPeak.setMust(destPeaks.get(i).getMust());

                        MatchPeakList.add(matchPeak);
                        break;
                    }
                }
            }

            if (MustNumber != matchMust) {
                continue;
                // return null;
            }

            if ((double) matchCount / (double) destPeaks.size() >= destInfo.getFtInfo().getMatchScale()) {
                MatchResultNodeInfo node = new MatchResultNodeInfo();
                MatchFtInfo specInfo = new MatchFtInfo();
                specInfo.setFtInfo(destInfo.getFtInfo());
                specInfo.setPeakList(destInfo.getPeakList());
                node.setSpcInfo(specInfo);
                node.setSampleTypeInfo(dest.getMatchSample());
                node.setCompareCount(matchCount);
                node.setCompareRate((double) matchCount / (double) destPeaks.size());
                double evaluationRate = (matchCount + matchCount) / (destPeaks.size() + source.size());
                node.setEvaluationRate(evaluationRate);
                node.setStrRatio(String.valueOf(matchCount) + "/" + String.valueOf(destPeaks.size()));
                node.setConfidence(destInfo.getFtInfo().getConfidence());
                node.setComparePointList(MatchPeakList);
                node.setTitle(dest.getMatchSample().getName());//匹配成功的样品大类名
                node.setSampleName(dest.getMatchSample().getName());
                long ft_id = destInfo.getFtInfo().getId();
                node.setFtId((int) ft_id);
                node.setFtSampleName(destInfo.getFtInfo().getSampleName());
                node.setMatchResult(1);
                node.setNeiBiao(isNeiBiao);
                node.setNeibiaoValue(neibiaoValue);
                return node;//在同一个样品名的谱图群中，只要有一个匹配上就结束匹配；
            }
            else
            {
               if(verification_mode) {
                   if (match_index == match_size) {
                       MatchResultNodeInfo node = new MatchResultNodeInfo();
                       MatchFtInfo specInfo = new MatchFtInfo();
                       specInfo.setFtInfo(destInfo.getFtInfo());
                       specInfo.setPeakList(destInfo.getPeakList());
                       node.setSpcInfo(specInfo);
                       node.setSampleTypeInfo(dest.getMatchSample());
                       node.setCompareCount(matchCount);
                       node.setCompareRate((double) matchCount / (double) destPeaks.size());
                       double evaluationRate = (matchCount + matchCount) / (destPeaks.size() + source.size());
                       node.setEvaluationRate(evaluationRate);
                       node.setStrRatio(String.valueOf(matchCount) + "/" + String.valueOf(destPeaks.size()));
                       node.setConfidence(destInfo.getFtInfo().getConfidence());
                       node.setComparePointList(MatchPeakList);
                       node.setTitle(dest.getMatchSample().getName());//匹配成功的样品大类名
                       node.setSampleName(dest.getMatchSample().getName());
                       long ft_id = destInfo.getFtInfo().getId();
                       node.setFtId((int) ft_id);
                       node.setFtSampleName(destInfo.getFtInfo().getSampleName());
                       node.setMatchResult(0);
                       node.setNeiBiao(isNeiBiao);
                       node.setNeibiaoValue(neibiaoValue);
                       return node;//验证没有匹配上时给出最后一张谱图和系数；
                   }
               }
            }
        }
        if(verification_mode)
        {
            int destPeakSize=old_destInfo.getUsePeakList().size();
            MatchResultNodeInfo node = new MatchResultNodeInfo();
            MatchFtInfo specInfo = new MatchFtInfo();
            specInfo.setFtInfo(old_destInfo.getFtInfo());
            specInfo.setPeakList(old_destInfo.getPeakList());
            node.setSpcInfo(specInfo);
            node.setSampleTypeInfo(dest.getMatchSample());
            node.setCompareCount(matchCount);
            if(destPeakSize!=0) {
                node.setCompareRate((double) matchCount / (double) destPeakSize);
            }
            else
            {
                node.setCompareRate(-1);
            }
            double evaluationRate = (matchCount + matchCount) / (destPeakSize + source.size());
            node.setEvaluationRate(evaluationRate);
            node.setStrRatio(String.valueOf(matchCount) + "/" + String.valueOf(destPeakSize));
            node.setConfidence(old_destInfo.getFtInfo().getConfidence());
            node.setComparePointList(MatchPeakList);
            node.setTitle(dest.getMatchSample().getName());//匹配成功的样品大类名
            node.setSampleName(dest.getMatchSample().getName());
            long ft_id = old_destInfo.getFtInfo().getId();
            node.setFtId((int) ft_id);
            node.setFtSampleName(old_destInfo.getFtInfo().getSampleName());
            node.setMatchResult(0);
            node.setNeiBiao(isNeiBiao);
            node.setNeibiaoValue(neibiaoValue);
            return node;//验证没有匹配上时给出最后一张谱图和系数；
        }
        else {
            return null;
        }
    }


    public VerificationResultInfo MathVerificationType(List<PeakInfo> source) {

        int index = 0;
        int matchCount = 0;

        List<Double> MatchPeakList = new ArrayList<>();
        double[] destPeaks = new double[]{376.0, 915.0, 1374.0, 2249.0};

        for (int i = 0; i < destPeaks.length; i++) {
            for (int j = index; j < source.size(); j++) {
                MatchResultPeakType matchPeak = new MatchResultPeakType();
                double a = source.get(j).getWave();//
                double b = destPeaks[i];//系统库数据

                //  验证设备时波数偏移需要求严格
                if (Math.abs(a - b) <= 5.0) {

                    index = j + 1;
                    matchCount++;
                    MatchPeakList.add(b);
                    break;
                }
            }
        }

        if ((double) matchCount / (double) destPeaks.length >= 1) {
            VerificationResultInfo node = new VerificationResultInfo();
            node.setMatchResult(1);
            double compareRate = ((double) matchCount / (double) destPeaks.length);
            node.setCompareRate(compareRate);
            node.setComparePointList(MatchPeakList);

            return node;
        } else {
            VerificationResultInfo node = new VerificationResultInfo();
            node.setMatchResult(0);
            double compareRate = ((double) matchCount / (double) destPeaks.length);
            node.setCompareRate(compareRate);
            node.setComparePointList(MatchPeakList);
            return node;
        }
    }

}
