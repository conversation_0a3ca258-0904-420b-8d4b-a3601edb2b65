package com.ssraman.drugraman.business;

import android.util.Log;

import com.jeremyliao.liveeventbus.LiveEventBus;
import com.ssraman.drugraman.common.algorithm.soAlgorithm;
import com.ssraman.drugraman.newentiry.MatchFtInfo;
import com.ssraman.drugraman.newentiry.MatchResultNodeInfo;
import com.ssraman.lib_common.mac.DetectionStateBean;
import com.ssraman.lib_common.utils.Utils;

import java.util.Collections;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/10/22
 */
public class SpecMatchUtil {
    public final String TAG = getClass().getSimpleName();
    public double correlation_value;
    public List<MatchResultNodeInfo> Correlation_Method(List<MatchResultNodeInfo> matchingResponseList, MatchFtInfo sampleNode,int core_method,boolean verification_mode) {

        //结果
        double[] ttresult = new double[100];
        int nnls_result_n = 0;
        double matchResult=0.0;

        int startIndex=0;

        int matchingListSize=matchingResponseList.size();

        int testLenth=sampleNode.getAutoLineSourceWave().length;
        double[] testWave = new double[testLenth];
        double[] testintensity = new double[testLenth];

        for (int i = 0; i < testLenth; i++) {
            if(i < testLenth) {
                testWave[i] = sampleNode.getAutoLineSourceWave()[i];
                testintensity[i] = sampleNode.getAutoLineIntensity()[i];
            }else
            {
                testWave[i] = 0.0;
                testintensity[i] = 0.0;
            }
        }

        SpectrumDataProcessUtil dataProcessUtil=new SpectrumDataProcessUtil();
        int press_step = 80;
        if (matchingListSize != 0) {
            press_step = 80 / (matchingListSize + 1);
        }
        int index = 0;
        for (int i = 0; i <matchingListSize; i++) {
            try {
                MatchResultNodeInfo item = matchingResponseList.get(i);
                if (item != null) {

                    double[] itemWaveSource=item.getSpcInfo().getUseSourceWave();

                    MatchFtInfo temp = dataProcessUtil.autoBaseNew(item.getSpcInfo().getSourceIntensity(), item.getSpcInfo().getSourceWave(),item.getSpcInfo().getFtInfo().getStartWave());//平滑和拉基线
                    double[] itemWave=temp.getAutoLineSourceWave();
                    double[] itemIntensity=temp.getAutoLineIntensity();

                    //////此处需重构代码，把一些工作放在算法里，防止重复做同样的步骤，代码里直接做插值即可
                    int wave_start=300;
                    int wave_end=3200;
                    int data_length = wave_end-wave_start;
                    double[] itemWaves=new double[data_length];
                    double[] itemIntensitys=new double[data_length];
                    double[] testWaves=new double[data_length];
                    double[] testintensitys=new double[data_length];
                    int k1=0;
                    for(int j=0;j<itemWave.length;j++)
                    {
                        if(k1>=data_length)
                        {
                            break;
                        }
                        if(itemWave[j]>=wave_start&&itemWave[j]<=wave_end)
                        {
                            itemWaves[k1]=itemWave[j];
                            itemIntensitys[k1]=itemIntensity[j];
                            k1++;
                        }
                    }
                    int k2=0;
                    for(int j=0;j<testWave.length;j++) {
                        if (k2 >= data_length) {
                            break;
                        }
                        if (testWave[j] >= wave_start && testWave[j] <= wave_end) {
                            testWaves[k2] = testWave[j];
                            testintensitys[k2] = testintensity[j];
                            k2++;
                        }
                    }
                    ////////////////////////////////////////////////

                    if(core_method==0) {
                        matchResult = soAlgorithm.PreCOSD(testWaves, testintensitys, k2, itemWaves, itemIntensitys, k1, 260);
                        //matchResult = soAlgorithm.PreWholeCOSD(testWaves, testintensitys, k2, itemWaves, itemIntensitys, k1, 260);
                    }
                    else if(core_method==1)
                    {
                        matchResult = soAlgorithm.PreHQI(testWaves, testintensitys, k2, itemWaves, itemIntensitys, k1, 260);
                    }
                    item.setExRatio(matchResult);

                } else {
                    Log.d(TAG,"item==null");
                }
            } catch (Exception e) {
                Log.d(TAG,"Exception：" + e.getMessage());
            }

            index++;
            int press_v = 20 + press_step * index;
            if(press_v>100)
            {
                press_v=100;
            }
            LiveEventBus.get("test_progress").postAcrossProcess(new DetectionStateBean(press_v, Utils.getContext().getResources().getString(com.ssraman.lib_common.R.string.correlation_runing)));
        }
        Collections.sort(matchingResponseList);
        correlation_value=matchingResponseList.get(0).getExRatio();
        for(int ii=matchingResponseList.size()-1;ii>=0;ii--)
        {
            if(!verification_mode) {
                if (matchingResponseList.get(ii).getExRatio() < matchingResponseList.get(ii).getConfidence()) {
                    matchingResponseList.remove(ii);
                } else {
                    break;
                }
            }
            else
            {
                if(matchingResponseList.get(ii).getMatchResult()==0)
                {
                    if(matchingResponseList.get(ii).getCompareRate()!=-1) {
                        matchingResponseList.get(ii).setExRatio(matchingResponseList.get(ii).getCompareRate());
                    }
                    else {
                        if (matchingResponseList.get(ii).getExRatio() < matchingResponseList.get(ii).getConfidence()) {

                        } else {
                            matchingResponseList.get(ii).setMatchResult(1);
                        }
                    }
                }
                else
                {
                    if (matchingResponseList.get(ii).getExRatio() < matchingResponseList.get(ii).getConfidence())
                    {
                        matchingResponseList.get(ii).setMatchResult(0);
                    }
                    else
                    {
                        matchingResponseList.get(ii).setMatchResult(1);
                    }
                }
            }
        }
        Collections.sort(matchingResponseList);
        return matchingResponseList;
    }

    public List<MatchResultNodeInfo> Correlation_nobaseline_Method(List<MatchResultNodeInfo> matchingResponseList, MatchFtInfo sampleNode,int core_method,boolean verification_mode) {

        //结果
        double[] ttresult = new double[100];
        int nnls_result_n = 0;
        double matchResult=0.0;

        int startIndex=0;

        int matchingListSize=matchingResponseList.size();

        int testLenth=sampleNode.getSourceIntensity().length;
        double[] testWave = new double[testLenth];
        double[] testintensity = new double[testLenth];

        SpectrumDataProcessUtil dataProcessUtil=new SpectrumDataProcessUtil();

        MatchFtInfo test_temp = dataProcessUtil.dataInterpolate(sampleNode.getSourceIntensity(), sampleNode.getSourceWave(),sampleNode.getFtInfo().getStartWave());//插值

        for (int i = 0; i < testLenth; i++) {
            if(i < testLenth) {
                testWave[i] = test_temp.getAutoLineSourceWave()[i];
                testintensity[i] = test_temp.getAutoLineIntensity()[i];
            }else
            {
                testWave[i] = 0.0;
                testintensity[i] = 0.0;
            }
        }


        int press_step = 80;
        if (matchingListSize != 0) {
            press_step = 80 / (matchingListSize + 1);
        }
        int index = 0;
        for (int i = 0; i <matchingListSize; i++) {
            try {
                MatchResultNodeInfo item = matchingResponseList.get(i);
                if (item != null) {

                    double[] itemWaveSource=item.getSpcInfo().getUseSourceWave();

                    MatchFtInfo temp = dataProcessUtil.dataInterpolate(item.getSpcInfo().getSourceIntensity(), item.getSpcInfo().getSourceWave(),item.getSpcInfo().getFtInfo().getStartWave());//插值
                    double[] itemWave=temp.getAutoLineSourceWave();
                    double[] itemIntensity=temp.getAutoLineIntensity();

                    //////此处需重构代码，把一些工作放在算法里，防止重复做同样的步骤，代码里直接做插值即可
                    int wave_start=300;
                    int wave_end=3200;
                    int data_length = wave_end-wave_start;
                    double[] itemWaves=new double[data_length];
                    double[] itemIntensitys=new double[data_length];
                    double[] testWaves=new double[data_length];
                    double[] testintensitys=new double[data_length];
                    int k1=0;
                    for(int j=0;j<itemWave.length;j++)
                    {
                        if(k1>=data_length)
                        {
                            break;
                        }
                        if(itemWave[j]>=wave_start&&itemWave[j]<=wave_end)
                        {
                            itemWaves[k1]=itemWave[j];
                            itemIntensitys[k1]=itemIntensity[j];
                            k1++;
                        }
                    }
                    int k2=0;
                    for(int j=0;j<testWave.length;j++) {
                        if (k2 >= data_length) {
                            break;
                        }
                        if (testWave[j] >= wave_start && testWave[j] <= wave_end) {
                            testWaves[k2] = testWave[j];
                            testintensitys[k2] = testintensity[j];
                            k2++;
                        }
                    }
                    ////////////////////////////////////////////////

                    if(core_method==0) {
                        matchResult = soAlgorithm.PreCOSD(testWaves, testintensitys, k2, itemWaves, itemIntensitys, k1, 260);
                    }
                    else if(core_method==1)
                    {
                        matchResult = soAlgorithm.PreHQI(testWaves, testintensitys, k2, itemWaves, itemIntensitys, k1, 260);
                    }
                    else if(core_method==5)
                    {
                        matchResult = soAlgorithm.PreWholeCOSD(testWaves, testintensitys, k2, itemWaves, itemIntensitys, k1, 260);
                    }
                    item.setExRatio(matchResult);

                } else {
                    Log.d(TAG,"item==null");
                }
            } catch (Exception e) {
                Log.d(TAG,"Exception：" + e.getMessage());
            }

            index++;
            int press_v = 20 + press_step * index;
            if(press_v>100)
            {
                press_v=100;
            }
            LiveEventBus.get("test_progress").postAcrossProcess(new DetectionStateBean(press_v, Utils.getContext().getResources().getString(com.ssraman.lib_common.R.string.correlation_runing)));
        }
        Collections.sort(matchingResponseList);
        correlation_value=matchingResponseList.get(0).getExRatio();
        for(int ii=matchingResponseList.size()-1;ii>=0;ii--)
        {
            if(!verification_mode) {
                if (matchingResponseList.get(ii).getExRatio() < matchingResponseList.get(ii).getConfidence()) {
                    matchingResponseList.remove(ii);
                } else {
                    break;
                }
            }
            else
            {
                if(matchingResponseList.get(ii).getMatchResult()==0)
                {
                    if(matchingResponseList.get(ii).getCompareRate()!=-1) {
                        matchingResponseList.get(ii).setExRatio(matchingResponseList.get(ii).getCompareRate());
                    }
                    else {
                        if (matchingResponseList.get(ii).getExRatio() < matchingResponseList.get(ii).getConfidence()) {

                        } else {
                            matchingResponseList.get(ii).setMatchResult(1);
                        }
                    }
                }
                else
                {
                    if (matchingResponseList.get(ii).getExRatio() < matchingResponseList.get(ii).getConfidence())
                    {
                        matchingResponseList.get(ii).setMatchResult(0);
                    }
                    else
                    {
                        matchingResponseList.get(ii).setMatchResult(1);
                    }
                }
            }
        }
        Collections.sort(matchingResponseList);
        return matchingResponseList;
    }

}
