package com.ssraman.drugraman.business;

import com.ssraman.drugraman.common.algorithm.soAlgorithm;
import com.ssraman.drugraman.constant.InterFaceConst;
import com.ssraman.drugraman.db.entity.CalibrationFtInfo;
import com.ssraman.drugraman.db.entity.FtInfo;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.newentiry.MatchFtInfo;
import com.ssraman.drugraman.newentiry.SampleNode;
import com.ssraman.lib_common.communication.TransitionUtil;
import com.ssraman.lib_common.utils.SDCardScanner;
import com.ssraman.lib_common.utils.TxtUtil;
import com.ssraman.lib_common.utils.Utils;

import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/10/22
 */
public class SpectrumDataProcessUtil {

    public MatchFtInfo DataProcess(double[] wave, byte[] receiveData, int integrationTime, int laser, int factor, int startWave) {
        double[] new_waves;//新x

        //获取 去掉包头包位
        byte[] returnDataHandle = new byte[receiveData.length - 9];
        for (int i = 0; i < returnDataHandle.length; i++) {
            returnDataHandle[i] = receiveData[i + 7];
        }
        //double[] intensitys = soAlgorithm.getIntensity(returnDataHandle, returnDataHandle.length);//检测的结果
        double[] intensitys = soAlgorithm.getIntensityNew(returnDataHandle,returnDataHandle.length);//检测的结果

        new_waves = soAlgorithm.getnewx(3200);
        double[] old_intensitys=new double[intensitys.length];
        double[] old_wave=new double[wave.length];
        System.arraycopy(intensitys,0,old_intensitys,0,intensitys.length);
        System.arraycopy(wave,0,old_wave,0,wave.length);
        SpecAndNoise returnSpecAndNoise = soAlgorithm.PreCacu2(wave, intensitys, new_waves, Double.valueOf(startWave));
        intensitys =returnSpecAndNoise.sepc;
        List<PeakInfo> specPeakList = soAlgorithm.date_xunfen_idx(new_waves, intensitys, 20, returnSpecAndNoise.noise);//寻峰

        MatchFtInfo matchFtInfo = new MatchFtInfo();
        ///////////////
        FtInfo ftInfo = new FtInfo();
        ftInfo.setObWave(TransitionUtil.doubleArray2ByteArray(old_wave));//存储的是否需要时拉过基线的？
        ftInfo.setObIntensity(TransitionUtil.doubleArray2ByteArray(old_intensitys));
        ftInfo.setSampleName("");
        ftInfo.setSendWave(InterFaceConst.SEND_WAVE);
        ftInfo.setStartWave(startWave);
        ftInfo.setIntegratioTime(integrationTime);
        ftInfo.setLaserPower((double) laser);
        ftInfo.setAverageCount(1);
        matchFtInfo.setFtInfo(ftInfo);


        //////////////////////////////
        matchFtInfo.setPeakList(specPeakList);

        matchFtInfo.setDetectionData(receiveData);
        matchFtInfo.setAutoLineSourceWave(new_waves);
        matchFtInfo.setAutoLineIntensity(intensitys);
        matchFtInfo.setAutoLinePeakList(specPeakList);
        return matchFtInfo;
    }

    public MatchFtInfo RecordDataProcess(double[] wave, byte[] receiveData,  double[] intensitys ,List<PeakInfo> specPeakList,int integrationTime, int laser,  int startWave) {
        double[] new_waves;//新x

        //获取 去掉包头包位
        byte[] returnDataHandle = new byte[receiveData.length - 9];
        for (int i = 0; i < returnDataHandle.length; i++) {
            returnDataHandle[i] = receiveData[i + 7];
        }
        //double[] intensitys = soAlgorithm.getIntensity(returnDataHandle, returnDataHandle.length);//检测的结果

        new_waves = soAlgorithm.getnewx(3200);
        double[] old_intensitys=new double[intensitys.length];
        System.arraycopy(intensitys,0,old_intensitys,0,intensitys.length);
        SpecAndNoise returnSpecAndNoise = soAlgorithm.PreCacu2(wave, intensitys, new_waves, Double.valueOf(startWave));
        intensitys =returnSpecAndNoise.sepc;
        //List<PeakInfo> specPeakList = soAlgorithm.date_xunfen(new_waves, intensitys, 20, returnSpecAndNoise.noise);//寻峰

        MatchFtInfo matchFtInfo = new MatchFtInfo();
        ///////////////
        FtInfo ftInfo = new FtInfo();
        ftInfo.setObWave(TransitionUtil.doubleArray2ByteArray(wave));//存储的是否需要时拉过基线的？
        ftInfo.setObIntensity(TransitionUtil.doubleArray2ByteArray(old_intensitys));
        ftInfo.setSampleName("");
        ftInfo.setSendWave(InterFaceConst.SEND_WAVE);
        ftInfo.setStartWave(startWave);
        ftInfo.setIntegratioTime(integrationTime);
        ftInfo.setLaserPower((double) laser);
        ftInfo.setAverageCount(1);
        matchFtInfo.setFtInfo(ftInfo);


        //////////////////////////////
        matchFtInfo.setPeakList(specPeakList);

        matchFtInfo.setDetectionData(receiveData);
        matchFtInfo.setAutoLineSourceWave(new_waves);
        matchFtInfo.setAutoLineIntensity(intensitys);
        matchFtInfo.setAutoLinePeakList(specPeakList);
        return matchFtInfo;
    }

    public SampleNode LibDataProcess(SampleNode node)
    {
        FtInfo ftInfo = node.getSpcInfo().getFtInfo();

        double[] wave =  TransitionUtil.byteArray2DoubleArray(ftInfo.getObWave());
        double[] intensitys = TransitionUtil.byteArray2DoubleArray(ftInfo.getObIntensity());
        double[]  new_waves = soAlgorithm.getnewx(3200);
        SpecAndNoise returnSpecAndNoise = soAlgorithm.PreCacu2(wave, intensitys, new_waves, ftInfo.getStartWave());
        List<PeakInfo> specPeakList = soAlgorithm.date_xunfen_idx(new_waves, intensitys, 20, returnSpecAndNoise.noise);//寻峰

        node.getSpcInfo().setAutoLineSourceWave(new_waves);
        node.getSpcInfo().setAutoLineIntensity(returnSpecAndNoise.sepc);
        node.getSpcInfo().setAutoLinePeakList(specPeakList);

        return node;
    }

    public SampleNode LibDataProcessNoline(SampleNode node)
    {
        FtInfo ftInfo = node.getSpcInfo().getFtInfo();

        double[] wave =  TransitionUtil.byteArray2DoubleArray(ftInfo.getObWave());
        double[] intensitys = TransitionUtil.byteArray2DoubleArray(ftInfo.getObIntensity());
        double[]  new_waves = soAlgorithm.getnewx(3200);
        double[]  new_waves2 = soAlgorithm.getnewx(3200);
        double[] old_wave=new double[wave.length];
        double[] old_intensitys=new double[intensitys.length];
        System.arraycopy(intensitys,0,old_intensitys,0,intensitys.length);
        System.arraycopy(wave,0,old_wave,0,wave.length);

        SpecAndNoise returnSpecAndNoise = soAlgorithm.PreCacu2(wave, intensitys, new_waves, ftInfo.getStartWave());
        List<PeakInfo> specPeakList = soAlgorithm.date_xunfen_idx(new_waves, intensitys, 20, returnSpecAndNoise.noise);//寻峰


        SpecAndNoise returnSpecNoline = soAlgorithm.PreInterpolate(old_wave, old_intensitys, new_waves2, ftInfo.getStartWave());

        node.getSpcInfo().setAutoLineSourceWave(new_waves2);
        node.getSpcInfo().setAutoLineIntensity(returnSpecNoline.sepc);
        node.getSpcInfo().setAutoLinePeakList(specPeakList);

        return node;
    }

    public MatchFtInfo LibDataProcess2(MatchFtInfo node)
    {
        FtInfo ftInfo = node.getFtInfo();

        double[] wave =  TransitionUtil.byteArray2DoubleArray(ftInfo.getObWave());
        double[] intensitys = TransitionUtil.byteArray2DoubleArray(ftInfo.getObIntensity());
        double[]  new_waves = soAlgorithm.getnewx(3200);
        SpecAndNoise returnSpecAndNoise = soAlgorithm.PreCacu2(wave, intensitys, new_waves, ftInfo.getStartWave());
        List<PeakInfo> specPeakList = soAlgorithm.date_xunfen_idx(new_waves, intensitys, 20, returnSpecAndNoise.noise);//寻峰

        node.setAutoLineSourceWave(new_waves);
        node.setAutoLineIntensity(returnSpecAndNoise.sepc);
        node.setAutoLinePeakList(specPeakList);

        return node;
    }

    public SampleNode LibDataProcess3(CalibrationFtInfo calibrationFtInfo)
    {
        double[] wave =  TransitionUtil.byteArray2DoubleArray(calibrationFtInfo.getObWave());
        double[] intensitys = TransitionUtil.byteArray2DoubleArray(calibrationFtInfo.getObIntensity());
        double[]  new_waves = soAlgorithm.getnewx(3200);
        SpecAndNoise returnSpecAndNoise = soAlgorithm.PreCacu2(wave, intensitys, new_waves, InterFaceConst.start_wave);

        SampleNode node = new SampleNode();
        node.setTitle(calibrationFtInfo.getSampleName());
        MatchFtInfo spcInfo=new MatchFtInfo();
        node.setSpcInfo(spcInfo);
        node.getSpcInfo().setAutoLineSourceWave(new_waves);
        node.getSpcInfo().setAutoLineIntensity(returnSpecAndNoise.sepc);

        return node;
    }

    public MatchFtInfo autoBaseNew(double[] intensitys, double[] wave,int startWave) {
        double[] new_waves;//新x
        MatchFtInfo matchFtInfo = new MatchFtInfo();

        new_waves = soAlgorithm.getnewx(3200);
        double[] old_intensitys=new double[intensitys.length];
        System.arraycopy(intensitys,0,old_intensitys,0,intensitys.length);
        //intensitys = soAlgorithm.PreCacu(lumbda, intensitys, new_waves, Double.valueOf(startWave));
        SpecAndNoise returnSpecAndNoise = soAlgorithm.PreCacu2(wave, intensitys, new_waves, Double.valueOf(startWave));
        intensitys =returnSpecAndNoise.sepc;

        matchFtInfo.setAutoLineSourceWave(new_waves);
        matchFtInfo.setAutoLineIntensity(intensitys);

        return matchFtInfo;
    }

    public MatchFtInfo dataInterpolate(double[] intensitys, double[] wave,int startWave) {
        double[] new_waves;//新x
        MatchFtInfo matchFtInfo = new MatchFtInfo();

        new_waves = soAlgorithm.getnewx(3200);
        double[] old_intensitys=new double[intensitys.length];
        System.arraycopy(intensitys,0,old_intensitys,0,intensitys.length);
        SpecAndNoise returnSpecAndNoise = soAlgorithm.PreInterpolate(wave, intensitys, new_waves, Double.valueOf(startWave));
        intensitys =returnSpecAndNoise.sepc;

        matchFtInfo.setAutoLineSourceWave(new_waves);
        matchFtInfo.setAutoLineIntensity(intensitys);

        return matchFtInfo;
    }

    public MatchFtInfo Calibration(double[] lumbda, byte[] receiveData, int integrationTime, int laser, int startWave)
    {
        //获取 去掉包头包位
        byte[] returnDataHandle = new byte[receiveData.length - 9];
        for (int i = 0; i < returnDataHandle.length; i++)
        {
            returnDataHandle[i] = receiveData[i + 7];
        }
        double[] intensitys = soAlgorithm.getIntensity(returnDataHandle, returnDataHandle.length);//检测的结果

        double[] old_intensitys = new double[intensitys.length];
        double[] old_lumbda = new double[lumbda.length];
        System.arraycopy(intensitys, 0, old_intensitys, 0, intensitys.length);
        System.arraycopy(lumbda, 0, old_lumbda, 0, lumbda.length);

        List<PeakInfo> specPeakList = soAlgorithm.date_xunfen(lumbda, intensitys, 10, 100);//寻峰

        MatchFtInfo matchFtInfo = new MatchFtInfo();
        ///////////////
        FtInfo ftInfo = new FtInfo();
        ftInfo.setObWave(TransitionUtil.doubleArray2ByteArray(old_lumbda));//存储的是否需要时拉过基线的？
        ftInfo.setObIntensity(TransitionUtil.doubleArray2ByteArray(old_intensitys));
        ftInfo.setSampleName("");
        ftInfo.setSendWave(InterFaceConst.SEND_WAVE);
        ftInfo.setStartWave(startWave);
        ftInfo.setIntegratioTime(integrationTime);
        ftInfo.setLaserPower((double) laser);
        ftInfo.setAverageCount(1);
        matchFtInfo.setFtInfo(ftInfo);

        //////////////////////////////
        for(int i=0;i< specPeakList.size();i++)
        {
            int t_wave=specPeakList.get(i).getPeakId();
            double wave=t_wave;
            specPeakList.get(i).setWave(wave);
        }
        matchFtInfo.setPeakList(specPeakList);

        double[] x = new double[2048];
        for (int i = 0; i < 2048; i++)
        {
            x[i] = i;
        }
        matchFtInfo.setDetectionData(receiveData);
        matchFtInfo.setAutoLineSourceWave(x);
        matchFtInfo.setAutoLineIntensity(intensitys);
        matchFtInfo.setAutoLinePeakList(specPeakList);
        return matchFtInfo;
    }
}
