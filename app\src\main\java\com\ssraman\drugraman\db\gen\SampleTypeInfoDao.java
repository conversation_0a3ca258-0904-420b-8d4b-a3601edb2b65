package com.ssraman.drugraman.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.ssraman.drugraman.db.entity.SampleTypeInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "SampleType".
*/
public class SampleTypeInfoDao extends AbstractDao<SampleTypeInfo, Long> {

    public static final String TABLENAME = "SampleType";

    /**
     * Properties of entity SampleTypeInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "Id", true, "Id");
        public final static Property ParentId = new Property(1, Integer.class, "ParentId", false, "ParentId");
        public final static Property FId = new Property(2, Integer.class, "FId", false, "FId");
        public final static Property Name = new Property(3, String.class, "Name", false, "Name");
        public final static Property Remark = new Property(4, String.class, "Remark", false, "Remark");
        public final static Property Type = new Property(5, Integer.class, "Type", false, "Type");
        public final static Property LibraryType = new Property(6, Integer.class, "LibraryType", false, "LibraryType");
        public final static Property ImageIndex = new Property(7, Integer.class, "ImageIndex", false, "ImageIndex");
    }


    public SampleTypeInfoDao(DaoConfig config) {
        super(config);
    }
    
    public SampleTypeInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, SampleTypeInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        Integer ParentId = entity.getParentId();
        if (ParentId != null) {
            stmt.bindLong(2, ParentId);
        }
 
        Integer FId = entity.getFId();
        if (FId != null) {
            stmt.bindLong(3, FId);
        }
 
        String Name = entity.getName();
        if (Name != null) {
            stmt.bindString(4, Name);
        }
 
        String Remark = entity.getRemark();
        if (Remark != null) {
            stmt.bindString(5, Remark);
        }
 
        Integer Type = entity.getType();
        if (Type != null) {
            stmt.bindLong(6, Type);
        }
 
        Integer LibraryType = entity.getLibraryType();
        if (LibraryType != null) {
            stmt.bindLong(7, LibraryType);
        }
 
        Integer ImageIndex = entity.getImageIndex();
        if (ImageIndex != null) {
            stmt.bindLong(8, ImageIndex);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, SampleTypeInfo entity) {
        stmt.clearBindings();
 
        Long Id = entity.getId();
        if (Id != null) {
            stmt.bindLong(1, Id);
        }
 
        Integer ParentId = entity.getParentId();
        if (ParentId != null) {
            stmt.bindLong(2, ParentId);
        }
 
        Integer FId = entity.getFId();
        if (FId != null) {
            stmt.bindLong(3, FId);
        }
 
        String Name = entity.getName();
        if (Name != null) {
            stmt.bindString(4, Name);
        }
 
        String Remark = entity.getRemark();
        if (Remark != null) {
            stmt.bindString(5, Remark);
        }
 
        Integer Type = entity.getType();
        if (Type != null) {
            stmt.bindLong(6, Type);
        }
 
        Integer LibraryType = entity.getLibraryType();
        if (LibraryType != null) {
            stmt.bindLong(7, LibraryType);
        }
 
        Integer ImageIndex = entity.getImageIndex();
        if (ImageIndex != null) {
            stmt.bindLong(8, ImageIndex);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public SampleTypeInfo readEntity(Cursor cursor, int offset) {
        SampleTypeInfo entity = new SampleTypeInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // Id
            cursor.isNull(offset + 1) ? null : cursor.getInt(offset + 1), // ParentId
            cursor.isNull(offset + 2) ? null : cursor.getInt(offset + 2), // FId
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // Name
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // Remark
            cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5), // Type
            cursor.isNull(offset + 6) ? null : cursor.getInt(offset + 6), // LibraryType
            cursor.isNull(offset + 7) ? null : cursor.getInt(offset + 7) // ImageIndex
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, SampleTypeInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setParentId(cursor.isNull(offset + 1) ? null : cursor.getInt(offset + 1));
        entity.setFId(cursor.isNull(offset + 2) ? null : cursor.getInt(offset + 2));
        entity.setName(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setRemark(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setType(cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5));
        entity.setLibraryType(cursor.isNull(offset + 6) ? null : cursor.getInt(offset + 6));
        entity.setImageIndex(cursor.isNull(offset + 7) ? null : cursor.getInt(offset + 7));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(SampleTypeInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(SampleTypeInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(SampleTypeInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
