package com.ssraman.drugraman.typeface;

/**
 * @author: Administrator
 * @date: 2021/7/20
 */
public enum CustomFuncType {
    CustomClassifyBulid(0),
    CustomLib(1);

    private int value = 0;

    private CustomFuncType(int value) {    //    必须是private的，否则编译错误
        this.value = value;
    }

    public static CustomFuncType valueOf(int value) {    //    手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return CustomClassifyBulid;
            case 1:
                return CustomLib;
            default:
                return CustomClassifyBulid;
        }
    }

    public int value() {
        return this.value;
    }
}
