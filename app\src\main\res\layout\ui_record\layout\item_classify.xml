<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <data>
        <variable
            name="sampleItem"
            type="com.ssraman.drugraman.item.SampleItem" />
    </data>

    <LinearLayout
        android:layout_width="120dp"
        android:layout_height="96dp"
        android:layout_marginBottom="30dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:gravity="center"
        >

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:backgroundResource="@{sampleItem.imageRes}"
            android:gravity="bottom|center_horizontal"
            android:paddingBottom="10dp"
            android:text="@{sampleItem.getName()}"
            android:textColor="@color/white"
            android:textSize="18sp"
            tools:text="农药残留" />

    </LinearLayout>
</layout>