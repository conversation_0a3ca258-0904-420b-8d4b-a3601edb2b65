package com.ssraman.drugraman.rbac.test;

import com.ssraman.drugraman.rbac.entity.User;
import com.ssraman.drugraman.rbac.security.SecurityContext;
import com.ssraman.drugraman.rbac.security.PermissionChecker;
import com.ssraman.drugraman.rbac.interceptor.PermissionInterceptor;
import com.ssraman.drugraman.rbac.annotation.RequirePermission;
import com.ssraman.drugraman.rbac.annotation.RequireRole;
import com.ssraman.drugraman.rbac.exception.PermissionDeniedException;
import com.ssraman.drugraman.rbac.exception.AuthenticationRequiredException;
import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;

import org.junit.Before;
import org.junit.Test;
import org.junit.After;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 安全测试类
 * 测试权限系统的安全性和边界条件
 */
public class SecurityTest {
    
    @Mock
    private PermissionChecker mockPermissionChecker;
    
    private PermissionInterceptor permissionInterceptor;
    private User testUser;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        permissionInterceptor = new PermissionInterceptor(mockPermissionChecker);
        
        // 创建测试用户
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setStatus(1);
        
        // 清除安全上下文
        SecurityContext.clear();
    }
    
    @After
    public void tearDown() {
        SecurityContext.clear();
    }
    
    @Test(expected = AuthenticationRequiredException.class)
    public void testUnauthenticatedAccess_ShouldThrowException() throws Exception {
        // 准备测试方法
        Method method = TestService.class.getMethod("protectedMethod");
        
        // 执行测试 - 未设置当前用户
        permissionInterceptor.intercept(method, new Object[]{});
    }
    
    @Test(expected = PermissionDeniedException.class)
    public void testInsufficientPermission_ShouldThrowException() throws Exception {
        // 设置当前用户
        SecurityContext.setCurrentUser(testUser);
        
        // 模拟权限检查失败
        when(mockPermissionChecker.hasPermission(testUser.getId(), PermissionType.USER_CREATE.getCode()))
                .thenReturn(false);
        
        // 准备测试方法
        Method method = TestService.class.getMethod("createUserMethod");
        
        // 执行测试
        permissionInterceptor.intercept(method, new Object[]{});
    }
    
    @Test
    public void testSufficientPermission_ShouldAllowAccess() throws Exception {
        // 设置当前用户
        SecurityContext.setCurrentUser(testUser);
        
        // 模拟权限检查成功
        when(mockPermissionChecker.hasPermission(testUser.getId(), PermissionType.USER_CREATE.getCode()))
                .thenReturn(true);
        
        // 准备测试方法
        Method method = TestService.class.getMethod("createUserMethod");
        
        // 执行测试
        boolean result = permissionInterceptor.intercept(method, new Object[]{});
        
        // 验证结果
        assertTrue("应该允许访问", result);
    }
    
    @Test(expected = PermissionDeniedException.class)
    public void testInsufficientRoleLevel_ShouldThrowException() throws Exception {
        // 设置当前用户
        SecurityContext.setCurrentUser(testUser);
        
        // 模拟角色级别检查失败
        when(mockPermissionChecker.hasRoleLevel(testUser.getId(), RoleType.SYSTEM_ADMIN.getLevel()))
                .thenReturn(false);
        
        // 准备测试方法
        Method method = TestService.class.getMethod("adminOnlyMethod");
        
        // 执行测试
        permissionInterceptor.intercept(method, new Object[]{});
    }
    
    @Test
    public void testMultiplePermissions_AND_Logic() throws Exception {
        // 设置当前用户
        SecurityContext.setCurrentUser(testUser);
        
        // 模拟部分权限检查成功
        when(mockPermissionChecker.hasAllPermissions(testUser.getId(), 
                PermissionType.USER_CREATE.getCode(), PermissionType.USER_EDIT.getCode()))
                .thenReturn(false);
        
        // 准备测试方法
        Method method = TestService.class.getMethod("multiplePermissionsANDMethod");
        
        // 执行测试
        try {
            permissionInterceptor.intercept(method, new Object[]{});
            fail("应该抛出权限不足异常");
        } catch (PermissionDeniedException e) {
            // 预期的异常
            assertTrue("异常消息应该包含权限信息", e.getMessage().contains("权限不足"));
        }
    }
    
    @Test
    public void testMultiplePermissions_OR_Logic() throws Exception {
        // 设置当前用户
        SecurityContext.setCurrentUser(testUser);
        
        // 模拟任一权限检查成功
        when(mockPermissionChecker.hasAnyPermission(testUser.getId(), 
                PermissionType.USER_CREATE.getCode(), PermissionType.USER_EDIT.getCode()))
                .thenReturn(true);
        
        // 准备测试方法
        Method method = TestService.class.getMethod("multiplePermissionsORMethod");
        
        // 执行测试
        boolean result = permissionInterceptor.intercept(method, new Object[]{});
        
        // 验证结果
        assertTrue("应该允许访问", result);
    }
    
    @Test
    public void testSecurityContext_ThreadSafety() throws InterruptedException {
        // 测试安全上下文的线程安全性
        final User user1 = new User();
        user1.setId(1L);
        user1.setUsername("user1");
        
        final User user2 = new User();
        user2.setId(2L);
        user2.setUsername("user2");
        
        final boolean[] results = new boolean[2];
        
        // 创建两个线程，分别设置不同的用户
        Thread thread1 = new Thread(() -> {
            SecurityContext.setCurrentUser(user1);
            try {
                Thread.sleep(100); // 模拟一些处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            User currentUser = SecurityContext.getCurrentUser();
            results[0] = (currentUser != null && currentUser.getId().equals(user1.getId()));
            SecurityContext.clear();
        });
        
        Thread thread2 = new Thread(() -> {
            SecurityContext.setCurrentUser(user2);
            try {
                Thread.sleep(100); // 模拟一些处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            User currentUser = SecurityContext.getCurrentUser();
            results[1] = (currentUser != null && currentUser.getId().equals(user2.getId()));
            SecurityContext.clear();
        });
        
        // 启动线程
        thread1.start();
        thread2.start();
        
        // 等待线程完成
        thread1.join();
        thread2.join();
        
        // 验证结果
        assertTrue("线程1应该获取到正确的用户", results[0]);
        assertTrue("线程2应该获取到正确的用户", results[1]);
    }
    
    @Test
    public void testPermissionBypass_ShouldNotWork() {
        // 测试权限绕过攻击
        SecurityContext.setCurrentUser(testUser);
        
        // 尝试直接设置权限（这不应该影响实际的权限检查）
        SecurityContext.setCurrentPermissionCodes(new HashSet<>(Arrays.asList(
                PermissionType.SYSTEM_CONFIG.getCode(),
                PermissionType.USER_CREATE.getCode()
        )));
        
        // 权限检查器应该从数据库查询，而不是从上下文获取
        when(mockPermissionChecker.hasPermission(testUser.getId(), PermissionType.SYSTEM_CONFIG.getCode()))
                .thenReturn(false);
        
        // 验证权限检查仍然失败
        boolean hasPermission = mockPermissionChecker.hasPermission(testUser.getId(), PermissionType.SYSTEM_CONFIG.getCode());
        assertFalse("权限检查不应该被绕过", hasPermission);
    }
    
    @Test
    public void testNullInjection_ShouldBeHandled() {
        // 测试空值注入攻击
        
        // 测试空用户ID
        boolean result1 = mockPermissionChecker.hasPermission(null, PermissionType.USER_CREATE.getCode());
        assertFalse("空用户ID应该返回false", result1);
        
        // 测试空权限代码
        boolean result2 = mockPermissionChecker.hasPermission(testUser.getId(), null);
        assertFalse("空权限代码应该返回false", result2);
        
        // 测试空资源和操作
        boolean result3 = mockPermissionChecker.hasPermission(testUser.getId(), null, null);
        assertFalse("空资源和操作应该返回false", result3);
    }
    
    @Test
    public void testInvalidPermissionCode_ShouldBeHandled() {
        // 测试无效权限代码
        when(mockPermissionChecker.hasPermission(testUser.getId(), "INVALID_PERMISSION"))
                .thenReturn(false);
        
        boolean result = mockPermissionChecker.hasPermission(testUser.getId(), "INVALID_PERMISSION");
        assertFalse("无效权限代码应该返回false", result);
    }
    
    @Test
    public void testDisabledUser_ShouldNotHavePermissions() {
        // 测试禁用用户
        User disabledUser = new User();
        disabledUser.setId(2L);
        disabledUser.setUsername("disabled_user");
        disabledUser.setStatus(0); // 禁用状态
        
        // 即使用户有权限记录，禁用用户也不应该有权限
        when(mockPermissionChecker.hasPermission(disabledUser.getId(), PermissionType.DETECTION_EXECUTE.getCode()))
                .thenReturn(false);
        
        boolean result = mockPermissionChecker.hasPermission(disabledUser.getId(), PermissionType.DETECTION_EXECUTE.getCode());
        assertFalse("禁用用户不应该有权限", result);
    }
    
    // 测试服务类
    public static class TestService {
        
        @RequirePermission("USER_CREATE")
        public void createUserMethod() {
            // 需要用户创建权限的方法
        }
        
        @RequireRole(minLevel = 5) // 系统管理员级别
        public void adminOnlyMethod() {
            // 只有管理员能访问的方法
        }
        
        @RequirePermission(value = {"USER_CREATE", "USER_EDIT"}, logical = RequirePermission.LogicalOperator.AND)
        public void multiplePermissionsANDMethod() {
            // 需要多个权限（AND逻辑）的方法
        }
        
        @RequirePermission(value = {"USER_CREATE", "USER_EDIT"}, logical = RequirePermission.LogicalOperator.OR)
        public void multiplePermissionsORMethod() {
            // 需要多个权限（OR逻辑）的方法
        }
        
        public void protectedMethod() {
            // 受保护的方法（需要认证）
        }
    }
}
