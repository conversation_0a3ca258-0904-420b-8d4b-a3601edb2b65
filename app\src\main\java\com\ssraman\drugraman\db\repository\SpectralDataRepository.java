package com.ssraman.drugraman.db.repository;

import com.ssraman.drugraman.db.entity.SpectralDataInfo;
import com.ssraman.drugraman.db.gen.DaoSession;
import com.ssraman.drugraman.db.gen.SpectralDataInfoDao;
import com.ssraman.lib_common.room.RxRoom;

import java.util.List;
import java.util.concurrent.Callable;

import io.reactivex.Completable;
import io.reactivex.Single;

/**
 * @author: Administrator
 * @date: 2021/10/20
 */
public class SpectralDataRepository {
    private DaoSession mDaoSession;
    private SpectralDataInfoDao spectralDataDao;

    public SpectralDataRepository(DaoSession daoSession) {
        this.mDaoSession = daoSession;
        spectralDataDao = this.mDaoSession.getSpectralDataInfoDao();
    }

    public Completable insertSpecData(SpectralDataInfo spectralDataInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                spectralDataDao.insert(spectralDataInfo);
                return null;
            }
        });
    }

    public long insertSpecDataByRe(SpectralDataInfo spectralDataInfo) {
        return spectralDataDao.insert(spectralDataInfo);
    }

    public Completable deleteSpecData(SpectralDataInfo spectralDataInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                spectralDataDao.delete(spectralDataInfo);
                return null;
            }
        });
    }

    public Completable updateSpecData(SpectralDataInfo spectralDataInfo) {
        return Completable.fromCallable(new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                spectralDataDao.update(spectralDataInfo);
                return null;
            }
        });
    }

    public long updateSpecDataByRe(SpectralDataInfo spectralDataInfo) {
        spectralDataDao.update(spectralDataInfo);
        return spectralDataInfo.getId();
    }


    public Single<SpectralDataInfo> getSpecDataById(long id) {
        return RxRoom.createSingle(new Callable<SpectralDataInfo>() {
            @Override
            public SpectralDataInfo call() throws Exception {
                List<SpectralDataInfo> specDatalist = spectralDataDao.queryBuilder().where(SpectralDataInfoDao.Properties.Id.eq(id)).list();
                return specDatalist.get(0);
            }
        });
    }


    public SpectralDataInfo getSpecDataByIdSimple(long id) {
        List<SpectralDataInfo> specDatalist = spectralDataDao.queryBuilder().where(SpectralDataInfoDao.Properties.Id.eq(id)).list();
        return specDatalist.get(0);
    }

}
