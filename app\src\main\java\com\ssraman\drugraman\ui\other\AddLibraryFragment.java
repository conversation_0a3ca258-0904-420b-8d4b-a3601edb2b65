package com.ssraman.drugraman.ui.other;

import android.app.DatePickerDialog;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bestvike.linq.Linq;
import com.google.android.material.textfield.TextInputEditText;
import com.jeremyliao.liveeventbus.LiveEventBus;
import com.kongzue.dialog.interfaces.OnDismissListener;
import com.kongzue.dialog.util.MessageBoxIcon;
import com.kongzue.dialog.v3.CustomDialog;
import com.kongzue.dialog.v3.MessageDialog;
import com.kongzue.dialog.v3.TipDialog;
import com.kongzue.dialog.v3.WaitDialog;
import com.ssraman.control.spinner.MaterialSpinner;
import com.ssraman.drugraman.BR;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.app.AppApplication;
import com.ssraman.drugraman.base.ExBaseFragment;
import com.ssraman.drugraman.base.GlobalViewModel;
import com.ssraman.drugraman.business.MyCustomException;
import com.ssraman.drugraman.constant.InterFaceConst;
import com.ssraman.drugraman.constant.MacConst;
import com.ssraman.drugraman.custom.SpectrumLineData;
import com.ssraman.drugraman.databinding.FragmentAddLibraryBinding;
import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;
import com.ssraman.drugraman.db.entity.FtInfo;
import com.ssraman.drugraman.db.entity.PeakInfo;
import com.ssraman.drugraman.db.entity.SampleTypeInfo;
import com.ssraman.drugraman.db.entity.User_info;
import com.ssraman.drugraman.newentiry.AddLibCollectInfo;
import com.ssraman.drugraman.newentiry.SampleCollectionInfo;
import com.ssraman.drugraman.typeface.EvaluationModeType;
import com.ssraman.drugraman.typeface.NodeItemType;
import com.ssraman.drugraman.ui.adapter.AddPeakRecyViewAdapter;
import com.ssraman.drugraman.ui.vm.AddLibDetectionDisplayViewModel;
import com.ssraman.drugraman.ui.vm.AddLibraryViewModel;
import com.ssraman.drugraman.ui.vm.MainViewModel;
import com.ssraman.drugraman.util.MyStringUtils;
import com.ssraman.drugraman.util.OperatingAuthority;
import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.util.PermissionUtils;
import com.ssraman.drugraman.util.UIContextUtils;
import com.ssraman.lib_common.mac.DrawerSwichBean;
import com.ssraman.lib_common.mac.SettingPre;
import com.ssraman.lib_common.mac.ToolBarSwichBean;
import com.ssraman.lib_common.utils.SubscribeDataInterface;
import com.ssraman.lib_common.utils.SubscribeInterface;
import com.ssraman.lib_common.utils.ToastUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class AddLibraryFragment extends ExBaseFragment<FragmentAddLibraryBinding, AddLibraryViewModel> {
    private AppCompatActivity me = null;
    private GlobalViewModel shareViewModel;
    private MainViewModel mainViewModel;
    private AddLibDetectionDisplayViewModel detectionDisplayViewModel;

    private AddPeakRecyViewAdapter addPeakRecyViewAdapter;

    private SampleTypeInfo add_current_sampleInfo;
    private AddLibCollectInfo collectInfo;
    private SampleCollectionInfo disp_sample_collection;
    private int add_material_type_index = 0;

    private long maxdate;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        me = (AppCompatActivity) this.getActivity();
    }

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container,
            @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_add_library;
    }

    @Override
    public int initVariableId() {
        return BR.addLibraryViewModel;
    }

    @Override
    public void initData() {
        super.initData();
        binding.setMpresenter(new MPresenter());

        shareViewModel = new ViewModelProvider(getActivity()).get(GlobalViewModel.class);
        mainViewModel = new ViewModelProvider(getActivity()).get(MainViewModel.class);
        viewModel.setLogin_name(shareViewModel.get_Login_data().getValue().getLogin_name());
        viewModel.setPriority(shareViewModel.get_Login_data().getValue().getPriority());
        detectionDisplayViewModel = ((AppApplication) mAppCompatActivity.getApplicationContext())
                .getAppViewModelProvider(mActivity).get(AddLibDetectionDisplayViewModel.class);
        collectInfo = detectionDisplayViewModel.getCollectInfo().getValue();

        disp_sample_collection = new SampleCollectionInfo();

        add_material_type_index = SettingPre.getMaterialTypeIndex();

        //
        viewModel.getMaterial(EvaluationModeType.验证.value());
        viewModel.getMaterialList().observe(this, new Observer<List<SampleTypeInfo>>() {
            @Override
            public void onChanged(List<SampleTypeInfo> materialList) {
                List<SampleTypeInfo> _material_type_list = Linq.of(materialList)
                        .where(p -> p.getType() == (int) NodeItemType.LowLevelDirectory.value()).toList();
                binding.tvMaterialType.setItems(_material_type_list);
                binding.tvMaterialType.setSelectedIndex(add_material_type_index);
            }
        });

        //
        binding.editSampleName.setText(collectInfo.getSample_name());
        binding.editStartWave.setText(String.valueOf(collectInfo.getStart_wave()));
        binding.editRate.setText("0.6");// 匹配比例
        binding.editRange.setText("10");
        binding.editConfidence.setText("0.96");
        binding.specDetectionResult2.setMarkerView(getActivity(), R.layout.custom_marker_view);
        // 初始化对比谱图
        detectionDisplayViewModel.getDetection_spec().observe(this, new Observer<SpectrumLineData>() {
            @Override
            public void onChanged(SpectrumLineData spectrumLineData) {
                if (spectrumLineData != null) {
                    if (!binding.specDetectionResult2.loadMainSpcLine(spectrumLineData)) {
                        ToastUtils.showShort("检测谱图加载失败！");
                    }
                }
            }
        });

        addPeakRecyViewAdapter = new AddPeakRecyViewAdapter(me);
        LinearLayoutManager mManager = new LinearLayoutManager(me, LinearLayoutManager.VERTICAL, false);
        binding.recyclerViewPeak.setLayoutManager(mManager);
        binding.recyclerViewPeak.setAdapter(addPeakRecyViewAdapter);
        detectionDisplayViewModel.getDetection_peaklist().observe(this, new Observer<List<PeakInfo>>() {
            @Override
            public void onChanged(List<PeakInfo> peaklist) {
                if (peaklist.size() > 0) {
                    addPeakRecyViewAdapter.update(peaklist);
                    addPeakRecyViewAdapter.All();
                } else {
                    addPeakRecyViewAdapter.clear();
                }
            }
        });

        mainViewModel.getMacAdress(getActivity());
        maxdate = System.currentTimeMillis() + 1 * 24 * 3600 * 1000L;
    }

    @Override
    public void onResume() {
        super.onResume();

        LiveEventBus.get("toolbar_swich").postAcrossProcess(new ToolBarSwichBean(1, "主菜单"));
        LiveEventBus.get("drawer_swich").postAcrossProcess(new DrawerSwichBean(1, "不使能"));
    }

    private void authenticationMethod(SampleTypeInfo selectedMaterialType, FtInfo add_ft, List<PeakInfo> seleted_list) {
        // 创建FtInfo的深度复制，以避免修改原始对象
        FtInfo authFtInfo = new FtInfo();

        // 复制传入FtInfo的所有属性
        authFtInfo.setId(add_ft.getId());
        authFtInfo.setMark(add_ft.getMark());
        authFtInfo.setSampleTypeId(add_ft.getSampleTypeId());
        authFtInfo.setObWave(add_ft.getObWave());
        authFtInfo.setObIntensity(add_ft.getObIntensity());
        authFtInfo.setObNoise(add_ft.getObNoise());
        authFtInfo.setSendWave(add_ft.getSendWave());
        authFtInfo.setHanLiang(add_ft.getHanLiang());
        authFtInfo.setRate(add_ft.getRate());
        authFtInfo.setType(add_ft.getType());
        authFtInfo.setSampleName(add_ft.getSampleName());
        authFtInfo.setMatchLimit(add_ft.getMatchLimit());
        authFtInfo.setMatchScale(add_ft.getMatchScale());
        authFtInfo.setConfidence(add_ft.getConfidence());
        authFtInfo.setStandard(add_ft.getStandard());
        authFtInfo.setStartWave(add_ft.getStartWave());
        authFtInfo.setIntegratioTime(add_ft.getIntegratioTime());
        authFtInfo.setLaserPower(add_ft.getLaserPower());
        authFtInfo.setAverageCount(add_ft.getAverageCount());
        authFtInfo.setSourceType(add_ft.getSourceType());
        authFtInfo.setDescriptionInformation(add_ft.getDescriptionInformation());
        authFtInfo.setSpecType(add_ft.getSpecType());
        authFtInfo.setInspector(add_ft.getInspector());
        authFtInfo.setInspectorTime(add_ft.getInspectorTime());
        authFtInfo.setInspectorPass(add_ft.getInspectorPass());
        authFtInfo.setPublisherMacAddress(add_ft.getPublisherMacAddress());
        authFtInfo.setReviewer(add_ft.getReviewer());
        authFtInfo.setReviewerTime(add_ft.getReviewerTime());
        authFtInfo.setReviewerPass(add_ft.getReviewerPass());
        authFtInfo.setReviewerMacAddress(add_ft.getReviewerMacAddress());

        CustomDialog.build(me, R.layout.layout_publish_authentication, new CustomDialog.OnBindView() {
            @Override
            public void onBind(final CustomDialog dialog, View v) {
                TextView txt_title = v.findViewById(R.id.txt_signature_title);
                TextInputEditText etPassword = v.findViewById(R.id.et_signer_password);
                MaterialSpinner etuser = v.findViewById(R.id.et_signer);
                TextView btnCancel = v.findViewById(R.id.btn_singer_cancel);
                TextView btnOk = v.findViewById(R.id.btn_singer_ok);

                txt_title.setText("   认证");
                etPassword.setText("");
                List<String> user_name_list = mainViewModel.all_user_name_list.getValue();
                if ((user_name_list != null) && (user_name_list.size() > 0)) {
                    etuser.setItems(user_name_list);
                    etuser.setSelectedIndex(0);
                }
                btnOk.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        String user_name = etuser.getText().toString();
                        String password = etPassword.getText().toString();
                        if (mainViewModel.isUserNameValid(user_name)) {
                            hideKeyboard();
                            mainViewModel.loginAuthentication(user_name, password,
                                    new SubscribeDataInterface<User_info>() {
                                        @Override
                                        public void onSubscribe() {
                                        }

                                        @Override
                                        public void onComplete(User_info singer) {

                                            WaitDialog.dismiss();
                                            addSignatureMethon(singer, selectedMaterialType, authFtInfo, seleted_list);
                                            dialog.doDismiss();
                                        }

                                        @Override
                                        public void onError(Throwable e) {
                                            if (e instanceof MyCustomException) {
                                                MyCustomException myCustomException = (MyCustomException) e;

                                                switch (myCustomException.getCode()) {
                                                    case 201:
                                                        etPassword.setText("");
                                                    case 202:
                                                        MessageDialog.show(me, "提示",
                                                                myCustomException.getDisplayMessage(), "确定",
                                                                MessageBoxIcon.Information);
                                                        break;
                                                }
                                            } else {
                                                TipDialog.show(me, "认证失败！", TipDialog.TYPE.ERROR)
                                                        .setOnDismissListener(new OnDismissListener() {
                                                            @Override
                                                            public void onDismiss() {
                                                            }
                                                        });
                                            }
                                        }
                                    });
                        } else {
                            ToastUtils.showShortSafe("用户名不能为空！");
                        }
                    }
                });
                btnCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.doDismiss();
                    }
                });
            }
        }).setAlign(CustomDialog.ALIGN.DEFAULT).setCancelable(false).show();
    }

    private String mac_adress = "02:00:00:00:00:00";

    private void addSignatureMethon(User_info signer, SampleTypeInfo selectedMaterialType, FtInfo add_ft,
            List<PeakInfo> seleted_list) {
        mac_adress = MacConst.Local_Machine_MAC_ADRESS;

        // 创建FtInfo的深度复制，以避免修改原始对象
        FtInfo signedFtInfo = new FtInfo();

        // 复制传入FtInfo的所有属性
        signedFtInfo.setId(add_ft.getId());
        signedFtInfo.setMark(add_ft.getMark());
        signedFtInfo.setSampleTypeId(add_ft.getSampleTypeId());
        signedFtInfo.setObWave(add_ft.getObWave());
        signedFtInfo.setObIntensity(add_ft.getObIntensity());
        signedFtInfo.setObNoise(add_ft.getObNoise());
        signedFtInfo.setSendWave(add_ft.getSendWave());
        signedFtInfo.setHanLiang(add_ft.getHanLiang());
        signedFtInfo.setRate(add_ft.getRate());
        signedFtInfo.setType(add_ft.getType());
        signedFtInfo.setSampleName(add_ft.getSampleName());
        signedFtInfo.setMatchLimit(add_ft.getMatchLimit());
        signedFtInfo.setMatchScale(add_ft.getMatchScale());
        signedFtInfo.setConfidence(add_ft.getConfidence());
        signedFtInfo.setStandard(add_ft.getStandard());
        signedFtInfo.setStartWave(add_ft.getStartWave());
        signedFtInfo.setIntegratioTime(add_ft.getIntegratioTime());
        signedFtInfo.setLaserPower(add_ft.getLaserPower());
        signedFtInfo.setAverageCount(add_ft.getAverageCount());
        signedFtInfo.setSourceType(add_ft.getSourceType());
        signedFtInfo.setDescriptionInformation(add_ft.getDescriptionInformation());
        signedFtInfo.setSpecType(add_ft.getSpecType());
        signedFtInfo.setInspector(add_ft.getInspector());
        signedFtInfo.setInspectorTime(add_ft.getInspectorTime());
        signedFtInfo.setInspectorPass(add_ft.getInspectorPass());
        signedFtInfo.setPublisherMacAddress(add_ft.getPublisherMacAddress());
        signedFtInfo.setReviewer(add_ft.getReviewer());
        signedFtInfo.setReviewerTime(add_ft.getReviewerTime());
        signedFtInfo.setReviewerPass(add_ft.getReviewerPass());
        signedFtInfo.setReviewerMacAddress(add_ft.getReviewerMacAddress());

        CustomDialog.build(me, R.layout.layout_add_signature, new CustomDialog.OnBindView() {
            @Override
            public void onBind(final CustomDialog dialog, View v) {
                TextView txt_title = v.findViewById(R.id.txt_add_signature_title);
                TextView txt_signer = v.findViewById(R.id.txt_signer);
                TextView txt_mac_adress = v.findViewById(R.id.txt_mac_adress);

                MaterialSpinner et_signature_meaning = v.findViewById(R.id.et_signature_meaning);

                final TextView txt_cal_start = v.findViewById(R.id.txt_cal_start);
                final ImageView imv_cal_start = v.findViewById(R.id.imv_cal_start);
                TextView btnCancel = v.findViewById(R.id.btn_signature_cancel);
                TextView btnOk = v.findViewById(R.id.btn_signature_ok);

                txt_title.setText("   添加署名");
                txt_signer.setText(signer.getLogin_name());
                txt_mac_adress.setText(mac_adress);
                et_signature_meaning.setItems(UIContextUtils.getSignatureItem());
                if ((signedFtInfo.getInspectorPass() == null) || (signedFtInfo.getInspectorPass() == 0)) {
                    et_signature_meaning.setSelectedIndex(0);
                } else {
                    et_signature_meaning.setSelectedIndex(1);
                }

                Calendar calendar = Calendar.getInstance();
                String temp = getResources().getString(R.string.data_str);
                int year_s = calendar.get(Calendar.YEAR);
                int month_s = calendar.get(Calendar.MONTH) + 1;
                int day_s = calendar.get(Calendar.DAY_OF_MONTH);
                String desc1 = String.format(temp, year_s, month_s, day_s);
                txt_cal_start.setText(desc1);
                imv_cal_start.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(year_s, month_s, day_s);
                        DatePickerDialog dialog = new DatePickerDialog(getContext(),
                                new DatePickerDialog.OnDateSetListener() {
                                    @Override
                                    public void onDateSet(DatePicker view, int year,
                                            int month, int day) {
                                        String temp = getResources().getString(R.string.data_str);
                                        String desc = String.format(temp, year, month + 1, day);
                                        txt_cal_start.setText(desc);
                                    }
                                },
                                calendar.get(Calendar.YEAR),
                                calendar.get(Calendar.MONTH),
                                calendar.get(Calendar.DAY_OF_MONTH));

                        DatePicker datePicker = dialog.getDatePicker();
                        datePicker.setMaxDate(maxdate);
                        dialog.show();
                    }
                });

                btnOk.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        String signer_name = txt_signer.getText().toString();
                        String signer_mac_adress = txt_mac_adress.getText().toString();
                        int signature_meaning_selected_index = et_signature_meaning.getSelectedIndex();
                        if ((signedFtInfo.getInspectorPass() == null) || (signedFtInfo.getInspectorPass() == 0)) {
                            if (signature_meaning_selected_index == 1) {
                                MessageDialog.show(me, "提示", "请先发布后再复核署名！", "确定", MessageBoxIcon.Information);
                                return;
                            }
                            signedFtInfo.setInspectorPass(0);
                            signedFtInfo.setReviewerPass(0);
                        }
                        if (signature_meaning_selected_index == 1) {
                            // 检查报告签名权限
                            if (!PermissionUtils.checkRoleLevelWithMessage(mAppCompatActivity, RoleType.REVIEWER)) {
                                return;
                            }
                        }
                        if (signature_meaning_selected_index == 0) {
                            signedFtInfo.setInspector(signer_name);
                            signedFtInfo.setInspectorTime(new Date());
                            signedFtInfo.setPublisherMacAddress(signer_mac_adress);
                            signedFtInfo.setInspectorPass(1);

                            authenticationMethod(selectedMaterialType, signedFtInfo, seleted_list);
                        } else {
                            signedFtInfo.setReviewer(signer_name);
                            signedFtInfo.setReviewerTime(new Date());
                            signedFtInfo.setReviewerMacAddress(signer_mac_adress);
                            signedFtInfo.setReviewerPass(1);

                            MessageDialog.show(me, "提示", "署名认证成功！", "确定", MessageBoxIcon.Information);
                        }

                        if (signedFtInfo.getInspectorPass() == 1 && signedFtInfo.getReviewerPass() == 1) {
                            addFtToLib(selectedMaterialType, signedFtInfo, seleted_list);
                        }
                        dialog.doDismiss();
                    }
                });
                btnCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.doDismiss();
                    }
                });
            }
        }).setAlign(CustomDialog.ALIGN.DEFAULT).setCancelable(false).show();
    }

    private void addFtToLib(SampleTypeInfo selectedMaterialType, FtInfo add_ft, List<PeakInfo> seleted_list) {
        viewModel.addFtSpec(selectedMaterialType, add_ft, seleted_list, disp_sample_collection,
                new SubscribeInterface() {
                    @Override
                    public void onSubscribe() {

                    }

                    @Override
                    public void onComplete() {
                        ToastUtils.showLongSafe("创建新标准谱图成功！");
                        NavController navController = Navigation.findNavController(binding.tvMaterialType);
                        Bundle args = new Bundle();
                        navController.popBackStack();
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (e instanceof MyCustomException) {
                            MyCustomException myCustomException = (MyCustomException) e;

                            switch (myCustomException.getCode()) {
                                case 201:
                                case 202:
                                    ToastUtils.showLongSafe("创建新标准谱图失败，" + myCustomException.getDisplayMessage());
                                    break;
                            }
                        } else {
                            ToastUtils.showLongSafe("创建新标准谱图失败，" + e.getMessage());
                        }
                    }
                });
    }

    public class MPresenter {
        public void BtnDescriptionInfoClick(View view) {
            CustomDialog.build(mAppCompatActivity, R.layout.layout_add_sample_collection_info,
                    new CustomDialog.OnBindView() {
                        @Override
                        public void onBind(CustomDialog dialog, View v) {
                            final EditText tv_polymer_name = v.findViewById(R.id.edit_polymer_name);
                            final EditText tv_cas_number = v.findViewById(R.id.edit_cas_number);
                            final EditText tv_substance = v.findViewById(R.id.edit_substance);
                            final EditText tv_trade_name = v.findViewById(R.id.edit_trade_name);
                            final EditText tv_supplier = v.findViewById(R.id.edit_supplier);
                            final EditText tv_filler = v.findViewById(R.id.edit_filler);
                            final EditText tv_filler_content = v.findViewById(R.id.edit_filler_content);
                            final EditText tv_color = v.findViewById(R.id.edit_color);
                            final EditText tv_processing_method = v.findViewById(R.id.edit_processing_method);
                            final EditText tv_applications = v.findViewById(R.id.edit_applications);
                            final EditText tv_meltionPoint = v.findViewById(R.id.edit_meltionPoint);
                            if (disp_sample_collection != null) {
                                if (!MyStringUtils.isNull(disp_sample_collection.getPolymer())) {
                                    tv_polymer_name.setText(disp_sample_collection.getPolymer());
                                } else {
                                    tv_polymer_name.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getCAS())) {
                                    tv_cas_number.setText(disp_sample_collection.getCAS());
                                } else {
                                    tv_cas_number.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getSubstance())) {
                                    tv_substance.setText(disp_sample_collection.getSubstance());
                                } else {
                                    tv_substance.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getTradeName())) {
                                    tv_trade_name.setText(disp_sample_collection.getTradeName());
                                } else {
                                    tv_trade_name.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getSupplier())) {
                                    tv_supplier.setText(disp_sample_collection.getSupplier());
                                } else {
                                    tv_supplier.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getFiller())) {
                                    tv_filler.setText(disp_sample_collection.getFiller());
                                } else {
                                    tv_filler.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getFillerContent())) {
                                    tv_filler_content.setText(disp_sample_collection.getFillerContent());
                                } else {
                                    tv_filler_content.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getColor())) {
                                    tv_color.setText(disp_sample_collection.getColor());
                                } else {
                                    tv_color.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getProcessingMethod())) {
                                    tv_processing_method.setText(disp_sample_collection.getProcessingMethod());
                                } else {
                                    tv_processing_method.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getApplications())) {
                                    tv_applications.setText(disp_sample_collection.getApplications());
                                } else {
                                    tv_applications.setText("");
                                }

                                if (!MyStringUtils.isNull(disp_sample_collection.getMeltionPoint())) {
                                    tv_meltionPoint.setText(disp_sample_collection.getMeltionPoint());
                                } else {
                                    tv_meltionPoint.setText(" ");
                                }
                            } else {
                                disp_sample_collection = new SampleCollectionInfo();
                            }
                            TextView btnCancel = v.findViewById(R.id.btn_a_collection_cancel);
                            TextView btnok = v.findViewById(R.id.btn_a_collection_ok);

                            btnCancel.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    dialog.doDismiss();
                                }
                            });
                            btnok.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    if (tv_polymer_name.getText() != null) {
                                        disp_sample_collection.setPolymer(tv_polymer_name.getText().toString());
                                    } else {
                                        disp_sample_collection.setPolymer("");
                                    }

                                    if (tv_cas_number.getText() != null) {
                                        disp_sample_collection.setCAS(tv_cas_number.getText().toString());
                                    } else {
                                        disp_sample_collection.setCAS("");
                                    }

                                    if (tv_substance.getText() != null) {
                                        disp_sample_collection.setSubstance(tv_substance.getText().toString());
                                    } else {
                                        disp_sample_collection.setSubstance("");
                                    }

                                    if (tv_trade_name.getText() != null) {
                                        disp_sample_collection.setTradeName(tv_trade_name.getText().toString());
                                    } else {
                                        disp_sample_collection.setTradeName("");
                                    }

                                    if (tv_supplier.getText() != null) {
                                        disp_sample_collection.setSupplier(tv_supplier.getText().toString());
                                    } else {
                                        disp_sample_collection.setSupplier("");
                                    }

                                    if (tv_filler.getText() != null) {
                                        disp_sample_collection.setFiller(tv_filler.getText().toString());
                                    } else {
                                        disp_sample_collection.setFiller("");
                                    }

                                    if (tv_filler_content.getText() != null) {
                                        disp_sample_collection.setFillerContent(tv_filler_content.getText().toString());
                                    } else {
                                        disp_sample_collection.setFillerContent("");
                                    }

                                    if (tv_color.getText() != null) {
                                        disp_sample_collection.setColor(tv_color.getText().toString());
                                    } else {
                                        disp_sample_collection.setColor("");
                                    }

                                    if (tv_processing_method.getText() != null) {
                                        disp_sample_collection
                                                .setProcessingMethod(tv_processing_method.getText().toString());
                                    } else {
                                        disp_sample_collection.setProcessingMethod("");
                                    }

                                    if (tv_applications.getText() != null) {
                                        disp_sample_collection.setApplications(tv_applications.getText().toString());
                                    } else {
                                        disp_sample_collection.setApplications("");
                                    }

                                    if (tv_meltionPoint.getText() != null) {
                                        disp_sample_collection.setMeltionPoint(tv_meltionPoint.getText().toString());
                                    } else {
                                        disp_sample_collection.setMeltionPoint("");
                                    }

                                    dialog.doDismiss();
                                }
                            });
                        }
                    }).setAlign(CustomDialog.ALIGN.DEFAULT).show();
        }

        public void BtnAddLibClick(View view) {
            mac_adress = MacConst.Local_Machine_MAC_ADRESS;
            // 物质名称有，就在项下面追加，没有就新建，系统库项不允许用户添加
            SampleTypeInfo selectedMaterialType = binding.tvMaterialType.getSelectedItem();
            List<PeakInfo> seleted_list = addPeakRecyViewAdapter.getSeletedItem();

            // 获取原始FtInfo对象
            FtInfo original_ft = detectionDisplayViewModel.getDetectionSpecData().getValue().getFtInfo();

            // 创建FtInfo的深度复制，以避免修改原始对象
            FtInfo add_ft = new FtInfo();

            // 复制原始FtInfo的所有属性
            add_ft.setId(null); // 新对象ID设为null，让数据库自动生成
            add_ft.setMark(original_ft.getMark());
            add_ft.setSampleTypeId(original_ft.getSampleTypeId());
            add_ft.setObWave(original_ft.getObWave());
            add_ft.setObIntensity(original_ft.getObIntensity());
            add_ft.setObNoise(original_ft.getObNoise());
            add_ft.setSendWave(original_ft.getSendWave());
            add_ft.setHanLiang(original_ft.getHanLiang());
            add_ft.setRate(original_ft.getRate());
            add_ft.setType(original_ft.getType());
            add_ft.setSampleName(original_ft.getSampleName());
            add_ft.setMatchLimit(original_ft.getMatchLimit());
            add_ft.setMatchScale(original_ft.getMatchScale());
            add_ft.setConfidence(original_ft.getConfidence());
            add_ft.setStandard(original_ft.getStandard());
            add_ft.setStartWave(original_ft.getStartWave());
            add_ft.setIntegratioTime(original_ft.getIntegratioTime());
            add_ft.setLaserPower(original_ft.getLaserPower());
            add_ft.setAverageCount(original_ft.getAverageCount());
            add_ft.setSourceType(original_ft.getSourceType());
            add_ft.setDescriptionInformation(original_ft.getDescriptionInformation());
            add_ft.setSpecType(original_ft.getSpecType());
            add_ft.setInspector(original_ft.getInspector());
            add_ft.setInspectorTime(original_ft.getInspectorTime());
            add_ft.setInspectorPass(original_ft.getInspectorPass());
            add_ft.setPublisherMacAddress(original_ft.getPublisherMacAddress());
            add_ft.setReviewer(original_ft.getReviewer());
            add_ft.setReviewerTime(original_ft.getReviewerTime());
            add_ft.setReviewerPass(original_ft.getReviewerPass());
            add_ft.setReviewerMacAddress(original_ft.getReviewerMacAddress());

            try {
                if (binding.editSampleName.getText() != null) {
                    if (binding.editSampleName.getText().toString().trim().equals("")) {
                        ToastUtils.showLongSafe("必须输入谱图名称！");
                        return;
                    }
                    add_ft.setSampleName(binding.editSampleName.getText().toString().trim());
                } else {
                    ToastUtils.showLongSafe("必须输入谱图名称！");
                    return;
                }

                if (binding.editStartWave.getText() != null) {
                    int start_wave = Integer.parseInt(binding.editStartWave.getText().toString().trim());
                    if (start_wave < InterFaceConst.start_wave_start || start_wave > InterFaceConst.start_wave_end) {
                        add_ft.setStartWave(start_wave);
                    } else {
                        add_ft.setStartWave(260);
                    }
                } else {
                    add_ft.setStartWave(260);
                }

                if (binding.editRate.getText() != null) {
                    double match_scale = Double.parseDouble(binding.editRate.getText().toString().trim());
                    if (match_scale < InterFaceConst.match_scale_min || match_scale > InterFaceConst.match_scale_max) {
                        add_ft.setMatchScale(match_scale);
                    } else {
                        add_ft.setMatchScale(0.7);
                    }
                } else {
                    add_ft.setMatchScale(0.7);
                }

                if (binding.editRange.getText() != null) {
                    double match_limit = Double.parseDouble(binding.editRange.getText().toString().trim());
                    if (match_limit < InterFaceConst.match_limit_min || match_limit > InterFaceConst.match_limit_max) {
                        add_ft.setMatchLimit(match_limit);
                    } else {
                        add_ft.setMatchLimit(10.0);
                    }
                } else {
                    add_ft.setMatchLimit(10.0);
                }

                if (binding.editConfidence.getText() != null) {
                    double confidence = Double.parseDouble(binding.editConfidence.getText().toString().trim());
                    if (confidence < InterFaceConst.confidence_min || confidence > InterFaceConst.confidence_max) {
                        add_ft.setConfidence(confidence);
                    } else {
                        add_ft.setConfidence(0.95);
                    }
                } else {
                    add_ft.setConfidence(0.95);
                }
                add_ft.setStandard(1);
                add_ft.setType(1);
                add_ft.setSendWave(InterFaceConst.SEND_WAVE);

                add_ft.setMark("");
                add_ft.setHanLiang(0.0);
                add_ft.setRate(0.0);
                add_ft.setSourceType(0);
                add_ft.setSpecType(0);
                // 增加签名认证及存储
                // 检查谱图库管理权限
                if (PermissionUtils.checkPermissionWithMessage(mAppCompatActivity, PermissionType.LIBRARY_MANAGE, "您没有管理谱图库的权限")) {
                    add_ft.setReviewer("厂家");
                    add_ft.setReviewerTime(new Date());
                    add_ft.setReviewerMacAddress(mac_adress);
                    add_ft.setReviewerPass(1);
                    add_ft.setInspector("厂家");
                    add_ft.setInspectorTime(new Date());
                    add_ft.setPublisherMacAddress(mac_adress);
                    add_ft.setInspectorPass(1);
                    add_ft.setStandard(0);
                    addFtToLib(selectedMaterialType, add_ft, seleted_list);
                } else {
                    if (SettingPre.getValidationOption() == 1) {
                        authenticationMethod(selectedMaterialType, add_ft, seleted_list);
                    } else {
                        addFtToLib(selectedMaterialType, add_ft, seleted_list);
                    }
                }

            } catch (Exception ex) {
                ToastUtils.showLongSafe("谱图参数输入不符合规范，请检查正确后再更新");
                return;
            }

        }

    }

}