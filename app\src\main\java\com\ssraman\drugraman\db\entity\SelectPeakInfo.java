package com.ssraman.drugraman.db.entity;

public class SelectPeakInfo  implements Comparable<SelectPeakInfo>{

    private int MatchNum;
    private int MustMatchNum;
    private double Wave;
    private int FtId;
    private int PeakCount;
    private int MustNum;
    private double PeakMatch;
    private String SampleName;
    private int SampleTypeId;

    public int getMatchNum() {
        return MatchNum;
    }

    public void setMatchNum(int matchNum) {
        this.MatchNum = matchNum;
    }

    public int getMustMatchNum() {
        return MustMatchNum;
    }

    public void setMustMatchNum(int mustMatchNum) {
        MustMatchNum = mustMatchNum;
    }

    public double getWave() {
        return Wave;
    }

    public void setWave(double wave) {
        Wave = wave;
    }

    public int getFtId() {
        return FtId;
    }

    public void setFtId(int ftId) {
        FtId = ftId;
    }

    public int getPeakCount() {
        return PeakCount;
    }

    public void setPeakCount(int peakCount) {
        PeakCount = peakCount;
    }

    public int getMustNum() {
        return MustNum;
    }

    public void setMustNum(int mustNum) {
        MustNum = mustNum;
    }

    public double getPeakMatch() {
        return PeakMatch;
    }

    public void setPeakMatch(double peakMatch) {
        PeakMatch = peakMatch;
    }

    public String getSampleName() {
        return SampleName;
    }

    public void setSampleName(String sampleName) {
        SampleName = sampleName;
    }

    public int getSampleTypeId() {
        return SampleTypeId;
    }

    public void setSampleTypeId(int sampleTypeId) {
        SampleTypeId = sampleTypeId;
    }

    @Override
    public int compareTo(SelectPeakInfo selectPeakInfo) {
//        return selectPeakInfo.getSampleTypeId().compareTo(this.getSampleTypeId());
        int V1 =this.getSampleTypeId()-selectPeakInfo.getSampleTypeId();
        return V1;
//        if(V1==0) {
//            return matchingResponse.getExRatio().compareTo(this.getExRatio());
//        }
//        return matchingResponse.getCompareCount().compareTo(this.getCompareCount());
    }
}
