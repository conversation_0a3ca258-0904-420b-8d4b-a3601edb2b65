package com.ssraman.drugraman.db.entity;

import java.util.Date;

/**
 * @author: Administrator
 * @date: 2022/3/3
 */
public class AuthenticationInfo {

    //检验确认人员（发布人）
    private String Inspector;

    //检验确认时间
    private Date InspectorTime;

    //是否检验确认通过
    private Integer InspectorPass;

    //检验MAC地址（发布人）
    private String PublisherMacAddress;

    //审核确认人员 （复核）
    private String Reviewer;

    //审核确认时间
    private Date ReviewerTime;

    //是否审核确认通过
    private Integer ReviewerPass;

    //审核MAC地址（复核）
    private String ReviewerMacAddress;

    //检测认证结果  人工确认后的结果
    private Integer CertificationResult;

    public String getInspector() {
        if(Inspector==null)
        {
            return "";
        }
        return Inspector;
    }

    public void setInspector(String inspector) {
        Inspector = inspector;
    }

    public Date getInspectorTime() {
        return InspectorTime;
    }

    public void setInspectorTime(Date inspectorTime) {
        InspectorTime = inspectorTime;
    }

    public Integer getInspectorPass() {
        if(InspectorPass==null)
        {
            return 0;
        }
        return InspectorPass;
    }

    public void setInspectorPass(Integer inspectorPass) {
        InspectorPass = inspectorPass;
    }

    public String getPublisherMacAddress() {
        if(PublisherMacAddress==null)
        {
            return "";
        }
        return PublisherMacAddress;
    }

    public void setPublisherMacAddress(String publisherMacAddress) {
        PublisherMacAddress = publisherMacAddress;
    }

    public String getReviewer() {
        if(Reviewer==null)
        {
            return "";
        }
        return Reviewer;
    }

    public void setReviewer(String reviewer) {
        Reviewer = reviewer;
    }

    public Date getReviewerTime() {
        return ReviewerTime;
    }

    public void setReviewerTime(Date reviewerTime) {
        ReviewerTime = reviewerTime;
    }

    public Integer getReviewerPass() {
        if(ReviewerPass==null)
        {
            return 0;
        }
        return ReviewerPass;
    }

    public void setReviewerPass(Integer reviewerPass) {
        ReviewerPass = reviewerPass;
    }

    public String getReviewerMacAddress() {
        if(ReviewerMacAddress==null)
        {
            return "";
        }
        return ReviewerMacAddress;
    }

    public void setReviewerMacAddress(String reviewerMacAddress) {
        ReviewerMacAddress = reviewerMacAddress;
    }

    public Integer getCertificationResult() {
        if(CertificationResult==null)
        {
            return 0;
        }
        return CertificationResult;
    }

    public void setCertificationResult(Integer certificationResult) {
        CertificationResult = certificationResult;
    }

    public void Clear() {
        Inspector = "";
        InspectorPass = 0;
        PublisherMacAddress = "";
        Reviewer = "";
        ReviewerPass = 0;
        ReviewerMacAddress = "";
        CertificationResult = 0;
    }

}
