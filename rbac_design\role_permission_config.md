# 角色权限配置设计

## 角色定义

### 1. 操作员 (Operator)
- **角色代码**: `OPERATOR`
- **权限级别**: 1
- **权限范围**: 仅可执行检测操作
- **具体权限**:
  - `DETECTION_EXECUTE` - 执行药物拉曼光谱检测
  - `DETECTION_VIEW_RESULT` - 查看检测结果
  - `DETECTION_EXPORT_BASIC` - 导出基础检测数据

### 2. 审核员 (Reviewer)
- **角色代码**: `REVIEWER`
- **权限级别**: 2
- **权限范围**: 检测 + 报告管理
- **具体权限**:
  - 继承操作员所有权限
  - `REPORT_CREATE` - 创建检测报告
  - `REPORT_PUBLISH` - 发布检测报告
  - `REPORT_REVIEW` - 复审已发布的报告
  - `REPORT_APPROVE` - 审批报告
  - `REPORT_REJECT` - 拒绝报告
  - `REPORT_SIGN` - 报告签名
  - `ARCHIVE_VIEW` - 查看档案记录
  - `ARCHIVE_SEARCH` - 搜索档案记录

### 3. 谱图管理员 (Spectrum Manager)
- **角色代码**: `SPECTRUM_MANAGER`
- **权限级别**: 3
- **权限范围**: 检测 + 谱图库管理
- **具体权限**:
  - 继承操作员所有权限
  - `SPECTRUM_CREATE` - 创建拉曼谱图
  - `SPECTRUM_EDIT` - 编辑谱图数据
  - `SPECTRUM_DELETE` - 删除谱图数据
  - `SPECTRUM_IMPORT` - 导入标准谱图
  - `SPECTRUM_EXPORT` - 导出谱图数据
  - `LIBRARY_MANAGE` - 管理谱图库
  - `STANDARD_MAINTAIN` - 维护标准谱图数据
  - `CALIBRATION_MANAGE` - 管理校准数据

### 4. 用户管理员 (User Administrator)
- **角色代码**: `USER_ADMIN`
- **权限级别**: 4
- **权限范围**: 仅用户账号管理
- **具体权限**:
  - `USER_CREATE` - 创建用户账号
  - `USER_EDIT` - 编辑用户信息
  - `USER_DELETE` - 删除用户账号
  - `USER_VIEW` - 查看用户列表
  - `USER_SEARCH` - 搜索用户
  - `ROLE_ASSIGN` - 分配角色权限
  - `ROLE_REVOKE` - 撤销角色权限
  - `USER_STATUS_MANAGE` - 管理用户状态（启用/禁用）
  - `PASSWORD_RESET` - 重置用户密码

### 5. 系统管理员 (System Administrator)
- **角色代码**: `SYSTEM_ADMIN`
- **权限级别**: 5
- **权限范围**: 所有系统权限
- **具体权限**:
  - 继承所有其他角色的权限
  - `SYSTEM_CONFIG` - 系统配置管理
  - `SYSTEM_BACKUP` - 系统备份
  - `SYSTEM_RESTORE` - 系统恢复
  - `LOG_VIEW` - 查看系统日志
  - `LOG_EXPORT` - 导出日志
  - `AUDIT_VIEW` - 查看审计日志
  - `PERMISSION_MANAGE` - 权限管理
  - `ROLE_MANAGE` - 角色管理
  - `DATABASE_MANAGE` - 数据库管理
  - `SYSTEM_MONITOR` - 系统监控

## 权限资源分类

### 检测相关 (DETECTION)
- `DETECTION_EXECUTE` - 执行检测
- `DETECTION_VIEW_RESULT` - 查看结果
- `DETECTION_EXPORT_BASIC` - 导出基础数据

### 报告相关 (REPORT)
- `REPORT_CREATE` - 创建报告
- `REPORT_PUBLISH` - 发布报告
- `REPORT_REVIEW` - 复审报告
- `REPORT_APPROVE` - 审批报告
- `REPORT_REJECT` - 拒绝报告
- `REPORT_SIGN` - 报告签名

### 谱图相关 (SPECTRUM)
- `SPECTRUM_CREATE` - 创建谱图
- `SPECTRUM_EDIT` - 编辑谱图
- `SPECTRUM_DELETE` - 删除谱图
- `SPECTRUM_IMPORT` - 导入谱图
- `SPECTRUM_EXPORT` - 导出谱图
- `LIBRARY_MANAGE` - 管理谱图库
- `STANDARD_MAINTAIN` - 维护标准谱图
- `CALIBRATION_MANAGE` - 管理校准数据

### 用户相关 (USER)
- `USER_CREATE` - 创建用户
- `USER_EDIT` - 编辑用户
- `USER_DELETE` - 删除用户
- `USER_VIEW` - 查看用户
- `USER_SEARCH` - 搜索用户
- `ROLE_ASSIGN` - 分配角色
- `ROLE_REVOKE` - 撤销角色
- `USER_STATUS_MANAGE` - 管理用户状态
- `PASSWORD_RESET` - 重置密码

### 系统相关 (SYSTEM)
- `SYSTEM_CONFIG` - 系统配置
- `SYSTEM_BACKUP` - 系统备份
- `SYSTEM_RESTORE` - 系统恢复
- `LOG_VIEW` - 查看日志
- `LOG_EXPORT` - 导出日志
- `AUDIT_VIEW` - 查看审计日志
- `PERMISSION_MANAGE` - 权限管理
- `ROLE_MANAGE` - 角色管理
- `DATABASE_MANAGE` - 数据库管理
- `SYSTEM_MONITOR` - 系统监控

### 档案相关 (ARCHIVE)
- `ARCHIVE_VIEW` - 查看档案
- `ARCHIVE_SEARCH` - 搜索档案
- `ARCHIVE_EXPORT` - 导出档案

## 权限继承规则

1. **级别继承**: 高级别角色自动继承低级别角色的权限
2. **功能继承**: 某些角色可以继承特定功能模块的权限
3. **组合权限**: 用户可以同时拥有多个角色，权限取并集

## 权限验证规则

1. **最小权限原则**: 用户只能获得完成工作所需的最小权限
2. **权限分离**: 关键操作需要多个权限组合验证
3. **时效性**: 某些权限可以设置过期时间
4. **审计追踪**: 所有权限操作都需要记录审计日志
