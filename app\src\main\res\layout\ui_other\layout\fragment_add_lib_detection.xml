<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="addLibDetectionViewModel"
            type="com.ssraman.drugraman.ui.vm.AddLibDetectionViewModel" />
    </data>

    <RelativeLayout
        android:id="@+id/rlv_progress"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/nc_main"
        android:keepScreenOn="true"
        tools:context=".ui.other.AddLibDetectionFragment">

        <com.ssraman.control.progressbar.CircleProgressBar
            android:id="@+id/progress_detection"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_marginLeft="80dp"
            android:layout_marginRight="80dp"
            android:layout_marginBottom="100dp"
            app:textPaintSize="28sp"
            app:textStatusSize="28sp" />

        <ImageView
            android:id="@+id/img_warn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/progress_detection"
            android:layout_centerInParent="true"
            android:layout_marginBottom="20dp"
            app:srcCompat="@drawable/icon_warn" />

    </RelativeLayout>
</layout>