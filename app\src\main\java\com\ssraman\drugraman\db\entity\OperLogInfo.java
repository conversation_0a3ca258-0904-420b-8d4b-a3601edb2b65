package com.ssraman.drugraman.db.entity;

import com.ssraman.drugraman.db.StringDateConverter;

import org.greenrobot.greendao.annotation.Convert;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;

import java.util.Date;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Transient;

/**
 * @author: Administrator
 * @date: 2021/10/13
 */
@Entity(nameInDb = "OperLogTable", createInDb = false)
public class OperLogInfo {
    @Id(autoincrement = true)
    @Property(nameInDb = "Id")
    private Long Id;

    //操作者
    @Property(nameInDb = "Operator")
    private String Operator;
    //操作类别
    @Property(nameInDb = "OperatorType")
    private Integer OperatorType;
    //操作时间
    @Property(nameInDb = "OperatorTime")
    @Convert(converter = StringDateConverter.class, columnType = String.class)
    private Date OperatorTime;
    //操作详细描述
    @Property(nameInDb = "OperatorDescription")
    private String OperatorDescription;

    //预留
    @Property(nameInDb = "Reserve")
    private byte[] Reserve1;

    /**
     * 是否选中 购删除使用
     */
    @Transient
    private boolean isCheck;

    @Generated(hash = 1943229787)
    public OperLogInfo(Long Id, String Operator, Integer OperatorType,
            Date OperatorTime, String OperatorDescription, byte[] Reserve1) {
        this.Id = Id;
        this.Operator = Operator;
        this.OperatorType = OperatorType;
        this.OperatorTime = OperatorTime;
        this.OperatorDescription = OperatorDescription;
        this.Reserve1 = Reserve1;
    }

    @Generated(hash = 336031114)
    public OperLogInfo() {
    }

    public Long getId() {
        return this.Id;
    }

    public void setId(Long Id) {
        this.Id = Id;
    }

    public String getOperator() {
        return this.Operator;
    }

    public void setOperator(String Operator) {
        this.Operator = Operator;
    }

    public Integer getOperatorType() {
        return this.OperatorType;
    }

    public void setOperatorType(Integer OperatorType) {
        this.OperatorType = OperatorType;
    }

    public Date getOperatorTime() {
        return this.OperatorTime;
    }

    public void setOperatorTime(Date OperatorTime) {
        this.OperatorTime = OperatorTime;
    }

    public String getOperatorDescription() {
        return this.OperatorDescription;
    }

    public void setOperatorDescription(String OperatorDescription) {
        this.OperatorDescription = OperatorDescription;
    }

    public byte[] getReserve1() {
        return this.Reserve1;
    }

    public void setReserve1(byte[] Reserve1) {
        this.Reserve1 = Reserve1;
    }

    public boolean isCheck() {
        return isCheck;
    }

    public void setCheck(boolean check) {
        isCheck = check;
    }
}

