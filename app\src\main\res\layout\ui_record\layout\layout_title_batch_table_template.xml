<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="25dp"
        android:layout_height="550dp"
        android:background="@color/white">

        <TextView
            android:id="@+id/t1_name"
            android:layout_width="200dp"
            android:layout_height="18dp"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="308dp"
            android:rotation="270"
            android:text="@string/title_raman_batch_analysis_report"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
