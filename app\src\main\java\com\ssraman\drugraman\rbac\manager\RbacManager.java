package com.ssraman.drugraman.rbac.manager;

import android.content.Context;
import android.util.Log;

import com.ssraman.drugraman.db.entity.RbacUser;
import com.ssraman.drugraman.rbac.annotation.RequirePermission;
import com.ssraman.drugraman.rbac.annotation.RequireRole;
import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.security.PermissionChecker;
import com.ssraman.drugraman.rbac.security.SecurityContext;
import com.ssraman.drugraman.rbac.security.PermissionCache;
import com.ssraman.drugraman.rbac.service.IRbacUserService;
import com.ssraman.drugraman.rbac.service.IRbacRoleService;
import com.ssraman.drugraman.rbac.service.IRbacPermissionService;

import java.lang.reflect.Method;

/**
 * RBAC权限管理器
 * 提供统一的权限管理和验证功能
 */
public class RbacManager {
    
    private static final String TAG = "RbacManager";
    private static RbacManager instance;
    
    private Context context;
    private IRbacUserService userService;
    private IRbacRoleService roleService;
    private IRbacPermissionService permissionService;
    private PermissionChecker permissionChecker;
    private PermissionCache permissionCache;
    
    private RbacManager(Context context) {
        this.context = context.getApplicationContext();
        this.permissionCache = new PermissionCache();
        // 这里需要初始化服务实例，暂时留空，等服务实现完成后再填充
        // this.userService = new RbacUserServiceImpl();
        // this.roleService = new RbacRoleServiceImpl();
        // this.permissionService = new RbacPermissionServiceImpl();
        // this.permissionChecker = new PermissionChecker(permissionService, roleService, permissionCache);
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized RbacManager getInstance(Context context) {
        if (instance == null) {
            instance = new RbacManager(context);
        }
        return instance;
    }
    
    /**
     * 获取单例实例（需要先初始化）
     */
    public static RbacManager getInstance() {
        if (instance == null) {
            throw new IllegalStateException("RbacManager未初始化，请先调用getInstance(Context)");
        }
        return instance;
    }
    
    /**
     * 检查用户是否具有指定权限
     */
    public boolean hasPermission(String permissionCode) {
        Long userId = SecurityContext.getCurrentUserId();
        if (userId == null || permissionChecker == null) {
            return false;
        }
        return permissionChecker.hasPermission(userId, permissionCode);
    }
    
    /**
     * 检查用户是否具有指定权限类型
     */
    public boolean hasPermission(PermissionType permissionType) {
        return hasPermission(permissionType.getCode());
    }
    
    /**
     * 检查用户是否具有指定资源和操作的权限
     */
    public boolean hasPermission(String resource, String action) {
        Long userId = SecurityContext.getCurrentUserId();
        if (userId == null || permissionChecker == null) {
            return false;
        }
        return permissionChecker.hasPermission(userId, resource, action);
    }
    
    /**
     * 检查用户是否具有指定角色
     */
    public boolean hasRole(RoleType roleType) {
        Long userId = SecurityContext.getCurrentUserId();
        if (userId == null || permissionChecker == null) {
            return false;
        }
        return permissionChecker.hasRole(userId, roleType);
    }
    
    /**
     * 检查用户是否满足角色级别要求
     */
    public boolean hasRoleLevel(int requiredLevel) {
        Long userId = SecurityContext.getCurrentUserId();
        if (userId == null || permissionChecker == null) {
            return false;
        }
        return permissionChecker.hasRoleLevel(userId, requiredLevel);
    }
    
    /**
     * 验证方法权限
     */
    public boolean validateMethodPermission(Method method) {
        try {
            // 检查方法级别的权限注解
            RequirePermission methodPermissionAnnotation = method.getAnnotation(RequirePermission.class);
            RequireRole methodRoleAnnotation = method.getAnnotation(RequireRole.class);
            
            // 检查类级别的权限注解
            Class<?> declaringClass = method.getDeclaringClass();
            RequirePermission classPermissionAnnotation = declaringClass.getAnnotation(RequirePermission.class);
            RequireRole classRoleAnnotation = declaringClass.getAnnotation(RequireRole.class);
            
            // 验证权限注解
            if (methodPermissionAnnotation != null) {
                return validatePermissionAnnotation(methodPermissionAnnotation);
            } else if (classPermissionAnnotation != null) {
                return validatePermissionAnnotation(classPermissionAnnotation);
            }
            
            // 验证角色注解
            if (methodRoleAnnotation != null) {
                return validateRoleAnnotation(methodRoleAnnotation);
            } else if (classRoleAnnotation != null) {
                return validateRoleAnnotation(classRoleAnnotation);
            }
            
            // 没有权限注解，默认允许
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "权限验证失败", e);
            return false;
        }
    }
    
    /**
     * 验证权限注解
     */
    private boolean validatePermissionAnnotation(RequirePermission annotation) {
        String[] permissionCodes = getPermissionCodes(annotation);
        
        if (permissionCodes.length == 0) {
            return true; // 没有指定权限要求
        }
        
        Long userId = SecurityContext.getCurrentUserId();
        if (userId == null || permissionChecker == null) {
            return false;
        }
        
        if (annotation.logical() == RequirePermission.LogicalOperator.AND) {
            // AND逻辑：需要所有权限
            return permissionChecker.hasAllPermissions(userId, permissionCodes);
        } else {
            // OR逻辑：需要任一权限
            return permissionChecker.hasAnyPermission(userId, permissionCodes);
        }
    }
    
    /**
     * 验证角色注解
     */
    private boolean validateRoleAnnotation(RequireRole annotation) {
        // 检查最低角色级别
        if (annotation.minLevel() > 0) {
            if (!hasRoleLevel(annotation.minLevel())) {
                return false;
            }
        }
        
        // 获取角色代码列表
        String[] roleCodes = getRoleCodes(annotation);
        
        if (roleCodes.length == 0) {
            return true; // 没有指定角色要求
        }
        
        if (annotation.logical() == RequireRole.LogicalOperator.AND) {
            // AND逻辑：需要所有角色
            for (String roleCode : roleCodes) {
                if (!SecurityContext.hasRole(roleCode)) {
                    return false;
                }
            }
            return true;
        } else {
            // OR逻辑：需要任一角色
            for (String roleCode : roleCodes) {
                if (SecurityContext.hasRole(roleCode)) {
                    return true;
                }
            }
            return false;
        }
    }
    
    /**
     * 获取权限代码列表
     */
    private String[] getPermissionCodes(RequirePermission annotation) {
        // 优先使用value属性
        if (annotation.value().length > 0) {
            return annotation.value();
        }
        
        // 其次使用permissions属性
        if (annotation.permissions().length > 0) {
            return annotation.permissions();
        }
        
        // 最后使用resource和action组合
        if (!annotation.resource().isEmpty() && !annotation.action().isEmpty()) {
            String permissionCode = annotation.resource().toUpperCase() + "_" + annotation.action().toUpperCase();
            return new String[]{permissionCode};
        }
        
        return new String[0];
    }
    
    /**
     * 获取角色代码列表
     */
    private String[] getRoleCodes(RequireRole annotation) {
        // 优先使用value属性
        if (annotation.value().length > 0) {
            return annotation.value();
        }
        
        // 其次使用roles属性
        if (annotation.roles().length > 0) {
            return annotation.roles();
        }
        
        return new String[0];
    }
    
    /**
     * 清除用户权限缓存
     */
    public void clearUserPermissionCache(Long userId) {
        if (permissionChecker != null) {
            permissionChecker.clearUserPermissionCache(userId);
        }
    }
    
    /**
     * 清除所有权限缓存
     */
    public void clearAllPermissionCache() {
        if (permissionChecker != null) {
            permissionChecker.clearAllPermissionCache();
        }
    }
    
    /**
     * 获取权限检查器
     */
    public PermissionChecker getPermissionChecker() {
        return permissionChecker;
    }
    
    /**
     * 获取用户服务
     */
    public IRbacUserService getUserService() {
        return userService;
    }
    
    /**
     * 获取角色服务
     */
    public IRbacRoleService getRoleService() {
        return roleService;
    }
    
    /**
     * 获取权限服务
     */
    public IRbacPermissionService getPermissionService() {
        return permissionService;
    }
    
    /**
     * 销毁管理器
     */
    public void destroy() {
        if (permissionCache != null) {
            permissionCache.shutdown();
        }
        SecurityContext.clear();
    }
}
