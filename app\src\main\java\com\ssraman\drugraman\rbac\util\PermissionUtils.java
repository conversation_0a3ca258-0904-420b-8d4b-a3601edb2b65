package com.ssraman.drugraman.rbac.util;

import android.content.Context;
import android.view.View;

import com.kongzue.dialog.interfaces.OnDialogButtonClickListener;
import com.kongzue.dialog.util.BaseDialog;
import com.kongzue.dialog.v3.MessageDialog;
import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.manager.RbacManager;

/**
 * 权限验证工具类
 * 提供便捷的权限检查和UI控制方法
 */
public class PermissionUtils {
    
    /**
     * 检查权限并显示错误消息
     * @param context 上下文
     * @param permissionType 权限类型
     * @param errorMessage 错误消息
     * @return 是否有权限
     */
    public static boolean checkPermissionWithMessage(Context context, PermissionType permissionType, String errorMessage) {
        if (RbacManager.getInstance().hasPermission(permissionType)) {
            return true;
        } else {
            showPermissionDeniedDialog(context, errorMessage);
            return false;
        }
    }
    
    /**
     * 检查权限并显示默认错误消息
     * @param context 上下文
     * @param permissionType 权限类型
     * @return 是否有权限
     */
    public static boolean checkPermissionWithMessage(Context context, PermissionType permissionType) {
        return checkPermissionWithMessage(context, permissionType, "您没有执行此操作的权限");
    }
    
    /**
     * 检查角色级别并显示错误消息
     * @param context 上下文
     * @param requiredLevel 要求的最低级别
     * @param errorMessage 错误消息
     * @return 是否满足级别要求
     */
    public static boolean checkRoleLevelWithMessage(Context context, int requiredLevel, String errorMessage) {
        if (RbacManager.getInstance().hasRoleLevel(requiredLevel)) {
            return true;
        } else {
            showPermissionDeniedDialog(context, errorMessage);
            return false;
        }
    }
    
    /**
     * 检查角色级别并显示默认错误消息
     * @param context 上下文
     * @param roleType 角色类型
     * @return 是否满足级别要求
     */
    public static boolean checkRoleLevelWithMessage(Context context, RoleType roleType) {
        return checkRoleLevelWithMessage(context, roleType.getLevel(), 
                "您的权限级别不足，需要" + roleType.getName() + "及以上权限");
    }
    
    /**
     * 根据权限控制视图可见性
     * @param view 视图
     * @param permissionType 权限类型
     */
    public static void setViewVisibilityByPermission(View view, PermissionType permissionType) {
        if (RbacManager.getInstance().hasPermission(permissionType)) {
            view.setVisibility(View.VISIBLE);
        } else {
            view.setVisibility(View.GONE);
        }
    }
    
    /**
     * 根据角色级别控制视图可见性
     * @param view 视图
     * @param requiredLevel 要求的最低级别
     */
    public static void setViewVisibilityByRoleLevel(View view, int requiredLevel) {
        if (RbacManager.getInstance().hasRoleLevel(requiredLevel)) {
            view.setVisibility(View.VISIBLE);
        } else {
            view.setVisibility(View.GONE);
        }
    }
    
    /**
     * 根据权限控制视图启用状态
     * @param view 视图
     * @param permissionType 权限类型
     */
    public static void setViewEnabledByPermission(View view, PermissionType permissionType) {
        view.setEnabled(RbacManager.getInstance().hasPermission(permissionType));
    }
    
    /**
     * 根据角色级别控制视图启用状态
     * @param view 视图
     * @param requiredLevel 要求的最低级别
     */
    public static void setViewEnabledByRoleLevel(View view, int requiredLevel) {
        view.setEnabled(RbacManager.getInstance().hasRoleLevel(requiredLevel));
    }
    
    /**
     * 执行需要权限的操作
     * @param context 上下文
     * @param permissionType 权限类型
     * @param action 操作
     * @param errorMessage 错误消息
     */
    public static void executeWithPermission(Context context, PermissionType permissionType, 
                                           Runnable action, String errorMessage) {
        if (RbacManager.getInstance().hasPermission(permissionType)) {
            action.run();
        } else {
            showPermissionDeniedDialog(context, errorMessage);
        }
    }
    
    /**
     * 执行需要权限的操作（使用默认错误消息）
     * @param context 上下文
     * @param permissionType 权限类型
     * @param action 操作
     */
    public static void executeWithPermission(Context context, PermissionType permissionType, Runnable action) {
        executeWithPermission(context, permissionType, action, "您没有执行此操作的权限");
    }
    
    /**
     * 执行需要角色级别的操作
     * @param context 上下文
     * @param requiredLevel 要求的最低级别
     * @param action 操作
     * @param errorMessage 错误消息
     */
    public static void executeWithRoleLevel(Context context, int requiredLevel, 
                                          Runnable action, String errorMessage) {
        if (RbacManager.getInstance().hasRoleLevel(requiredLevel)) {
            action.run();
        } else {
            showPermissionDeniedDialog(context, errorMessage);
        }
    }
    
    /**
     * 执行需要角色级别的操作（使用默认错误消息）
     * @param context 上下文
     * @param roleType 角色类型
     * @param action 操作
     */
    public static void executeWithRoleLevel(Context context, RoleType roleType, Runnable action) {
        executeWithRoleLevel(context, roleType.getLevel(), action, 
                "您的权限级别不足，需要" + roleType.getName() + "及以上权限");
    }
    
    /**
     * 显示权限拒绝对话框
     * @param context 上下文
     * @param message 消息
     */
    private static void showPermissionDeniedDialog(Context context, String message) {
        MessageDialog.build(context)
                .setTitle("权限不足")
                .setMessage(message)
                .setOkButton("确定", new OnDialogButtonClickListener() {
                    @Override
                    public boolean onClick(BaseDialog baseDialog, View v) {
                        return false;
                    }
                })
                .show();
    }
    
    /**
     * 替换旧的权限检查逻辑
     * 这个方法用于兼容现有代码，逐步迁移到新的权限系统
     * @param oldPriority 旧的权限级别
     * @param requiredOldLevel 要求的旧权限级别
     * @return 是否有权限
     */
    @Deprecated
    public static boolean checkOldPermissionCompatibility(int oldPriority, int requiredOldLevel) {
        // 将旧的权限级别映射到新的角色级别
        int newRoleLevel = mapOldPriorityToNewRoleLevel(oldPriority);
        int requiredNewLevel = mapOldPriorityToNewRoleLevel(requiredOldLevel);
        
        return RbacManager.getInstance().hasRoleLevel(requiredNewLevel);
    }
    
    /**
     * 映射旧的权限级别到新的角色级别
     * @param oldPriority 旧的权限级别
     * @return 新的角色级别
     */
    private static int mapOldPriorityToNewRoleLevel(int oldPriority) {
        switch (oldPriority) {
            case 1: return RoleType.OPERATOR.getLevel();
            case 2: return RoleType.REVIEWER.getLevel();
            case 3: return RoleType.SYSTEM_ADMIN.getLevel();
            default: return RoleType.OPERATOR.getLevel();
        }
    }
}
