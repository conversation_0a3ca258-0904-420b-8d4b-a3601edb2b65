package com.ssraman.drugraman.exceltableview;

import android.content.Context;
import android.util.SparseIntArray;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ssraman.control.excelpanel.BaseExcelPanelAdapter;
import com.ssraman.control.textview.AutofitTextView;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.db.entity.ArchiveRecordsInfo;
import com.ssraman.drugraman.exceltableview.model.Cell;
import com.ssraman.drugraman.exceltableview.model.ColTitle;
import com.ssraman.drugraman.exceltableview.model.RowTitle;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Administrator
 * @date: 2021/11/24
 */
public class ExcelCustomAdapter extends BaseExcelPanelAdapter<ColTitle, RowTitle, Cell> {

    private Context context;
    private View.OnClickListener blockListener;
    @NonNull
    private final SparseIntArray mCachedWidthList = new SparseIntArray();
    private final int normal_width;

    private BatchTableViewModel myTableViewModel;
    private List<ArchiveRecordsInfo> mData;


    public ExcelCustomAdapter(Context context, View.OnClickListener blockListener) {
        super(context);
        this.context = context;
        this.blockListener = blockListener;
        this.normal_width=dp2px(context,50.0f);

        this.myTableViewModel = new BatchTableViewModel();
        mData = new ArrayList<>();
        for(int i=0;i<this.myTableViewModel.getColumHeaderModeList().size();i++)
        {
            int t_col_width=dp2px(context,this.myTableViewModel.getColumHeaderModeList().get(i).getColWidth());
            setCacheWidth(i,t_col_width);
        }

    }

    private void setCacheWidth(int position, int width) {
        mCachedWidthList.put(position, width);
    }

    private int getCacheWidth(int position) {
        return mCachedWidthList.get(position, -1);
    }

    private int dp2px(Context context, float dpValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, context.getResources().getDisplayMetrics());
    }

    //=========================================content's cell===========================================
    @Override
    public RecyclerView.ViewHolder onCreateCellViewHolder(ViewGroup parent, int viewType) {
        View layout = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_batch_table_normal_cell, parent, false);
        CellHolder cellHolder = new CellHolder(layout);
        return cellHolder;
    }

    @Override
    public void onBindCellViewHolder(RecyclerView.ViewHolder holder, int verticalPosition, int horizontalPosition) {
        Cell cell = getMajorItem(verticalPosition, horizontalPosition);
        if (null == holder || !(holder instanceof CellHolder) || cell == null) {
            return;
        }
        CellHolder viewHolder = (CellHolder) holder;
        viewHolder.cellContainer.setTag(cell);
        viewHolder.cellContainer.setOnClickListener(blockListener);
        if (cell.getStatus() == 0) {
            viewHolder.cellContent.setText("");
            //viewHolder.cellContainer.setBackgroundColor(ContextCompat.getColor(context, R.color.white));
        } else {
            viewHolder.cellContent.setText(cell.getCellContent());
        }

        int cacheWidth = getCacheWidth(horizontalPosition);
        if (cacheWidth == -1) {
            setCacheWidth(horizontalPosition,normal_width);
            cacheWidth=normal_width;
        }
        viewHolder.cellContainer.getLayoutParams().width = cacheWidth;
        viewHolder.cellContainer.requestLayout();
    }

    static class CellHolder extends RecyclerView.ViewHolder {

        public final AutofitTextView cellContent;
        public final LinearLayout cellContainer;

        public CellHolder(View itemView) {
            super(itemView);
            cellContent = (AutofitTextView) itemView.findViewById(R.id.cell_content);
            cellContainer = (LinearLayout) itemView.findViewById(R.id.pms_cell_container);
        }
    }


    //=========================================top cell===========================================
    @Override
    public RecyclerView.ViewHolder onCreateTopViewHolder(ViewGroup parent, int viewType) {
        View layout = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_batch_table_top_header, parent, false);
        TopHolder topHolder = new TopHolder(layout);
        return topHolder;
    }

    @Override
    public void onBindTopViewHolder(RecyclerView.ViewHolder holder, int position) {
        ColTitle colTitle = getTopItem(position);
        if (null == holder || !(holder instanceof TopHolder) || colTitle == null) {
            return;
        }
        TopHolder viewHolder = (TopHolder) holder;
        viewHolder.columnLabel.setText(colTitle.getColTitleName());

        int cacheWidth = getCacheWidth(position);
        if (cacheWidth == -1) {
            setCacheWidth(position,normal_width);
            cacheWidth=normal_width;
        }
        viewHolder.cellContainer.getLayoutParams().width = cacheWidth;
        viewHolder.cellContainer.requestLayout();
    }

    static class TopHolder extends RecyclerView.ViewHolder {

        public final TextView columnLabel;
        public final LinearLayout cellContainer;

        public TopHolder(View itemView) {
            super(itemView);
            columnLabel = (TextView) itemView.findViewById(R.id.column_label);
            cellContainer = (LinearLayout) itemView.findViewById(R.id.pms_cell_container);
        }
    }

    //=========================================left cell===========================================
    @Override
    public RecyclerView.ViewHolder onCreateLeftViewHolder(ViewGroup parent, int viewType) {
        View layout = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_batch_table_left_header, parent, false);
        LeftHolder leftHolder = new LeftHolder(layout);
        return leftHolder;
    }

    @Override
    public void onBindLeftViewHolder(RecyclerView.ViewHolder holder, int position) {
        RowTitle rowTitle = getLeftItem(position);
        if (null == holder || !(holder instanceof LeftHolder) || rowTitle == null) {
            return;
        }
        LeftHolder viewHolder = (LeftHolder) holder;
        viewHolder.rowIndexLabel.setText(rowTitle.getRowTitleIndex());
        ViewGroup.LayoutParams lp = viewHolder.root.getLayoutParams();
        viewHolder.root.setLayoutParams(lp);
    }

    static class LeftHolder extends RecyclerView.ViewHolder {

        public final TextView rowIndexLabel;
        public final View root;

        public LeftHolder(View itemView) {
            super(itemView);
            root = itemView.findViewById(R.id.root);
            rowIndexLabel = (TextView) itemView.findViewById(R.id.row_index_label);
        }
    }

    //=========================================left-top cell===========================================
    @Override
    public View onCreateTopLeftView() {
        return LayoutInflater.from(context).inflate(R.layout.item_batch_table_normal_cell, null);
    }

    //=========================================data===========================================

    public void initialization() {
        setRecordList(0);
    }

    /**
     * This method is not a generic Adapter method. It helps to generate lists from single user
     * list for this adapter.
     */
    private void setRecordList(int start) {
        // Generate the lists that are used to TableViewAdapter
        myTableViewModel.generateListForTableView(start,mData);//建表和表头

        // Now we got what we need to show on TableView.
        setAllData(myTableViewModel.getRowHeaderModelList(), myTableViewModel
                .getColumHeaderModeList(), myTableViewModel.getCellModelList());
    }

    public void setData(int start,List<ArchiveRecordsInfo> data) {
        if (mData == null) {
            mData = new ArrayList<>();
        }
        mData.clear();
        mData.addAll(data);
        setRecordList(start);
    }

//    public void addData(List<ArchiveRecordsInfo> data) {
//        int rowPositionStart = mData.size();
//        List<RowTitle> rowHeaderItem = myTableViewModel.createRowHeaderList(rowPositionStart, data.size());
//        List<List<Cell>> cellItems = myTableViewModel.createCellModelList(data);
//        mData.addAll(data);
//        addRowRange(rowPositionStart, rowHeaderItem, cellItems);
//    }

    public void clearData() {
        if (mData == null) {
            mData = new ArrayList<>();
        }
        mData.clear();
        setRecordList(0);
    }

    public List<ArchiveRecordsInfo> getData()
    {
        return mData;
    }


}
