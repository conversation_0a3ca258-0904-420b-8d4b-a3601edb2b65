package com.ssraman.drugraman.typeface;

/**
 * @author: Administrator
 * @date: 2021/6/21
 */
public enum NodeItemType {
    HighLevelDirectory(1),
    LowLevelDirectory(2),
    ItemLevelDirectory(3);

    private int value = 0;

    private NodeItemType(int value)
    {
        this.value = value;
    }

    public static NodeItemType valueOf(int value) {    //    手写的从int到enum的转换函数
        switch (value) {
            case 1:
                return HighLevelDirectory;
            case 2:
                return LowLevelDirectory;
            case 3:
                return ItemLevelDirectory;
            default:
                return HighLevelDirectory;
        }
    }

    public int value() {
        return this.value;
    }

}
