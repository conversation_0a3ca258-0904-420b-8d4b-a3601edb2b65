package com.ssraman.drugraman.rbac.dto;

import com.ssraman.drugraman.db.entity.RbacUser;
import com.ssraman.drugraman.db.entity.RbacRole;
import com.ssraman.drugraman.db.entity.RbacPermission;

import java.util.List;

/**
 * 认证结果DTO
 * 包含用户认证的结果信息
 */
public class AuthResult {
    
    private boolean success;
    private String message;
    private RbacUser user;
    private String sessionToken;
    private List<RbacRole> roles;
    private List<RbacPermission> permissions;
    private long sessionExpiresAt;
    
    public AuthResult() {
    }
    
    public AuthResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    /**
     * 创建成功的认证结果
     */
    public static AuthResult success(RbacUser user, String sessionToken, 
                                   List<RbacRole> roles, List<RbacPermission> permissions,
                                   long sessionExpiresAt) {
        AuthResult result = new AuthResult(true, "认证成功");
        result.user = user;
        result.sessionToken = sessionToken;
        result.roles = roles;
        result.permissions = permissions;
        result.sessionExpiresAt = sessionExpiresAt;
        return result;
    }
    
    /**
     * 创建失败的认证结果
     */
    public static AuthResult failure(String message) {
        return new AuthResult(false, message);
    }
    
    /**
     * 用户名或密码错误
     */
    public static AuthResult invalidCredentials() {
        return new AuthResult(false, "用户名或密码错误");
    }
    
    /**
     * 用户被锁定
     */
    public static AuthResult userLocked() {
        return new AuthResult(false, "用户账号已被锁定");
    }
    
    /**
     * 用户未激活
     */
    public static AuthResult userInactive() {
        return new AuthResult(false, "用户账号未激活");
    }
    
    /**
     * 用户已删除
     */
    public static AuthResult userDeleted() {
        return new AuthResult(false, "用户账号已被删除");
    }
    
    /**
     * 登录尝试次数过多
     */
    public static AuthResult tooManyAttempts() {
        return new AuthResult(false, "登录尝试次数过多，账号已被临时锁定");
    }
    
    // Getters and Setters
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public RbacUser getUser() {
        return user;
    }
    
    public void setUser(RbacUser user) {
        this.user = user;
    }
    
    public String getSessionToken() {
        return sessionToken;
    }
    
    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }
    
    public List<RbacRole> getRoles() {
        return roles;
    }
    
    public void setRoles(List<RbacRole> roles) {
        this.roles = roles;
    }
    
    public List<RbacPermission> getPermissions() {
        return permissions;
    }
    
    public void setPermissions(List<RbacPermission> permissions) {
        this.permissions = permissions;
    }
    
    public long getSessionExpiresAt() {
        return sessionExpiresAt;
    }
    
    public void setSessionExpiresAt(long sessionExpiresAt) {
        this.sessionExpiresAt = sessionExpiresAt;
    }
    
    @Override
    public String toString() {
        return "AuthResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", user=" + (user != null ? user.getUsername() : null) +
                ", sessionToken='" + (sessionToken != null ? "***" : null) + '\'' +
                ", rolesCount=" + (roles != null ? roles.size() : 0) +
                ", permissionsCount=" + (permissions != null ? permissions.size() : 0) +
                ", sessionExpiresAt=" + sessionExpiresAt +
                '}';
    }
}
