package com.ssraman.drugraman.sortableview;

import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ssraman.drugraman.newentiry.MatchResultNodeInfo;

import java.util.List;

import de.codecrafters.tableview.TableView;
import de.codecrafters.tableview.toolkit.LongPressAwareTableDataAdapter;

/**
 * @author: Administrator
 * @date: 2021/6/29
 */
public class CorrelationSorTableDataAdapter extends LongPressAwareTableDataAdapter<MatchResultNodeInfo> {
    private static final int TEXT_SIZE = 16;

    public CorrelationSorTableDataAdapter(Context context, List data, TableView tableView) {
        super(context, data, tableView);
    }

    @Override
    public View getDefaultCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final MatchResultNodeInfo matchResultInfo = getRowData(rowIndex);
        View renderedView = null;
        switch (columnIndex) {
            case 0:
                renderedView = renderString(String.valueOf(rowIndex+1));
                break;
            case 1:
                double correlation = matchResultInfo.getExRatio();
                renderedView = renderString(String.format("%.2f",correlation));
                break;
            case 2:
                String sampleName = matchResultInfo.getSampleName();
                renderedView = renderString(sampleName);
                break;
        }
        return renderedView;
    }

    @Override
    public View getLongPressCellView(int rowIndex, int columnIndex, ViewGroup parentView) {
        final MatchResultNodeInfo peakInfo = getRowData(rowIndex);
        View renderedView = null;

        renderedView = getDefaultCellView(rowIndex, columnIndex, parentView);

        return renderedView;
    }

    private View renderString(final String value) {
        final TextView textView = new TextView(getContext());
        if(value==null||value.equals("null"))
        {
            textView.setText("");
        }
        else
        {
            textView.setText(value);
        }
        textView.setPadding(20, 10, 20, 10);
        textView.setTextSize(TEXT_SIZE);
        textView.setTextColor(Color.WHITE);
        return textView;
    }

}
