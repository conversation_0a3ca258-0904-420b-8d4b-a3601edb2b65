package com.ssraman.drugraman.sortableview;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;

import androidx.core.content.ContextCompat;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.db.entity.User_info;

import de.codecrafters.tableview.SortableTableView;
import de.codecrafters.tableview.model.TableColumnWeightModel;
import de.codecrafters.tableview.toolkit.SimpleTableHeaderAdapter;
import de.codecrafters.tableview.toolkit.SortStateViewProviders;
import de.codecrafters.tableview.toolkit.TableDataRowBackgroundProviders;

/**
 * @author: Administrator
 * @date: 2021/7/9
 */
public class UserManagementSorTableView extends SortableTableView<User_info> {
    public UserManagementSorTableView(Context context) {
        this(context, null);
    }

    public UserManagementSorTableView(Context context, AttributeSet attributes) {
        this(context, attributes, android.R.attr.listViewStyle);
    }

    public UserManagementSorTableView(Context context, AttributeSet attributes, int styleAttributes) {
        super(context, attributes, styleAttributes);

        final SimpleTableHeaderAdapter simpleTableHeaderAdapter = new SimpleTableHeaderAdapter(context, R.string.user_t_index, R.string.user_t_name, R.string.user_t_priority,R.string.user_t_describe);
        simpleTableHeaderAdapter.setTextColor(ContextCompat.getColor(context, R.color.white));
        simpleTableHeaderAdapter.setPaddings(8,5,2,5);
        simpleTableHeaderAdapter.setGravity(Gravity.CENTER);
        setHeaderAdapter(simpleTableHeaderAdapter);

        //每一个item颜色差异化的操作
        final int rowColorEven = ContextCompat.getColor(context, R.color.table_data_row_even);
        final int rowColorOdd = ContextCompat.getColor(context, R.color.table_data_row_odd);
        final int rowColorSelected = ContextCompat.getColor(context, R.color.table_data_row_selected);
        setDataRowBackgroundProvider(TableDataRowBackgroundProviders.alternatingRowColors(rowColorEven, rowColorOdd,rowColorSelected));
        final int cowHeaderColor = ContextCompat.getColor(context, R.color.table_data_cow_header);
        setHeaderBackgroundColor(cowHeaderColor);
        setHeaderSortStateViewProvider(SortStateViewProviders.brightArrows());

        final TableColumnWeightModel tableColumnWeightModel = new TableColumnWeightModel(4);
        tableColumnWeightModel.setColumnWeight(0, 1);
        tableColumnWeightModel.setColumnWeight(1, 2);
        tableColumnWeightModel.setColumnWeight(3, 2);
        tableColumnWeightModel.setColumnWeight(4, 2);
        setColumnModel(tableColumnWeightModel);
    }
}
