
#include "basicfuc.h"
//#include "stdb.h"
#include <string>
#include <jni.h>

#include "include/rapidjson/rapidjson.h "
#include "include/rapidjson/stringbuffer.h"
#include "include/rapidjson/writer.h"
#include "include/rapidjson/reader.h"
#include "include/rapidjson/document.h"
#include "include/rapidjson/encodedstream.h"    // EncodedInputStream
#include "drpublic.h"

/**
 * 修改记录：修改keyword Json数据解析缓存
 * 修改page Json读取数据解析缓存
 *
 * 20180914
 * 2018-0914-18-41
 */






namespace ssrlib {
    char m_title[50];
//    char* buff_Json = NULL;
//    char* buff_Json = NULL;
//    char* buff_Json = NULL;
//    char* buff_Json = NULL;
    char *buff_Json = NULL;
    int *matching_peak_idxlst = NULL;
    std::string curFunction = "Hello from C++";
}

using namespace rapidjson;
using namespace ssrlib;

extern "C" {

JNIEXPORT jstring JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_stringFromJNI(
        JNIEnv *env,
        jobject /* this */) {
//    return ssrlib::curFunction;
    /* for(int i = 0;i < 100000; i++){
          curFunction = curFunction + '0';
      }*/
    return env->NewStringUTF(curFunction.c_str());
}
//
// Created by ssbj on 2018/8/1.
//

//寻峰
//例12
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_seePeaks
* Signature: ([D[DIDI[B)I
*/
/*JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1seePeaks
        (JNIEnv *env, jobject, jdoubleArray wcnt, jdoubleArray intensity, jdouble SNR,
         jint inttime_ms) {
    curFunction = "SSR_seePeaks";
    SPEC spec;
    spec.len = env->GetArrayLength(wcnt);
    spec.dWcnt = env->GetDoubleArrayElements(wcnt, 0);
    spec.dIntensity = env->GetDoubleArrayElements(intensity, 0);
    PEAKS2 result;
    double h[14];
    double w[14];
    result.peaks_hight = h;
    result.peaks_wcnt = w;

    int ret = seePeaks(&spec, SNR, inttime_ms, &result);
    if (ret == 0) {        //curFunction = "I'm sleep!";
        return 0;
    }

    ////////////
    Document d;    //生成一个dom元素Document
    Document::AllocatorType &allocator = d.GetAllocator(); //获取分配器
    d.SetObject();    //将当前的Document设置为一个object，也就是说，整个Document是一个Object类型的dom元素


    Value wcntArray(rapidjson::kArrayType);
    Value intensityArray(rapidjson::kArrayType);

    for (int i = 0; i < result.num; i++) {
        Value wcnt;
        wcnt.SetDouble(result.peaks_wcnt[i]);
        Value intens;
        intens.SetDouble(result.peaks_hight[i]);
        wcntArray.PushBack(wcnt, allocator);
        intensityArray.PushBack(intens, allocator);
    }
    d.AddMember("W", wcntArray, allocator);
    d.AddMember("I", intensityArray, allocator);
    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);

    d.Accept(writer);

    const char *output = buffer.GetString();
    if (buff_Json != NULL) {
        free(buff_Json);
        buff_Json = NULL;
    }

    buff_Json = (char *) malloc(buffer.GetSize());
    memcpy(buff_Json, output, buffer.GetSize());
    //buff_Json[buffer.GetSize()] = '\0';
    /*//*peaks_json = buff_Json;
    env->ReleaseDoubleArrayElements(wcnt, spec.dWcnt, 0);
    env->ReleaseDoubleArrayElements(intensity, spec.dIntensity, 0);
    curFunction = "I'm sleep!";
    ///////////
    return buffer.GetSize();


}*/
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_getpeaks_json
* Signature: ([B)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1getpeaks_1json
        (JNIEnv *env, jobject, jbyteArray jpeaks_json) {

    curFunction = "SSR_getpeaks_json";
    char *p = (char *) env->GetByteArrayElements(jpeaks_json, 0);
    jsize len = env->GetArrayLength(jpeaks_json);
    memcpy(p, buff_Json, len);
    env->ReleaseByteArrayElements(jpeaks_json, (jbyte *) buff_Json, 0);
    //curFunction="I'm sleep!";
    return 0;
}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_seePeaks_local
* Signature: ([D[DII[B)I
*/
/*JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1seePeaks_1local
        (JNIEnv *env, jobject, jdoubleArray wcnt, jdoubleArray intensity, jint boxwidth) {
    curFunction = " SSR_seePeaks_local";
    SPEC spec;
    spec.len = env->GetArrayLength(wcnt);
    spec.dWcnt = env->GetDoubleArrayElements(wcnt, 0);
    spec.dIntensity = env->GetDoubleArrayElements(intensity, 0);
    PEAKS2 result;
    double h[14];
    double w[14];
    result.peaks_hight = h;
    result.peaks_wcnt = w;
    int ret = seePeaks_local(&spec, boxwidth, &result);
    if (ret == 0) {
        // curFunction="I'm sleep!";
        return 0;
    }
    ////////////
    Document d;    //生成一个dom元素Document
    Document::AllocatorType &allocator = d.GetAllocator(); //获取分配器
    d.SetObject();    //将当前的Document设置为一个object，也就是说，整个Document是一个Object类型的dom元素


    Value wcntArray(rapidjson::kArrayType);
    Value intensityArray(rapidjson::kArrayType);

    for (int i = 0; i < result.num; i++) {
        Value wcnt;
        wcnt.SetDouble(result.peaks_wcnt[i]);
        Value intens;
        intens.SetDouble(result.peaks_hight[i]);
        wcntArray.PushBack(wcnt, allocator);
        intensityArray.PushBack(intens, allocator);
    }
    d.AddMember("W", wcntArray, allocator);
    d.AddMember("I", intensityArray, allocator);
    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);

    d.Accept(writer);

    const char *output = buffer.GetString();
    if (buff_Json != NULL) {
        free(buff_Json);
    }

    buff_Json = (char *) malloc(buffer.GetSize());
    memcpy(buff_Json, output, buffer.GetSize());
    //buff_Json[buffer.GetSize()] = '\0';
    /*//*peaks_json = buff_Json;

    env->ReleaseDoubleArrayElements(wcnt, spec.dWcnt, 0);
    env->ReleaseDoubleArrayElements(intensity, spec.dIntensity, 0);
    ///////////
    //curFunction="I'm sleep!";
    return buffer.GetSize();
}*/

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_calibration
* Signature: ([DI[D[D)V
*/
JNIEXPORT void JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1calibration
        (JNIEnv *env, jobject, jdoubleArray jstdpeaks, jdoubleArray jcoff, jdoubleArray jpixels) {
    curFunction = "SSR_calibration ";
    double *stdpeaks = env->GetDoubleArrayElements(jstdpeaks, 0);
    jsize size = env->GetArrayLength(jstdpeaks);
    double *coff = env->GetDoubleArrayElements(jcoff, 0);
    double *pixels = env->GetDoubleArrayElements(jpixels, 0);        //特征峰像素位置表,长度也是size，如果没有补0
    calibration(stdpeaks, size, coff, pixels);
    env->ReleaseDoubleArrayElements(jcoff, coff, 0);
    env->ReleaseDoubleArrayElements(jpixels, pixels, 0);

}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_makeshortSpec
* Signature: ([D[DI[DI[B)I
*/
/*JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1makeshortSpec
        (JNIEnv *env, jobject, jdoubleArray jwcnt, jdoubleArray jintensity, jdoubleArray jpeaks) {
    curFunction = "SSR_makeshortSpec ";
    SPEC spec;
    spec.len = env->GetArrayLength(jwcnt);
    spec.dWcnt = env->GetDoubleArrayElements(jwcnt, 0);
    spec.dIntensity = env->GetDoubleArrayElements(jintensity, 0);

    double *peaks = env->GetDoubleArrayElements(jpeaks, 0);
    jsize peaks_num = env->GetArrayLength(jpeaks);
    SPEC specnew;
    specnew.len = (spec.len / 3);
    specnew.dIntensity = (double *) malloc(sizeof(double) * specnew.len);
    specnew.dWcnt = (double *) malloc(sizeof(double) * specnew.len);
    makeshortSpec(&spec, peaks, peaks_num, &specnew);
//////////////////////////////////////////////////////////////////////////
    Document d;    //生成一个dom元素Document
    Document::AllocatorType &allocator = d.GetAllocator(); //获取分配器
    d.SetObject();    //将当前的Document设置为一个object，也就是说，整个Document是一个Object类型的dom元素

    Value wcntArray(rapidjson::kArrayType);
    Value intensityArray(rapidjson::kArrayType);

    for (int i = 0; i < specnew.len; i++) {
        Value wcnt;
        wcnt.SetInt((int) specnew.dWcnt[i]);
        Value intens;
        intens.SetInt((int) specnew.dIntensity[i]);
        wcntArray.PushBack(wcnt, allocator);
        intensityArray.PushBack(intens, allocator);
    }
    d.AddMember("W", wcntArray, allocator);
    d.AddMember("I", intensityArray, allocator);
    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);

    d.Accept(writer);
    const char *output = buffer.GetString();
    if (buff_Json != NULL) {
        free(buff_Json);
        buff_Json = NULL;
    }

    buff_Json = (char *) malloc(buffer.GetSize());
    memcpy(buff_Json, output, buffer.GetSize());
    //buff_Json[buffer.GetSize()] = '\0';
/*//*spec_s_json = buff_Json;
    env->ReleaseDoubleArrayElements(jwcnt, spec.dWcnt, 0);
    env->ReleaseDoubleArrayElements(jintensity, spec.dIntensity, 0);
    env->ReleaseDoubleArrayElements(jpeaks, peaks, 0);
    free(specnew.dIntensity);
    free(specnew.dWcnt);
    //curFunction="I'm sleep!";
    return buffer.GetSize();
}*/

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_spec_json
* Signature: ([B)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1spec_1json
        (JNIEnv *env, jobject, jbyteArray jspec_json) {
    curFunction = "SSR_spec_json ";
    char *spec_json = (char *) env->GetByteArrayElements(jspec_json, 0);
    int len = env->GetArrayLength(jspec_json);
    memcpy(spec_json, buff_Json, len);
    env->ReleaseByteArrayElements(jspec_json, (jbyte *) spec_json, 0);
    //curFunction="I'm sleep!";
    return 0;
}

/*
	* Class:     com_ssraman_ssbj_support_ssrfunlib
	* Method:    SSR_getPos
	* Signature: ([DID)I
	*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1getPos
        (JNIEnv *env, jobject, jdoubleArray jwCnt, jdouble jdWcnt) {
    curFunction = "SSR_getPos ";
    jsize len = env->GetArrayLength(jwCnt);
    double *wCnt = env->GetDoubleArrayElements(jwCnt, 0);

    env->ReleaseDoubleArrayElements(jwCnt, wCnt, 0);
    //curFunction="I'm sleep!";
    return getPos(wCnt, len, jdWcnt);
}

//工具函数
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_getMaxPos
* Signature: ([DI[DDD)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1getMaxPos
        (JNIEnv *env, jobject, jdoubleArray jwcnt, jdoubleArray jintensity, jdouble jwcnt_seed,
         jdouble jwidth) {
    curFunction = "SSR_getMaxPos ";
    SPEC spec;
    spec.dIntensity = env->GetDoubleArrayElements(jintensity, 0);
    spec.dWcnt = env->GetDoubleArrayElements(jwcnt, 0);
    spec.len = env->GetArrayLength(jwcnt);
    int ret = getMaxPos(&spec, jwcnt_seed, jwidth);
    env->ReleaseDoubleArrayElements(jwcnt, spec.dWcnt, 0);
    env->ReleaseDoubleArrayElements(jintensity, spec.dIntensity, 0);
    //curFunction="I'm sleep!";
    return ret;

}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_SpecSNR
* Signature: ([DIIIIII)D
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1SpecSNR
        (JNIEnv *env, jobject, jdoubleArray jpWcnt, jdoubleArray jpSpc, jdouble jlowlineCnt,
         jdouble jppeakCenter) {
//    curFunction = "SSR_SpecSNR ";
//    double *specdata = env->GetDoubleArrayElements(jspecdata, 0);
//    int len = env->GetArrayLength(jspecdata);
//    jdouble ret = SpecSNR(specdata, len, jlowlineCnt, jboxZeroPos, jboxWidth, jpeakCenter, jFWHM);
//    env->ReleaseDoubleArrayElements(jspecdata, specdata, 0);
    //curFunction="I'm sleep!";

    jint re;
    jdouble redouble;
    jdouble *pWaveCnt = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
    jint len = env->GetArrayLength(jpWcnt);
    SPEC spec;
    spec.dWcnt = pWaveCnt;
    spec.dIntensity = pSpc;
    spec.len = len;
    jint peakidx = getMaxPos(&spec, jppeakCenter, 10);

    redouble = SpecSNR(pSpc,
                       len,        //数据长度
                       getPos(pWaveCnt, len, jlowlineCnt), //低波数位置，像素单位
                       peakidx - 30, //特征峰的参考位置，单位像素, //特征峰框（在rawSpec中的首地址）
                       60,   //在特征峰框中的数据长度
                       30, //特征峰的参考位置，单位像素
                       10       //特征峰的搜索范围
    );
    re = int(redouble);
    env->ReleaseDoubleArrayElements(jpSpc, pSpc, 0);
    env->ReleaseDoubleArrayElements(jpWcnt, pWaveCnt, 0);
    return re;
}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_getNewSpec
* Signature: ([D[DIII[BI)I
*/
/*JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1getNewSpec
        (JNIEnv *env, jobject, jdoubleArray jwcnt, jdoubleArray jintensity, jint jlowline,
         jint jtopline, jint jmode) {
    curFunction = " SSR_getNewSpec";
    SPEC spec;
    spec.dIntensity = env->GetDoubleArrayElements(jintensity, 0);
    spec.dWcnt = env->GetDoubleArrayElements(jwcnt, 0);
    spec.len = env->GetArrayLength(jwcnt);

    SPEC specnew;
    specnew.len = (jtopline - jlowline);
    specnew.dIntensity = (double *) malloc(sizeof(double) * specnew.len);
    specnew.dWcnt = (double *) malloc(sizeof(double) * specnew.len);
    getNewSpec(&spec, jlowline, jtopline, &specnew, jmode);
//////////////////////////
    Document d;    //生成一个dom元素Document
    Document::AllocatorType &allocator = d.GetAllocator(); //获取分配器
    d.SetObject();    //将当前的Document设置为一个object，也就是说，整个Document是一个Object类型的dom元素

    Value wcntArray(rapidjson::kArrayType);
    Value intensityArray(rapidjson::kArrayType);
    int imax = 0;
    for (int i = 0; i < specnew.len; i++) {
        Value wcnt;
        wcnt.SetInt((int) specnew.dWcnt[i]);
        Value intens;
        intens.SetInt((int) specnew.dIntensity[i]);
        wcntArray.PushBack(wcnt, allocator);
        intensityArray.PushBack(intens, allocator);
        imax = (imax > (int) specnew.dIntensity[i]) ? imax : (int) specnew.dIntensity[i];
    }
    Value maxv;
    maxv.SetInt(imax);
    d.AddMember("max", maxv, allocator);
    d.AddMember("I", intensityArray, allocator);
    d.AddMember("W", wcntArray, allocator);


    StringBuffer buffer;
    Writer<StringBuffer> writer(buffer);

    d.Accept(writer);
    const char *output = buffer.GetString();
    if (buff_Json != NULL) {
        free(buff_Json);
    }

    buff_Json = (char *) malloc(buffer.GetSize());
    memcpy(buff_Json, output, buffer.GetSize());
    //buff_Json[buffer.GetSize()] = '\0';
/*//*spec_r_json = buff_Json;
    env->ReleaseDoubleArrayElements(jwcnt, spec.dWcnt, 0);
    env->ReleaseDoubleArrayElements(jintensity, spec.dIntensity, 0);
    free(specnew.dIntensity);
    free(specnew.dWcnt);
    //curFunction="I'm sleep!";
    return buffer.GetSize();
}*/

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_getNewWCnt
* Signature: (II[D)V
*/
JNIEXPORT void JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1getNewWCnt
        (JNIEnv *env, jobject, jint lowline, jint topline, jdoubleArray jnewWcnt) {
    curFunction = "SSR_getNewWCnt ";
    double *newWcnt = env->GetDoubleArrayElements(jnewWcnt, 0);
    getNewWCnt(lowline, topline, newWcnt);
    env->ReleaseDoubleArrayElements(jnewWcnt, newWcnt, 0);
}
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_autoBaseline
* Signature: ([D[DIDI)V
*/
JNIEXPORT void JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1autoBaseline
        (JNIEnv *env, jobject, jdoubleArray jwcnt, jdoubleArray jintensity, jdouble lowcnt,
         jint nR, jdouble upcnt) {
    curFunction = " SSR_autoBaseline";
    SPEC spec;
    spec.dIntensity = env->GetDoubleArrayElements(jintensity, 0);
    spec.dWcnt = env->GetDoubleArrayElements(jwcnt, 0);
    spec.len = env->GetArrayLength(jwcnt);
//    autoBaseline(&spec, lowcnt, nR, upcnt);
    autoBaseline(&spec, lowcnt, nR);
    env->ReleaseDoubleArrayElements(jwcnt, spec.dWcnt, 0);
    env->ReleaseDoubleArrayElements(jintensity, spec.dIntensity, 0);
}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_autobaseline_org
* Signature: ([D[D[D[DIDI)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1autobaseline_1org
        (JNIEnv *env, jobject, jdoubleArray jintensity, jdoubleArray jbkgSpc, jdoubleArray jwcnt,
         jdoubleArray jre, jdouble lowcnt, jint nR) {
    curFunction = " SSR_autobaseline_org";
    double *pSpc = env->GetDoubleArrayElements(jintensity, 0);
    double *pBkgSpc = env->GetDoubleArrayElements(jbkgSpc, 0);
    double *pWaveCnt = env->GetDoubleArrayElements(jwcnt, 0);
    double *pIntensity = env->GetDoubleArrayElements(jre, 0);
    int len = env->GetArrayLength(jre);

    int ret = autobaseline_org(pSpc, pBkgSpc, pWaveCnt, pIntensity, len, lowcnt, nR,1);
    env->ReleaseDoubleArrayElements(jwcnt, pWaveCnt, 0);
    env->ReleaseDoubleArrayElements(jintensity, pSpc, 0);
    env->ReleaseDoubleArrayElements(jbkgSpc, pBkgSpc, 0);
    env->ReleaseDoubleArrayElements(jre, pIntensity, 0);
    //curFunction="I'm sleep!";
    return ret;
}
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_smooth
* Signature: ([D[DIII[B)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1smooth
//        (JNIEnv *env, jobject, jdoubleArray jwcnt, jdoubleArray jintensity, jint lowline,
//         jint topline) {
//    curFunction = " SSR_smooth";
//    SPEC spec;
//    spec.dIntensity = env->GetDoubleArrayElements(jintensity, 0);
//    spec.dWcnt = env->GetDoubleArrayElements(jwcnt, 0);
//    spec.len = env->GetArrayLength(jwcnt);
//
//    SPEC specnew;
//    specnew.len = (topline - lowline);
//    specnew.dIntensity = (double *) malloc(sizeof(double) * specnew.len);
//    specnew.dWcnt = (double *) malloc(sizeof(double) * specnew.len);
//    smooth(&spec, lowline, topline, &specnew);
//
//    Document d;    //生成一个dom元素Document
//    Document::AllocatorType &allocator = d.GetAllocator(); //获取分配器
//    d.SetObject();    //将当前的Document设置为一个object，也就是说，整个Document是一个Object类型的dom元素
//
//    Value wcntArray(rapidjson::kArrayType);
//    Value intensityArray(rapidjson::kArrayType);
//
//    for (int i = 0; i < specnew.len; i++) {
//        Value wcnt;
//        wcnt.SetDouble(specnew.dWcnt[i]);
//        Value intens;
//        intens.SetDouble(specnew.dIntensity[i]);
//        wcntArray.PushBack(wcnt, allocator);
//        intensityArray.PushBack(intens, allocator);
//    }
//    d.AddMember("W", wcntArray, allocator);
//    d.AddMember("I", intensityArray, allocator);
//    StringBuffer buffer;
//    Writer<StringBuffer> writer(buffer);
//
//    d.Accept(writer);
//    const char *output = buffer.GetString();
//    if (buff_Json != NULL) {
//        free(buff_Json);
//    }
//
//    buff_Json = (char *) malloc(buffer.GetSize());
//    memcpy(buff_Json, output, buffer.GetSize());
//    //buff_Json[buffer.GetSize()] = '\0';
////*spec_r_json = buff_Json;
//    env->ReleaseDoubleArrayElements(jwcnt, spec.dWcnt, 0);
//    env->ReleaseDoubleArrayElements(jintensity, spec.dIntensity, 0);
//    free(specnew.dIntensity);
//    free(specnew.dWcnt);
//    //curFunction="I'm sleep!";
//    return buffer.GetSize();
//}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_encodeSoft
* Signature: ([D)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1encodeSoft
        (JNIEnv *env, jobject, jdoubleArray jintensity) {
    curFunction = " SSR_encodeSoft";
    double *spBuff = env->GetDoubleArrayElements(jintensity, 0);
    int len = env->GetArrayLength(jintensity);
    int ret = encodeSoft(spBuff, len);
    env->ReleaseDoubleArrayElements(jintensity, spBuff, 0);
    //curFunction="I'm sleep!";
    return ret;
}


JNIEXPORT jint JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1split_1spec(JNIEnv *env, jobject instance,
                                                         jdoubleArray x_, jdoubleArray curl1_,
                                                         jdoubleArray curl2_,
                                                         jdoubleArray score_, jdouble lowline,
                                                         jdouble topline) {
    jdouble *x = env->GetDoubleArrayElements(x_, NULL);
    jdouble *curl1 = env->GetDoubleArrayElements(curl1_, NULL);
    jint len = env->GetArrayLength(x_);
    jint size = env->GetArrayLength(curl2_) / len - 1;
    jdouble *curl2 = env->GetDoubleArrayElements(curl2_, NULL);
    jdouble *score = env->GetDoubleArrayElements(score_, NULL);

    // TODO
    jint re = split(x, curl1, len, curl2, size, score, lowline, topline);

    env->ReleaseDoubleArrayElements(x_, x, 0);
    env->ReleaseDoubleArrayElements(curl1_, curl1, 0);
    env->ReleaseDoubleArrayElements(curl2_, curl2, 0);
    env->ReleaseDoubleArrayElements(score_, score, 0);
    return re;
}
/////////////////////////数据库查询
//准备
//准备数据库模块
//例1


/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_init
* Signature: ([BID)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1init
//        (JNIEnv *env, jobject, jbyteArray jdbname, jdouble lowcnt) {
//    curFunction = "SSR_lib_init ";
//    char *dbname = (char *) env->GetByteArrayElements(jdbname, 0);
//    int len = env->GetArrayLength(jdbname);
//    int ret = init_matching_module(dbname, len, lowcnt);
//    env->ReleaseByteArrayElements(jdbname, (jbyte *) dbname, 0);
//    //curFunction="I'm sleep!";
//    return ret;
//}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_usr_select
* Signature: ([B[BI[B)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1usr_1select
//        (JNIEnv *env, jobject, jbyteArray jkeywords_json, jbyteArray jtitle) {
//    curFunction = " SSR_lib_usr_select";
//    jbyte *keywords_json =  env->GetByteArrayElements(jkeywords_json, 0);
//    char *title = (char *) env->GetByteArrayElements(jtitle, 0);
//    int len = env->GetArrayLength(jkeywords_json);
//
//
//    KEYWORDS keywords;
///////////////////////////////keywords_json拆包
//    Document document;
//    //document.SetObject();
//    //使用了2个Document
//    if (len > 0) {
//        char* tbuf=(char*)malloc(len+1);
//        memcpy(tbuf,keywords_json,len);
//        tbuf[len]='\0';
//        document.Parse(tbuf);
//
//        Value kArray;
//        kArray.SetArray();
//        if (document.HasMember("kArray")) {
//            kArray = document["kArray"].GetArray();
//            if (kArray.IsArray() && !kArray.Empty()) {
//                for (rapidjson::SizeType i = 0; i < kArray.Size(); i++) {
//                    int tlen = kArray[i]["val"].GetStringLength();
//                    char *buff = (char *) malloc(tlen + 1);
//                    memcpy(buff, kArray[i]["val"].GetString(), tlen);
//                    buff[tlen] = '\0';
//                    KEYWORD keyw;
//                    keyw.value = buff;
//                    keyw.value_len = tlen;
//                    tlen = kArray[i]["key"].GetStringLength();
//                    char *buff1 = (char *) malloc(tlen + 1);
//                    memcpy(buff1, kArray[i]["key"].GetString(), tlen);
//                    buff1[tlen] = '\0';
//                    keyw.key = buff1;
//                    keyw.key_len = tlen;
//                    keywords.push_back(keyw);
//                }
//            }
//        }
//        free(tbuf);
//    }
//
//    TITLE t;
//    t.title = title;
//    t.len = 0;
//    NAMELST titlest;
//    int ret = usr_select(&keywords, &t, &titlest);
//
///////////////////////////////titlelst打包
//    if (ret > 0) {
//        //Document d;    //生成一个dom元素Document
//        Document::AllocatorType &allocator = document.GetAllocator(); //获取分配器
//        //d.SetObject();    //将当前的Document设置为一个object，也就是说，整个Document是一个Object类型的dom元素
//        //直接写了一个Array
//        Value titleArray;
//        titleArray.SetArray();
//
//        for (NAMELST::iterator i = titlest.begin(); i != titlest.end(); i++) {
//            Value t;
//            t.SetString((*i).title, (*i).len, allocator);
//            titleArray.PushBack(t, allocator);
//            free((*i).title);
//        }
//
//        StringBuffer buffer;
//        Writer<StringBuffer> writer(buffer);
//
//        titleArray.Accept(writer);
//        const char *output = buffer.GetString();
//        if (buff_Json != NULL) {
//            free(buff_Json);
//            buff_Json = NULL;
//        }
//        buff_Json = (char *) malloc(buffer.GetSize());
//        memcpy(buff_Json, output, buffer.GetSize());
//        // buff_Json[buffer.GetSize()] = '\0';
////*titlelst_json = buff_Json;
//
//        ret = buffer.GetSize();
//    }
///*else {
//    ret=0;
//}*/
//
//    for (KEYWORDS::iterator i = keywords.begin(); i != keywords.end(); i++) {
//        free((*i).key);
//        free((*i).value);
//    }
//    env->ReleaseByteArrayElements(jkeywords_json, (jbyte *) keywords_json, 0);
//    env->ReleaseByteArrayElements(jtitle, (jbyte *) title, 0);
//    //curFunction="I'm sleep!";
//    return ret;
//}
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_get_usr_select_json
* Signature: ([B)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1get_1usr_1select_1json
        (JNIEnv *env, jobject, jbyteArray jtitlelst_json) {
    // curFunction=" SSR_lib_get_usr_select_json";
    char *titlelst_json = (char *) env->GetByteArrayElements(jtitlelst_json, 0);
    int len = env->GetArrayLength(jtitlelst_json);
    memcpy(titlelst_json, buff_Json, len);
    env->ReleaseByteArrayElements(jtitlelst_json, (jbyte *) titlelst_json, 0);

    //curFunction="I'm sleep!";
    return 0;
}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_domatchinginall
* Signature: ([I[BID)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1domatchinginall
//        (JNIEnv *env, jobject, jintArray jidxlst, jbyteArray jpeaks_json, jint isAccu,
//         jdouble lowcnt, jdouble snr) {
//    curFunction = "SSR_lib_domatchinginall ";
//    int *idxlst = env->GetIntArrayElements(jidxlst, 0);
//    int len = env->GetArrayLength(jidxlst);
//    SELIDX selidx;
//    for (int i = 0; i < len; i++) {
//        selidx.push_back(idxlst[i]);
//    }
//    len=env->GetArrayLength(jpeaks_json);
//    int ret = 0;
//    if (len > 0) {
//        jbyte *peaks_json = env->GetByteArrayElements(jpeaks_json, 0);
//        char *tbuf = (char *) malloc(len + 1);
//        memcpy(tbuf, peaks_json, len);
//        tbuf[len] = '\0';
//
//        PEAKS2 peaks;
//        Document d;
//        // d.SetObject();
//        d.Parse(tbuf);
//
//        Value wArray;
//        wArray = d["W"].GetArray();
//        Value IArray;
//        IArray = d["I"].GetArray();
//        peaks.peaks_hight = (double *) malloc(sizeof(double) * wArray.Size());
//        peaks.peaks_wcnt = (double *) malloc(sizeof(double) * wArray.Size());
//        for (int i = 0; i < (int) wArray.Size(); i++) {
//            peaks.peaks_hight[i] = IArray[i].GetDouble();
//            peaks.peaks_wcnt[i] = wArray[i].GetDouble();
//        }
//        free(tbuf);
//        peaks.num = wArray.Size();
//
//        ret = domatchinginall(&selidx, &peaks, isAccu, lowcnt, snr);
//        env->ReleaseIntArrayElements(jidxlst, idxlst, 0);
//        env->ReleaseByteArrayElements(jpeaks_json, (jbyte *) peaks_json, 0);
//        free(peaks.peaks_hight);
//        free(peaks.peaks_wcnt);
//    }
//    //curFunction="I'm sleep!";
//    return ret;
//}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_free_matching_data
* Signature: ()I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1free_1matching_1data
//        (JNIEnv *, jobject) {
//    curFunction = " SSR_lib_free_matching_data";
//    if (buff_Json != NULL) {
//        free(buff_Json);
//        buff_Json = NULL;
//    }
//
//    if (matching_peak_idxlst != NULL) {
//        free(matching_peak_idxlst);
//        matching_peak_idxlst = NULL;
//    }
//    //curFunction="I'm sleep!";
//    return free_matching_data();
//}
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_distroy
* Signature: ()I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1distroy
//        (JNIEnv *, jobject) {
//    curFunction = " SSR_lib_distroy";
//
//    if (buff_Json != NULL) {
//        free(buff_Json);
//        buff_Json = NULL;
//    }
//    if (matching_peak_idxlst != NULL) {
//        free(matching_peak_idxlst);
//        matching_peak_idxlst = NULL;
//    }
//    //curFunction="I'm sleep!";
//    return distroy();
//}
//例6
////////////////////////////////////获取结果										 //匹配之后读取数据                                                           //cur_step ==4 可执行
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_gettitle
* Signature: (I[B)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1gettitle
//        (JNIEnv *env, jobject, jint idx) {
//    curFunction = " SSR_lib_gettitle";
//    //curFunction="I'm sleep!";
//    return gettitle(idx, m_title);
//
//}
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_get_title
* Signature: ([B)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1get_1title
        (JNIEnv *env, jobject, jbyteArray jtitle) {
    curFunction = " SSR_lib_get_title";
    char *title = (char *) env->GetByteArrayElements(jtitle, 0);
    int len = env->GetArrayLength(jtitle);
    memcpy(title, m_title, len);
    env->ReleaseByteArrayElements(jtitle, (jbyte *) title, 0);
    // curFunction="I'm sleep!";
    return 0;
}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getpeaks
* Signature: (I[B)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getpeaks
//        (JNIEnv *, jobject, jint idx) {
//    curFunction = "SSR_lib_getpeaks ";
//    PEAKS peaks;
//    peaks.num = 0;
//    int ret = getpeaks(idx, &peaks);
//    if (ret > 0) {
//
//        Document d;    //生成一个dom元素Document
//        Document::AllocatorType &allocator = d.GetAllocator(); //获取分配器
//        d.SetObject();    //将当前的Document设置为一个object，也就是说，整个Document是一个Object类型的dom元素
//
//        Value peakArray(rapidjson::kArrayType);
//
//        for (int i = 0; i < peaks.num; i++) {
//            Value peak;
//            peak.SetObject();
//            Value wcnt;
//            wcnt.SetDouble(peaks.peaks[i].dWcnt);
//            peak.AddMember("W", wcnt, allocator);
//            Value intens;
//            intens.SetDouble(peaks.peaks[i].dIntensity);
//            peak.AddMember("I", intens, allocator);
//            Value left;
//            left.SetDouble(peaks.peaks[i].leftWcnt);
//            peak.AddMember("L", left, allocator);
//            Value right;
//            right.SetDouble(peaks.peaks[i].rightWcnt);
//            peak.AddMember("R", right, allocator);
//            Value mustbe;
//            mustbe.SetInt(peaks.peaks[i].mustbe);
//            peak.AddMember("M", mustbe, allocator);
//
//            peakArray.PushBack(peak, allocator);
//        }
//
//        StringBuffer buffer;
//        Writer<StringBuffer> writer(buffer);
//
//        peakArray.Accept(writer);
//        const char *output = buffer.GetString();
//        if (buff_Json != NULL) {
//            free(buff_Json);
//        }
//
//        buff_Json = (char *) malloc(buffer.GetSize());
//        memcpy(buff_Json, output, buffer.GetSize());
//        //buff_Json[buffer.GetSize()] = '\0';
////*peaks_json = buff_Json;
//        ret = buffer.GetSize();
//    }
///*else
//    ret = 0;*/
////*peaks_json = NULL;
//
//    //curFunction = "I'm sleep!";
//    return ret;
//}
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getpeaks_json
* Signature: ([B)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getpeaks_1json
        (JNIEnv *env, jobject, jbyteArray jpeaks_json) {
    curFunction = " SSR_lib_getpeaks_json";
    int len = env->GetArrayLength(jpeaks_json);
    char *peaks_json = (char *) env->GetByteArrayElements(jpeaks_json, 0);
    memcpy(peaks_json, buff_Json, len);
    env->ReleaseByteArrayElements(jpeaks_json, (jbyte *) peaks_json, 0);
    //curFunction = "I'm sleep!";
    return 0;
}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getmatchingpeaks
* Signature: (I[I)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getmatchingpeaks
//        (JNIEnv *env, jobject, jint idx) {
//    curFunction = "SSR_lib_getmatchingpeaks ";
//    intlst lst;
//    int len = getmatchingpeaks(idx, &lst);
//    if (matching_peak_idxlst != NULL) {
//        free(matching_peak_idxlst);
//        matching_peak_idxlst = NULL;
//    }
//    matching_peak_idxlst = (int *) malloc(sizeof(int) * len);
//    int cnt = 0;
//    for (intlst::iterator i = lst.begin(); i != lst.end(); i++) {
//        matching_peak_idxlst[cnt++] = (*i);
//    }
////*peaksidxlst = (len > 0) ? matching_peak_idxlst : NULL;
//    //curFunction = "I'm sleep!";
//    return len;
//}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getmatchingpeaks_json
* Signature: ([I)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getmatchingpeaks_1json
        (JNIEnv *env, jobject, jintArray jpeaksidxlst) {
    curFunction = "SSR_lib_getmatchingpeaks_json ";
    int *peaksidxlst = env->GetIntArrayElements(jpeaksidxlst, 0);
    int len = env->GetArrayLength(jpeaksidxlst);
    memcpy(peaksidxlst, matching_peak_idxlst, sizeof(int) * len);
    env->ReleaseIntArrayElements(jpeaksidxlst, peaksidxlst, 0);
    //curFunction = "I'm sleep!";
    return 0;
}
/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getpages
* Signature: (I[B)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getpages
//        (JNIEnv *env, jobject, jint idx) {
//    curFunction = "SSR_lib_getpages ";
//    PAGES pages;
//    pages.size = 0;
//    int ret = getpages(idx, &pages);
//    if (buff_Json != NULL) {
//        free(buff_Json);
//        buff_Json = NULL;
//    }
//    if (ret > 0)
//        buff_Json = pages.buff;
//    //curFunction = "I'm sleep!";
//    return ret;
//}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getpages_json
* Signature: ([B)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getpages_1json
        (JNIEnv *env, jobject, jbyteArray jpage_json) {
    curFunction = " SSR_lib_getpages_json";
    char *page_json = (char *) env->GetByteArrayElements(jpage_json, 0);
    int len = env->GetArrayLength(jpage_json);
    memcpy(page_json, buff_Json, sizeof(int) * len);
    env->ReleaseByteArrayElements(jpage_json, (jbyte *) page_json, 0);
    curFunction = "I'm sleep!";
    return 0;
}

/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_getspec
 * Signature: (IDD)[D
 */
//JNIEXPORT jdoubleArray JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getspec
//        (JNIEnv *env, jobject, jint idx, jdouble lowline, jdouble topline) {
//    curFunction = " ";
//    SPEC spec;
//    spec.len = 0;
//    getspec(idx, &spec, lowline, topline);
//////////////////////////////////////////
//    jdoubleArray jx;
//    if (spec.len > 0) {
//        jx = env->NewDoubleArray(spec.len * 2);
//        //jdoubleArray specArry=env->NewDoubleArray(spec.len<<1);
//        jdouble *x = env->GetDoubleArrayElements(jx, NULL);
//        memcpy(x, spec.dWcnt, (sizeof(double) * spec.len) * 2);
//        env->ReleaseDoubleArrayElements(jx, x, 0);
//        free(spec.dWcnt);
//    }
//    curFunction = "I'm sleep!";
//    return jx;
//}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getspec_json
* Signature: ([B)I
*/
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getspec_1json
        (JNIEnv *env, jobject, jbyteArray jspec_json) {
    curFunction = " ";
    char *spec_json = (char *) env->GetByteArrayElements(jspec_json, 0);
    int len = env->GetArrayLength(jspec_json);
    memcpy(spec_json, buff_Json, sizeof(int) * len);
    env->ReleaseByteArrayElements(jspec_json, (jbyte *) spec_json, 0);
    curFunction = "I'm sleep!";
    return 0;
}

/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getscore
* Signature: (I[D)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getscore
//        (JNIEnv *env, jobject, jint idx, jdoubleArray jscore) {
//    curFunction = " ";
//    double *score = env->GetDoubleArrayElements(jscore, 0);
//    int ret = getscore(idx, score);
//    env->ReleaseDoubleArrayElements(jscore, score, 0);
//    curFunction = "I'm sleep!";
//    return ret;
//}


/*
* Class:     com_ssraman_ssbj_support_ssrfunlib
* Method:    SSR_lib_getmatchingnum
* Signature: (I[I)I
*/
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getmatchingnum
//        (JNIEnv *env, jobject, jint idx, jintArray jnum) {
//    curFunction = " ";
//    int *nums = env->GetIntArrayElements(jnum, 0);
//    int ret = getmatchingnum(idx, nums);
//    env->ReleaseIntArrayElements(jnum, nums, 0);
//    return ret;
//
//}


/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_getSettingKeywords
 * Signature: ()I
 */
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getSettingKeywords
//        (JNIEnv *env, jobject) {
//    curFunction = " ";
//    if (buff_Json != NULL) {
//        free(buff_Json);
//        buff_Json = NULL;
//    }
//
//    int ret = getSettingKeywords(&buff_Json);
//
//    curFunction = "I'm sleep!";
//    return ret;
//}
/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_getSettingKeywords_json
 * Signature: ([B)I
 */
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getSettingKeywords_1json
        (JNIEnv *env, jobject, jbyteArray jkeywords_json) {
    curFunction = " ";
    char *page_json = (char *) env->GetByteArrayElements(jkeywords_json, 0);
    int len = env->GetArrayLength(jkeywords_json);
    memcpy(page_json, buff_Json, len);
    env->ReleaseByteArrayElements(jkeywords_json, (jbyte *) page_json, 0);
    curFunction = "I'm sleep!";
    return 0;

}

/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_setSettingKeywords
 * Signature: ([B)I
 */
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1setSettingKeywords
//        (JNIEnv *env, jobject, jbyteArray jbuff) {
//    curFunction = " SSR_lib_setSettingKeywords";
//    jbyte *page_json =  env->GetByteArrayElements(jbuff, 0);
//    int len=env->GetArrayLength(jbuff);
//    if (len > 0) {
//        char *tbuf = (char *) malloc(len + 1);
//        memcpy(tbuf, page_json, len);
//        tbuf[len] = '\0';
//        //setSettingKeywords(tbuf, len);
//        setSettingKeywords1(tbuf, len);
//        env->ReleaseByteArrayElements(jbuff, (jbyte *) page_json, 0);
//        free(tbuf);
//    }
//    curFunction = "I'm sleep!";
//    return 0;
//}

/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_getdbDatecnt
 * Signature: ()I
 */
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getdbDatecnt
//        (JNIEnv *env, jobject, jbyteArray jbuff) {
//    curFunction = " SSR_lib_getdbDatecnt";
//    char *page_json = (char *) env->GetByteArrayElements(jbuff, 0);
//    int len = env->GetArrayLength(jbuff);
//    int ret = -1;
//    if (len > 0) {
//        char *n = (char *) malloc(len + 1);
//        memcpy(n, page_json, len);
//        n[len] = '\0';
//        ret = getdbDatecnt(n);
//        free(n);
//        env->ReleaseByteArrayElements(jbuff, (jbyte *) page_json, 0);
//    }
//    curFunction = "I'm sleep!";
//    return ret;
//}

/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    od_matching
 * Signature: ([D[DID[D)I
 */
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_od_1matching
//        (JNIEnv *env, jobject obj, jdoubleArray peaks_wCnt, jdoubleArray peaks_hight,
//         jint matchingAccu, jdouble lowCnt, jdoubleArray results) {
//    curFunction = " od_matching";
//    CPEAKS2 peaks;
//    STDDATA stdata;
//    jsize len = env->GetArrayLength(peaks_wCnt);
//    jdouble *wCnt = env->GetDoubleArrayElements(peaks_wCnt, 0);
//    jdouble *hight = env->GetDoubleArrayElements(peaks_hight, 0);
//    peaks.num = len;
//    peaks.peaks_hight = hight;
//    peaks.peaks_wcnt = wCnt;
//    jdouble *pRe = env->GetDoubleArrayElements(results, 0);
//    //数据拆包
//    jdouble *pt = pRe;
//    jint ii = 0;
//    while (ii < env->GetArrayLength(results)) {
//        CMATCHDATA *t = (CMATCHDATA *) malloc(sizeof(CMATCHDATA));
//        t->num = (int) pt[ii++];
//        t->stID = (int) pt[ii++];
////        if(t->stID == 1063)
////            t->stID == 1063;
//// ii+=3;
//        ii += 2; //对齐
//        t->title_len = 0;
//        if (t->num > 0) {
//            t->peaks = (PEAK *) malloc(sizeof(PEAK) * t->num);
//            jint cnt = 0;
//            while (cnt < t->num) {
//                t->peaks[cnt].dWcnt = pt[ii++];
//                t->peaks[cnt].leftWcnt = pt[ii++];
//                t->peaks[cnt].rightWcnt = pt[ii++];
//                t->peaks[cnt].dIntensity = pt[ii++];
//                t->peaks[cnt++].mustbe = (int) pt[ii++];
//            }
//        }
//        //ii += 5;
//        stdata.push_back(t);
//    }
//    matching(&peaks, matchingAccu, lowCnt, &stdata);
//    pt = pRe;
//    ii = 0;
//    //数据打包
//    for (STDDATA::iterator iter = stdata.begin(); stdata.size() != 0;) {
//        CMATCHDATA *t = *iter;
//        pt[ii++] = (double) t->num;
//        pt[ii++] = (double) t->stID;
//        pt[ii++] = t->score;
////        pt[ii++] = t->score0;
////        pt[ii++] = t->score1;
////        pt[ii++] = t->score2;
//        pt[ii++] = (double) t->matchingNum;
//        if (t->matchingNum > 0) {
//            //for (int i = 0; i < t->matchingNum; i++) {
//            for (PEAKCUPLElst::iterator i = t->matchingPeakIdxlist->begin();
//                 i != t->matchingPeakIdxlist->end(); i++) {
//                pt[ii++] = t->peaks[(*i).pcode].dWcnt;
//                pt[ii++] = t->peaks[(*i).pcode].leftWcnt;
//                pt[ii++] = t->peaks[(*i).pcode].rightWcnt;
//                pt[ii++] = t->peaks[(*i).pcode].dIntensity;
//                pt[ii++] = (double) t->peaks[(*i).pcode].mustbe;
//            }
//        }
//        free(t->peaks);
//        //ii += 5;
//
//        iter = stdata.erase(iter);
//        free(t);
//
//
//    }
//
//    env->ReleaseDoubleArrayElements(peaks_wCnt, wCnt, 0);
//    env->ReleaseDoubleArrayElements(peaks_hight, hight, 0);
//    env->ReleaseDoubleArrayElements(results, pRe, 0);
//    curFunction = "I'm sleep!";
//    return ii;
//
//}

/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    od_seekPeaks
 * Signature: ([D[DD[D[D)I
 */
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_od_1seekPeaks
        (JNIEnv *env, jobject obj, jdoubleArray jpWaveCnt, jdoubleArray jpSpc, jdouble snr,
         jdoubleArray peaks_wcnt, jdoubleArray peaks_hight) {
    curFunction = " od_seekPeaks";
    jint res = 0;
    PEAKS2 peaks;
    SPEC spec;
    int inttime_ms = 0;

    jsize len = env->GetArrayLength(jpSpc);
    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
    spec.dIntensity = pSpc;
    //jsize pWaveCntlen = env->GetArrayLength(jpWaveCnt);
    jdouble *pWaveCnt = env->GetDoubleArrayElements(jpWaveCnt, 0);
    spec.dWcnt = pWaveCnt;
    spec.len = len;

    jdouble *pPeaks_wcnt = env->GetDoubleArrayElements(peaks_wcnt, 0);
    //jdouble *pPeaks_width = env->GetDoubleArrayElements(peaks_width, 0);
    jdouble *pPeaks_hight = env->GetDoubleArrayElements(peaks_hight, 0);


    peaks.peaks_hight = pPeaks_hight;
    //peaks.peaks_width = pPeaks_width;
    peaks.peaks_wcnt = pPeaks_wcnt;

    res = seePeaks(&spec, snr, inttime_ms, &peaks,3);

    env->ReleaseDoubleArrayElements(peaks_wcnt, pPeaks_wcnt, 0);
    //env->ReleaseDoubleArrayElements(peaks_width, pPeaks_width, 0);
    env->ReleaseDoubleArrayElements(peaks_hight, pPeaks_hight, 0);

    env->ReleaseDoubleArrayElements(jpWaveCnt, pWaveCnt, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSpc, 0);
    curFunction = "I'm sleep!";
    return peaks.num;
}


JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_od_1seekPeaks_1idx
        (JNIEnv *env, jobject obj, jdoubleArray jpWaveCnt, jdoubleArray jpSpc, jdouble snr,
         jintArray outLength,jintArray peaks_idx, jint max_peak_cnt) {
    curFunction = " od_seekPeaks_idx";
    jint res = 0;

    jsize len = env->GetArrayLength(jpSpc);
    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pWaveCnt = env->GetDoubleArrayElements(jpWaveCnt, 0);
    jint *pPeaks_idx = env->GetIntArrayElements(peaks_idx, 0);
    jint *poutLength = env->GetIntArrayElements(outLength,JNI_FALSE);

    res = seePeaks_idx(pSpc, snr,*poutLength, pPeaks_idx, max_peak_cnt,3);

    env->ReleaseIntArrayElements(peaks_idx, pPeaks_idx, 0);

    //env->ReleaseDoubleArrayElements(jpWaveCnt, pWaveCnt, 0);
    //env->ReleaseDoubleArrayElements(jpSpc, pSpc, 0);
    curFunction = "I'm sleep!";
    return res;
}


/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    od_shortspec
 * Signature: ([D[D[D[D[D)I
 */
JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_od_1shortspec
        (JNIEnv *env, jobject obj, jdoubleArray jpWcnt, jdoubleArray jpSpc, jdoubleArray jpeaksWcnt,
         jdoubleArray jpWcnt_s, jdoubleArray jpSpc_s) {
    curFunction = " od_shortspec";
    jint re;
    jdouble *pWaveCnt = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pPeaksWcnt = env->GetDoubleArrayElements(jpeaksWcnt, 0);
    jdouble *pWaveCnt_s = env->GetDoubleArrayElements(jpWcnt_s, 0);
    jdouble *pSpc_s = env->GetDoubleArrayElements(jpSpc_s, 0);
    SPEC spec;
    SPEC spec_s;

    spec.dIntensity = pSpc;
    spec.dWcnt = pWaveCnt;
    spec.len = env->GetArrayLength(jpSpc);
    jint peaksNum = env->GetArrayLength(jpeaksWcnt);
    spec_s.dIntensity = pSpc_s;
    spec_s.dWcnt = pWaveCnt_s;

    re = makeshortSpec(&spec, pPeaksWcnt, peaksNum, &spec_s);

    env->ReleaseDoubleArrayElements(jpWcnt, pWaveCnt, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSpc, 0);
    env->ReleaseDoubleArrayElements(jpWcnt_s, pWaveCnt_s, 0);
    env->ReleaseDoubleArrayElements(jpSpc_s, pSpc_s, 0);
    env->ReleaseDoubleArrayElements(jpeaksWcnt, pPeaksWcnt, 0);
    curFunction = "I'm sleep!";
    return re;
}

//JNIEXPORT jint JNICALL
//        Java_com_ssraman_ssbj_support_ssrfunlib_kmfilter(JNIEnv *env, jobject instance,
//                                                             jdoubleArray jpSpc, jdoubleArray jpWcnt_r){
//
//    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
//    jdouble *pSpc_r = env->GetDoubleArrayElements(jpWcnt_r, 0);
//    jint spec_size= env->GetArrayLength(jpSpc);
//
//    return 1;
//}
JNIEXPORT jint JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_od_1newspec(JNIEnv *env, jobject instance,
                                                    jdoubleArray jpWcnt,
                                                    jdoubleArray jpSpc, jdoubleArray jpWcnt_s,
                                                    jdoubleArray jpSpc_s, jdouble jlowline) {

    //curFunction = " od_newspec";
    jint re;
    jdouble noise;
    jdouble *pWaveCnt = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pWaveCnt_s = env->GetDoubleArrayElements(jpWcnt_s, 0);
    jdouble *pSpc_s = env->GetDoubleArrayElements(jpSpc_s, 0);
    SPEC spec;
    SPEC spec_s;

    spec.dIntensity = pSpc;
    spec.dWcnt = pWaveCnt;
    spec.len = env->GetArrayLength(jpSpc);
    spec_s.dIntensity = pSpc_s;
    spec_s.dWcnt = pWaveCnt_s;
    spec_s.len = env->GetArrayLength(jpSpc_s);

//    re = getNewSpec(&spec, (jint) jlowline, spec_s.len + (jint) jlowline, &spec_s, 1);
  //  re = getNewSpec_20201101(&spec, (jint) jlowline, spec_s.len + (jint) jlowline, &spec_s, 1);
//    noise = getNewSpec_20201101(&spec, (int)jlowline, spec_s.len + (int)jlowline, &spec_s, 1);
    noise =getNewSpec_20210308(&spec, (int)jlowline, spec_s.len + (int)jlowline, &spec_s, 1);
    clear_feak_peaks(spec_s.dIntensity, spec_s.len, 8, 3*noise);  // 8改成 6 或者7   3去掉
//    clear_feak_peaks(spec_s.dIntensity, spec_s.len,20, 3*noise);
    env->ReleaseDoubleArrayElements(jpWcnt, pWaveCnt, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSpc, 0);
    env->ReleaseDoubleArrayElements(jpWcnt_s, pWaveCnt_s, 0);
    env->ReleaseDoubleArrayElements(jpSpc_s, pSpc_s, 0);
    //curFunction = "I'm sleep!";
    return (jint)noise;

}
JNIEXPORT jint JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_od_1newspecsmooth(JNIEnv *env, jobject instance,
                                                    jdoubleArray jpWcnt,
                                                    jdoubleArray jpSpc, jdoubleArray jpWcnt_s,
                                                    jdoubleArray jpSpc_s, jdouble jlowline) {

    //curFunction = " od_newspec";
    jint re;
    jdouble noise;
    jdouble *pWaveCnt = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pWaveCnt_s = env->GetDoubleArrayElements(jpWcnt_s, 0);
    jdouble *pSpc_s = env->GetDoubleArrayElements(jpSpc_s, 0);
    SPEC spec;
    SPEC spec_s;

    spec.dIntensity = pSpc;
    spec.dWcnt = pWaveCnt;
    spec.len = env->GetArrayLength(jpSpc);
    spec_s.dIntensity = pSpc_s;
    spec_s.dWcnt = pWaveCnt_s;
    spec_s.len = env->GetArrayLength(jpSpc_s);

//    re = getNewSpec(&spec, (jint) jlowline, spec_s.len + (jint) jlowline, &spec_s, 1);
    //  re = getNewSpec_20201101(&spec, (jint) jlowline, spec_s.len + (jint) jlowline, &spec_s, 1);
//    noise = getNewSpec_20201101(&spec, (int)jlowline, spec_s.len + (int)jlowline, &spec_s, 1);
    noise =getNewSpecSmooth_20210308(&spec, (int)jlowline, spec_s.len + (int)jlowline, &spec_s, 1);
    clear_feak_peaks(spec_s.dIntensity, spec_s.len, 8, 3*noise);  // 8改成 6 或者7   3去掉
//    clear_feak_peaks(spec_s.dIntensity, spec_s.len,20, 3*noise);
    env->ReleaseDoubleArrayElements(jpWcnt, pWaveCnt, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSpc, 0);
    env->ReleaseDoubleArrayElements(jpWcnt_s, pWaveCnt_s, 0);
    env->ReleaseDoubleArrayElements(jpSpc_s, pSpc_s, 0);
    //curFunction = "I'm sleep!";
    return (jint)noise;

}
JNIEXPORT jint JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_od_1newspecautobase(JNIEnv *env, jobject instance,
                                                          jdoubleArray jpWcnt,
                                                          jdoubleArray jpSpc, jdoubleArray jpWcnt_s,
                                                          jdoubleArray jpSpc_s, jdouble jlowline) {

    //curFunction = " od_newspec";
    jint re;
    jdouble noise;
    jdouble *pWaveCnt = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pWaveCnt_s = env->GetDoubleArrayElements(jpWcnt_s, 0);
    jdouble *pSpc_s = env->GetDoubleArrayElements(jpSpc_s, 0);
    SPEC spec;
    SPEC spec_s;

    spec.dIntensity = pSpc;
    spec.dWcnt = pWaveCnt;
    spec.len = env->GetArrayLength(jpSpc);
    spec_s.dIntensity = pSpc_s;
    spec_s.dWcnt = pWaveCnt_s;
    spec_s.len = env->GetArrayLength(jpSpc_s);

//    re = getNewSpec(&spec, (jint) jlowline, spec_s.len + (jint) jlowline, &spec_s, 1);
    //  re = getNewSpec_20201101(&spec, (jint) jlowline, spec_s.len + (jint) jlowline, &spec_s, 1);
//    noise = getNewSpec_20201101(&spec, (int)jlowline, spec_s.len + (int)jlowline, &spec_s, 1);
    noise =getNewSpecAutobase_20210308(&spec, (int)jlowline, spec_s.len + (int)jlowline, &spec_s, 1);
    clear_feak_peaks(spec_s.dIntensity, spec_s.len, 8, 3*noise);  // 8改成 6 或者7   3去掉
//    clear_feak_peaks(spec_s.dIntensity, spec_s.len,20, 3*noise);
    env->ReleaseDoubleArrayElements(jpWcnt, pWaveCnt, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSpc, 0);
    env->ReleaseDoubleArrayElements(jpWcnt_s, pWaveCnt_s, 0);
    env->ReleaseDoubleArrayElements(jpSpc_s, pSpc_s, 0);
    //curFunction = "I'm sleep!";
    return (jint)noise;

}

JNIEXPORT jint JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_od_1newspecinterpolate(JNIEnv *env, jobject instance,
                                                            jdoubleArray jpWcnt,
                                                            jdoubleArray jpSpc, jdoubleArray jpWcnt_s,
                                                            jdoubleArray jpSpc_s, jdouble jlowline) {

    //curFunction = " od_newspec";
    jint re;
    jdouble noise;
    jdouble *pWaveCnt = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSpc = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pWaveCnt_s = env->GetDoubleArrayElements(jpWcnt_s, 0);
    jdouble *pSpc_s = env->GetDoubleArrayElements(jpSpc_s, 0);
    SPEC spec;
    SPEC spec_s;

    spec.dIntensity = pSpc;
    spec.dWcnt = pWaveCnt;
    spec.len = env->GetArrayLength(jpSpc);
    spec_s.dIntensity = pSpc_s;
    spec_s.dWcnt = pWaveCnt_s;
    spec_s.len = env->GetArrayLength(jpSpc_s);

//    re = getNewSpec(&spec, (jint) jlowline, spec_s.len + (jint) jlowline, &spec_s, 1);
    //  re = getNewSpec_20201101(&spec, (jint) jlowline, spec_s.len + (jint) jlowline, &spec_s, 1);
//    noise = getNewSpec_20201101(&spec, (int)jlowline, spec_s.len + (int)jlowline, &spec_s, 1);
    noise =getNewSpecInterpolate_20220909(&spec, (int)jlowline, spec_s.len + (int)jlowline, &spec_s, 1);
    clear_feak_peaks(spec_s.dIntensity, spec_s.len, 8, 3*noise);  // 8改成 6 或者7   3去掉
//    clear_feak_peaks(spec_s.dIntensity, spec_s.len,20, 3*noise);
    env->ReleaseDoubleArrayElements(jpWcnt, pWaveCnt, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSpc, 0);
    env->ReleaseDoubleArrayElements(jpWcnt_s, pWaveCnt_s, 0);
    env->ReleaseDoubleArrayElements(jpSpc_s, pSpc_s, 0);
    //curFunction = "I'm sleep!";
    return (jint)noise;

}


JNIEXPORT jint JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_od_1convertdata(JNIEnv *env, jobject instance,
                                                               jcharArray jspBuff,
                                                               jdoubleArray jpIntensity, jint jlen,
                                                               jdouble jxs) {

    jint re;
    jchar *spBuff = env->GetCharArrayElements(jspBuff, 0);
    jdouble *pIntensity = env->GetDoubleArrayElements(jpIntensity, 0);

    re =convertdata_org(spBuff, pIntensity,jlen,jxs);

    env->ReleaseDoubleArrayElements(jpIntensity, pIntensity, 0);
    return re;
}


/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_usr_select_step
 * Signature: ([B)I
 */
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1usr_1select_1step
//        (JNIEnv *env, jobject, jbyteArray jkeywords_json) {
//    curFunction = " SR_lib_usr_select_step";
//    jbyte *keywords_json =  env->GetByteArrayElements(jkeywords_json, 0);
//
//    KEYWORDS keywords;
///////////////////////////////keywords_json拆包
//    int len=env->GetArrayLength(jkeywords_json);
//    if(len>0) {
//        char *tbuf = (char *) malloc(len + 1);
//        memcpy(tbuf, keywords_json, len);
//        tbuf[len] = '\0';
//
//
//        Document document;
//        //是否应该设置Obj
////    document.SetObject();
//
//        document.Parse(tbuf);
//
//        Value kArray;
//        kArray.SetArray();
//        if (document.HasMember("kArray")) {
//            kArray = document["kArray"].GetArray();
//            if (kArray.IsArray() && !kArray.Empty()) {
//                for (rapidjson::SizeType i = 0; i < kArray.Size(); i++) {
//                    int tlen = kArray[i]["val"].GetStringLength();
//                    char *buff = (char *) malloc(tlen + 1);
//                    memcpy(buff, kArray[i]["val"].GetString(), tlen);
//                    buff[tlen] = '\0';
//                    KEYWORD keyw;
//                    keyw.value = buff;
//                    keyw.value_len = tlen;
//                    tlen = kArray[i]["key"].GetStringLength();
//                    char *buff1 = (char *) malloc(tlen + 1);
//                    memcpy(buff1, kArray[i]["key"].GetString(), tlen);
//                    buff1[tlen] = '\0';
//                    keyw.key = buff1;
//                    keyw.key_len = tlen;
//                    keywords.push_back(keyw);
//                }
//            }
//        }
//    }
//
//    int ret = usr_select_step(&keywords);
//
//    for (KEYWORDS::iterator i = keywords.begin(); i != keywords.end(); i++) {
//        free((*i).key);
//        free((*i).value);
//    }
//    env->ReleaseByteArrayElements(jkeywords_json, (jbyte *) keywords_json, 0);
//    curFunction = "I'm sleep!";
//    return ret;
//}

//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_reloadnamelst(JNIEnv *env, jobject instance) {
//
//    // TODO
//    int ret = reloadnamelst();
//    return ret;
//}

/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_get_usr_select_step_title
 * Signature: (I)[B
 */
//JNIEXPORT jbyteArray JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1get_1usr_1select_1step_1title
//        (JNIEnv *env, jobject, jint jidx) {
//    curFunction = " SSR_lib_get_usr_select_step_title";
//    char *buff = NULL;
//    int len = usr_select_step_title(jidx, &buff);
//    jbyteArray jtitle;
//    if (len > 0)
//        jtitle = env->NewByteArray(len);
//    else
//        return jtitle;
//    jbyte *pArray = env->GetByteArrayElements(jtitle, 0);
//
//    memcpy(pArray, buff, len);
//
//    env->ReleaseByteArrayElements(jtitle, pArray, 0);
//    curFunction = "I'm sleep!";
//    return jtitle;
//}

///*
// * Class:     com_ssraman_ssbj_support_ssrfunlib
// * Method:    SSR_lib_getpeaks_step
// * Signature: (I)I
// */
//JNIEXPORT jint JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getpeaks_1step
//        (JNIEnv *env, jobject, jint idx){
//
//}
//
///*
// * Class:     com_ssraman_ssbj_support_ssrfunlib
// * Method:    SSR_lib_getpeaks_step_json
// * Signature: (I)[B
// */
//JNIEXPORT jbyteArray JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getpeaks_1step_1json
//        (JNIEnv *, jobject, jint);

/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_getpages_step
 * Signature: (I)I
 */
//JNIEXPORT jbyteArray JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1getpages_1step
//        (JNIEnv *env, jobject, jint idx) {
//    curFunction = "SSR_lib_getpages_step";
//    PAGES pages;
//    pages.size = 0;
//    int ret = getpages(idx, &pages);
//    if (buff_Json != NULL) {
//        free(buff_Json);
//        buff_Json = NULL;
//    }
//    jbyteArray jreStr;
//    if (ret > 0){
//        buff_Json = pages.buff;
//        Document document;
//        document.Parse(buff_Json);
//        Value arr;
//        arr.SetArray();
//        arr = document.GetArray();
//        if (arr.IsArray()) {
//            Document::AllocatorType &allocator = document.GetAllocator(); //获取分配器
//            Value titlelst;
//            titlelst.SetArray();
//            for (int i = 0; i < arr.Size(); i++) {
//                Value titleStr;
//                titleStr = arr[i]["title"];
//                titlelst.PushBack(titleStr, allocator);
//            }
//            StringBuffer buffer;
//            Writer<StringBuffer> writer(buffer);
//            titlelst.Accept(writer);
//
//            const char *output = buffer.GetString();
//            jreStr = env->NewByteArray(buffer.GetSize());
//            jbyte *p = env->GetByteArrayElements(jreStr, 0);
//            memcpy(p, output, buffer.GetSize());
//            env->ReleaseByteArrayElements(jreStr, p, 0);
//
//        }
//    }
//    return jreStr;
//}

/*
 * Class:     com_ssraman_ssbj_support_ssrfunlib
 * Method:    SSR_lib_get1page_json
 * Signature: (I)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1get1page_1json
        (JNIEnv *env, jobject, jint idx) {
    curFunction = "SSR_lib_get1page_json";
    Document document;
    document.Parse(buff_Json);
    Value arr;
    arr.SetArray();
    arr = document.GetArray();
    jbyteArray jreStr;
    if (arr.IsArray()) {
        if(idx<arr.Size()){
            Value page;
            page.SetObject();
            page=arr[idx].GetObject();
            StringBuffer buffer;
            Writer<StringBuffer> writer(buffer);

            page.Accept(writer);
            const char *output = buffer.GetString();
            jreStr = env->NewByteArray(buffer.GetSize());
            jbyte *p = env->GetByteArrayElements(jreStr, 0);
            memcpy(p, output, buffer.GetSize());
            env->ReleaseByteArrayElements(jreStr, p, 0);
        }
    }

    return jreStr;
}

//光谱COSD算法
JNIEXPORT jdouble JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_od_1cosdspec(JNIEnv *env, jobject instance,
                                                    jdoubleArray jpWcnt,
                                                    jdoubleArray jpSpc,jint jplen,jdoubleArray jpWcnt_s,
                                                    jdoubleArray jpSpc_s,jint jplen_s,jdouble jlowline) {

    jdouble correlation;
    jdouble *pWavenum1 = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSignal1 = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pWavenum2 = env->GetDoubleArrayElements(jpWcnt_s, 0);
    jdouble *pSignal2 = env->GetDoubleArrayElements(jpSpc_s, 0);

    correlation = match_cosd(pWavenum1, pSignal1, jplen, pWavenum2, pSignal2, jplen_s);

    env->ReleaseDoubleArrayElements(jpWcnt, pWavenum1, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSignal1, 0);
    env->ReleaseDoubleArrayElements(jpWcnt_s, pWavenum2, 0);
    env->ReleaseDoubleArrayElements(jpSpc_s, pSignal2, 0);

    return correlation;
}

//光谱HQI算法
JNIEXPORT jdouble JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_od_1hqispec(JNIEnv *env, jobject instance,
                                                     jdoubleArray jpWcnt,
                                                     jdoubleArray jpSpc,jint jplen, jdoubleArray jpWcnt_s,
                                                     jdoubleArray jpSpc_s,jint jplen_s, jdouble jlowline) {


    jdouble correlation;
    jdouble *pWavenum1 = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSignal1 = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pWavenum2 = env->GetDoubleArrayElements(jpWcnt_s, 0);
    jdouble *pSignal2 = env->GetDoubleArrayElements(jpSpc_s, 0);

    correlation = match_hqi(pWavenum1, pSignal1, jplen, pWavenum2, pSignal2, jplen_s);

    env->ReleaseDoubleArrayElements(jpWcnt, pWavenum1, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSignal1, 0);
    env->ReleaseDoubleArrayElements(jpWcnt_s, pWavenum2, 0);
    env->ReleaseDoubleArrayElements(jpSpc_s, pSignal2, 0);

    return correlation;

}

//光谱COSD算法
JNIEXPORT jdouble JNICALL
Java_com_ssraman_ssbj_support_ssrfunlib_od_1cosdwholespec(JNIEnv *env, jobject instance,
                                                     jdoubleArray jpWcnt,
                                                     jdoubleArray jpSpc,jint jplen,jdoubleArray jpWcnt_s,
                                                     jdoubleArray jpSpc_s,jint jplen_s,jdouble jlowline) {

    jdouble correlation;
    jdouble *pWavenum1 = env->GetDoubleArrayElements(jpWcnt, 0);
    jdouble *pSignal1 = env->GetDoubleArrayElements(jpSpc, 0);
    jdouble *pWavenum2 = env->GetDoubleArrayElements(jpWcnt_s, 0);
    jdouble *pSignal2 = env->GetDoubleArrayElements(jpSpc_s, 0);

    correlation = match_cosd_w(pWavenum1, pSignal1, jplen, pWavenum2, pSignal2, jplen_s);

    env->ReleaseDoubleArrayElements(jpWcnt, pWavenum1, 0);
    env->ReleaseDoubleArrayElements(jpSpc, pSignal1, 0);
    env->ReleaseDoubleArrayElements(jpWcnt_s, pWavenum2, 0);
    env->ReleaseDoubleArrayElements(jpSpc_s, pSignal2, 0);

    return correlation;
}

//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_init_1usr_1matching_1module(JNIEnv *env, jobject instance,
//                                                                    jbyteArray dbname_,
//                                                                    jdouble lowcnt) {
//    jbyte *dbname = env->GetByteArrayElements(dbname_, NULL);
//    // TODO
//    jint len = env->GetArrayLength(dbname_);
//    int ret = 1;
//    if (len > 0) {
//        ret = init_usr_matching_module((char *) dbname, len, lowcnt);
//    }
//    env->ReleaseByteArrayElements(dbname_, dbname, 0);
//    return ret;
//}

//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_delete_1usr_1spec(JNIEnv *env, jobject instance,
//                                                          jint idxspec) {
//
//    // TODO
//    int ret = delete_usr_spec(idxspec);
//    return ret;
//
//}

//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_delete_1usr_1sample(JNIEnv *env, jobject instance,
//                                                            jint idx) {
//
//    // TODO
//    int ret = delete_usr_sample(idx);
//    return ret;
//
//}

//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_append_1usr_1sample(JNIEnv *env, jobject instance,
//                                                            jbyteArray data_json_,
//                                                            jbyteArray pages_,
//                                                            jint smpidx
//) {
//    jbyte *data_json = env->GetByteArrayElements(data_json_, NULL);
//    jbyte *pages = env->GetByteArrayElements(pages_, NULL);
//
//    // TODO
//    PAGES t;
//    int len = env->GetArrayLength(pages_);
//    if (len > 0) {
//        t.buff = (char *) malloc(len + 1);
//        memcpy(t.buff, (char *) pages, len);
//        t.buff[len] = '\0';
//        t.size = len;
//    } else {
//        t.buff = NULL;
//        t.size = 0;
//    }
//    int ret;
//    char *buf = NULL;
//    len = env->GetArrayLength(data_json_);
//    if (len > 0) {
//        if (len > 0) {
//            buf = (char *) malloc(len + 1);
//            memcpy(buf, (char *) data_json, len);
//            buf[len] = '\0';
//            ret = append_usr_sample(buf, &t, smpidx);
//        }
//
//    }
//    if (t.size > 0)
//        free(t.buff);
//    if (len > 0)
//        free(buf);
//
//    env->ReleaseByteArrayElements(data_json_, data_json, 0);
//    env->ReleaseByteArrayElements(pages_, pages, 0);
//    return ret;
//}

//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_append_1usr_1spec(JNIEnv *env, jobject instance,
//                                                          jbyteArray data_json_, jdoubleArray x_,
//                                                          jdoubleArray y_,
//                                                          jint smpidx,
//                                                          jint specidx
//) {
//    jbyte *data_json = env->GetByteArrayElements(data_json_, NULL);
//    int len = env->GetArrayLength(data_json_);
//    char *buf = NULL;
//    if (len > 0) {
//        buf = (char *) malloc(len + 1);
//        memcpy(buf, data_json, len);
//        buf[len] = '\0';
//    }
//    jdouble *x = env->GetDoubleArrayElements(x_, NULL);
//    jdouble *y = env->GetDoubleArrayElements(y_, NULL);
//    // TODO
//    SPEC spec;
//    spec.len = env->GetArrayLength(x_);
//    int ret = 0;
//    if (spec.len > 0) {
//        spec.dWcnt = (double *) malloc(spec.len * sizeof(double) * 2);
//        memcpy(spec.dWcnt, x, spec.len * sizeof(double));
//        memcpy(spec.dWcnt + spec.len, y, spec.len * sizeof(double));
//        spec.dIntensity = spec.dWcnt + spec.len;
//        ret = append_usr_spec(buf, &spec, smpidx);
//    } else {
//        spec.len = 0;
//        ret = append_usr_spec(buf, &spec, smpidx, specidx);
//    }
//    if (len > 0) {
//        free(buf);
//    }
//
//    if (spec.len != 0)
//        free(spec.dWcnt);
//    env->ReleaseByteArrayElements(data_json_, data_json, 0);
//    env->ReleaseDoubleArrayElements(x_, x, 0);
//    env->ReleaseDoubleArrayElements(y_, y, 0);
//    return ret;
//}

//JNIEXPORT jbyteArray JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_get_1usr_1peaks(JNIEnv *env, jobject instance, jint idx) {
//
//    // TODO
//    jbyteArray jre;
//    char *peaks;
//    int ret = get_usr_peaks(idx, &peaks);
//    if (ret > 0) {
//        jre = env->NewByteArray(ret);
//        jbyte *data = env->GetByteArrayElements(jre, 0);
//        memcpy(data, peaks, ret);
//        env->ReleaseByteArrayElements(jre, data, 0);
//        free(peaks);
//    }
//
//    return jre;
//}
//JNIEXPORT jbyteArray JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_get_1usr_1peaks(JNIEnv *env, jobject instance, jint idx) {
//JNIEXPORT jbyteArray JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_get_1usr_1peaks1(JNIEnv *env, jobject instance, jint idx) {
//
//    // TODO
//    jbyteArray jre;
//    char *peaks;
//    int ret = get_usr_peaks(idx, &peaks);
//    if (ret > 0) {
//        jre = env->NewByteArray(ret);
//        jbyte *data = env->GetByteArrayElements(jre, 0);
//        memcpy(data, peaks, ret);
//        env->ReleaseByteArrayElements(jre, data, 0);
//        free(peaks);
//    }
//
//
//    return jre;
//}

//JNIEXPORT jdoubleArray JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_get_1usr_1spec(JNIEnv *env, jobject instance,
//                                                       jint specidx,
//                                                       jint lowline, jint topline) {
//
//    // TODO
//    jdoubleArray jre;
//    SPEC spec;
//    int ret = get_usr_spec(&spec, specidx, lowline, topline) * 2;
//    jre = env->NewDoubleArray(ret);
//    jdouble *data = env->GetDoubleArrayElements(jre, 0);
//    memcpy(data, spec.dWcnt, env->GetArrayLength(jre) * sizeof(double));
//    env->ReleaseDoubleArrayElements(jre, (double *) data, 0);
//    free(spec.dWcnt);
//    return jre;
//}

//JNIEXPORT jbyteArray JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_get_1usr_1pages_1step(JNIEnv *env, jobject instance,
//                                                              jint idx) {
//    // TODO 需要对json解调
//    PAGES pages;
//    pages.size = 0;
//    int ret = get_usr_pages(idx, &pages);
//    jbyteArray jreStr;
//    //如果数据位空返回空串
//    if (ret < 1) {
//        jreStr = env->NewByteArray(0);
////        jbyte *p = env->GetByteArrayElements(jreStr, 0);
////        p[0]='{';
////        p[1]='}';
////      env->ReleaseByteArrayElements(jreStr, p, 0);
//        return jreStr;
//    }
//    if (buff_Json != NULL) {
//        free(buff_Json);
//        buff_Json = NULL;
//    }
//
//
//    buff_Json = pages.buff;
//    Document document;
//    document.Parse(buff_Json);
//    Value arr;
//    arr.SetArray();
//    arr = document.GetArray();
//    if (arr.IsArray()) {
//        Document::AllocatorType &allocator = document.GetAllocator(); //获取分配器
//        Value titlelst;
//        titlelst.SetArray();
//        for (int i = 0; i < arr.Size(); i++) {
//            Value titleStr;
//            titleStr = arr[i]["title"];
//            titlelst.PushBack(titleStr, allocator);
//        }
//        StringBuffer buffer;
//        Writer<StringBuffer> writer(buffer);
//        titlelst.Accept(writer);
//
//        const char *output = buffer.GetString();
//
//        jreStr = env->NewByteArray(buffer.GetSize());
//        jbyte *p = env->GetByteArrayElements(jreStr, 0);
//        memcpy(p, output, buffer.GetSize());
//        env->ReleaseByteArrayElements(jreStr, p, 0);
//    }
//
//    return jreStr;
//
//
//}

//JNIEXPORT jbyteArray JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_get_1usr_1smpinfo(JNIEnv *env, jobject instance, jint idx) {
//    // TODO
//    char *buff;
//    int ret = get_usr_smpinfo(idx, &buff);
//    jbyteArray jreStr;
//    if (ret > 0) {
//        jreStr = env->NewByteArray(ret);
//        jbyte *p = env->GetByteArrayElements(jreStr, 0);
//        memcpy(p, buff, ret);
//        env->ReleaseByteArrayElements(jreStr, p, 0);
//        free(buff);
//    }
//    return jreStr;
//}

//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_SSR_1lib_1usr_1select_1step_1defKeyword(JNIEnv *env,
//                                                                                jobject instance,
//                                                                                jint idx) {
//
//    // TODO
//    return loadDefKeyword(idx);
//
//}
//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_get_1usr_1spec_1cnt(JNIEnv *env, jobject instance,
//                                                            jint smpidx) {
//    // TODO
//    return get_usr_smp_spec_cnt(smpidx);
//}
//JNIEXPORT jint JNICALL
//Java_com_ssraman_ssbj_support_ssrfunlib_set_1usr_1smpinfo(JNIEnv *env, jobject instance, jint idx,
//                                                          jbyteArray data_json_,jbyteArray title_) {
//    jbyte *data_json = env->GetByteArrayElements(data_json_, NULL);
//    jbyte *title = env->GetByteArrayElements(title_, NULL);
//    // TODO
//    int len=env->GetArrayLength(data_json_);
//    int titlelen=env->GetArrayLength(title_);
//    char* buf=(char*)malloc(len+1);
//    memcpy(buf,data_json,len);
//    buf[len]='\0';
//
//    char* tbuf=(char*)malloc(titlelen+1);
//    memcpy(tbuf,title,titlelen);
//    tbuf[titlelen]='\0';
//    int ret=set_usr_smpinfo(idx, buf,len,tbuf,titlelen);
//    free(buf);
//    free(tbuf);
//
//    env->ReleaseByteArrayElements(data_json_, data_json, 0);
//    env->ReleaseByteArrayElements(title_, title, 0);
//    return ret;
//}
}