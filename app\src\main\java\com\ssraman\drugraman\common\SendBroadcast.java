package com.ssraman.drugraman.common;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.util.Log;


import com.ssraman.lib_common.mac.SettingPre;
import com.ssraman.lib_common.utils.Utils;

import java.util.Date;


/**
 * Created by Administrator on 2018/7/24.
 */

public class SendBroadcast {
    private static SendBroadcast sendBroadcast = null;
    private Context context = null;
    private boolean starting = false;
    private boolean isChecking = false;
    boolean delayStatus = false;
    private Runnable runnable = null;

    //   Timer timer;

    /**
     * 固件启动时长
     * 2020/5/14
     */
//    private int startTime = 10 * 1000;
    private int startTime = 4000;
    /**
     * 进入当前页面的时间 做提醒使用
     */
    private long enterTime = 0;
    /**
     * 固件启动时长是否结束
     */
    private boolean isEnable = false;

    public boolean isEnable() {
        return isEnable;
    }

    public boolean isChecking() {
        return isChecking;
    }

    public void setChecking(boolean checking) {
        isChecking = checking;
    }

    public static SendBroadcast getInstance(Context context) {
        synchronized (SendBroadcast.class) {
            if (sendBroadcast == null) {
                sendBroadcast = new SendBroadcast(context);
            }
        }
        return sendBroadcast;
    }

    private SendBroadcast(Context context) {
        this.context = context;
    }

    public long getEnterTime() {
        return enterTime;
    }

    /**
     * 2020/5/8
     * 发送指令
     *
     * @return
     */
    public boolean sendOpenIO() {
        Intent intent = new Intent("com.raman.sclm.io.reader");
        intent.putExtra("ioreader", "true");
        context.sendBroadcast(intent);
        return true;
    }


    //2020/5/13  立即关闭电源板
    public void sendCloseIO() {
        Intent intent = new Intent("com.raman.sclm.io.powersaving");
        intent.putExtra("powerSaving", "true");
        context.sendBroadcast(intent);
    }

    //2020/5/13  根据发送的时间，设置定时器，然后关闭电源板
    public void sendCloseTimeIo() {
        Intent intent = new Intent("com.raman.sclm.io.testsuccessclosetime");
        intent.putExtra("testsuccessclosetime", SettingPre.getTime());
        context.sendBroadcast(intent);
    }


    public boolean SendopenIO() {
//        boolean powerStatus=false;
//       // if(!SettingPre.getIsOpen())
//        if(!CommonParameter.BroadcastStatus)
//        {
//            if(!starting) {
//                powerStatus = true;
//                isEnable = false;
//                starting=true;
//            }
//        }
//        else
//        {
//            isEnable=true;
//        }

        if (starting)
            return false;
        delayStatus = true;
        CommonParameter.BroadcastStatus = false;
        CommonParameter.OPENBroadcast = false;
        isEnable = false;
        Intent intent = new Intent("com.raman.sclm.io.reader");
        intent.putExtra("ioreader", "true");
        context.sendBroadcast(intent);
        return true;
    }

    public boolean openedTask(boolean flagShow) {
        if (!CommonParameter.OPENBroadcast) {
            if (flagShow) {
                new AlertDialog.Builder(this.context)
                        .setTitle("电源板打开超时")
                        .setMessage("电源板打开服务超时，请点击后台服务图标打开服务！")
                        .setNegativeButton("关 闭", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                //do nothing - it will close on its own
                            }
                        })
                        .show();
                return false;
            }
            return true;
        }
        if (starting)
            return true;
        // if(powerStatus) {
        if (!CommonParameter.BroadcastStatus) {
            starting = true;
            Utils.postDelayed(() -> {
                isEnable = true;
                starting = false;
            }, startTime);
            enterTime = new Date().getTime();
            return true;
        } else {
            isEnable = true;
            //starting=false;
            return true;
        }
    }

    public void SendcloseIO() {
        Intent intent = new Intent("com.raman.sclm.io.write");
        intent.putExtra("iowrite", "true"); //
        context.sendBroadcast(intent);
    }

    public void timeOpenSend(final long time) {
        runnable = new Runnable() {
            @Override
            public void run() {
                if (isChecking == true) {
                    SendopenIO();//发送
                    timeOpenSend(time);
                    //Log.d(TAG, "隔一段时间一发送");
                }
            }
        };
        Utils.postDelayed(runnable, 1000 * time);
    }

    public void closeTimeOpenSend() {
        if (runnable != null) {
            Utils.removeCallbacks(runnable);
        }
    }


    /**
     * 马达复位
     */
    public void SendMotorReSet() {
        Intent motorReSet = new Intent("com.raman.sclm.porttest");
        motorReSet.putExtra("motor", "motorReSet");
        context.sendBroadcast(motorReSet);
    }

    /**
     * 马达运行到最前面
     */
    public void SendMotorBeFore() {
        Intent motorBeFore = new Intent("com.raman.sclm.porttest");
        motorBeFore.putExtra("motor", "motorBeFore");
        context.sendBroadcast(motorBeFore);
    }

    /**
     * 马达运行到最后面
     */

    public void SendMotorAfter() {
        Intent motorAfter = new Intent("com.raman.sclm.porttest");
        motorAfter.putExtra("motor", "motorAfter");
        context.sendBroadcast(motorAfter);
    }


    /**
     * 马达运行到中间
     */

    public void SendMotorCentre() {
        Intent motorAfter = new Intent("com.raman.sclm.porttest");
        motorAfter.putExtra("motor", "motorCentre");
        context.sendBroadcast(motorAfter);
    }

    /**
     * 马达向前运行100步
     */
    public void SendMotorBeForeOneHundred() {
        Intent motorBeForeOneHundred = new Intent("com.raman.sclm.porttest");
        motorBeForeOneHundred.putExtra("motor", "motorBeForeOneHundred");
        context.sendBroadcast(motorBeForeOneHundred);
    }

    /**
     * 马达向后运行100步
     */
    public void SendMotorAfterOneHundred() {
        Intent motorAfterOneHundred = new Intent("com.raman.sclm.porttest");
        motorAfterOneHundred.putExtra("motor", "motorAfterOneHundred");
        context.sendBroadcast(motorAfterOneHundred);
    }


    /**
     * 只负责打开关闭电源板
     */
    public void sendOpenPower() {
        Intent sendOpenPower = new Intent("com.raman.sclm.openpowertestsend");
        sendOpenPower.putExtra("openpower", "true");
        context.sendBroadcast(sendOpenPower);
    }

    public void sendResetIO(){
        Intent intent = new Intent("com.raman.sclm.io.powersaving");
        intent.putExtra("powerSaving","reset");
        context.sendBroadcast(intent);
        Log.d("TAG", "sendResetIO: 发送广播成功" + new Date().getTime() + "  " + System.currentTimeMillis() + "  " + System.nanoTime());
    }

}
