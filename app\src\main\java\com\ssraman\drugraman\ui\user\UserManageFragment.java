package com.ssraman.drugraman.ui.user;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.jeremyliao.liveeventbus.LiveEventBus;
import com.kongzue.dialog.interfaces.OnDialogButtonClickListener;
import com.kongzue.dialog.util.BaseDialog;
import com.kongzue.dialog.v3.CustomDialog;
import com.kongzue.dialog.v3.MessageDialog;
import com.ssraman.control.spinner.MaterialSpinner;
import com.ssraman.drugraman.R;
import com.ssraman.drugraman.base.ExBaseFragment;
import com.ssraman.drugraman.base.GlobalViewModel;
import com.ssraman.drugraman.databinding.FragmentUserManageBinding;
import com.ssraman.drugraman.db.entity.User_info;
import com.ssraman.drugraman.ui.vm.MainViewModel;
import com.ssraman.drugraman.ui.vm.UserManageViewModel;
import com.ssraman.drugraman.sortableview.UserManagementSorTableDataAdapter;
import com.ssraman.drugraman.sortableview.UserManagementSorTableView;
import com.ssraman.drugraman.util.OperatingAuthority;
import com.ssraman.drugraman.rbac.enums.PermissionType;
import com.ssraman.drugraman.rbac.enums.RoleType;
import com.ssraman.drugraman.rbac.util.PermissionUtils;
import com.ssraman.drugraman.util.UIContextUtils;
import com.ssraman.lib_common.mac.ToolBarSwichBean;
import com.ssraman.lib_common.utils.SubscribeInterface;
import com.ssraman.lib_common.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

import de.codecrafters.tableview.listeners.TableDataClickListener;


public class UserManageFragment extends ExBaseFragment<FragmentUserManageBinding, UserManageViewModel> {
    private GlobalViewModel shareViewModel;
    private UserManagementSorTableDataAdapter userManagementTableDataAdapter;
    private int selected_row = -1;

    @Override
    public int initContentView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return R.layout.fragment_user_manage;
    }

    @Override
    public int initVariableId() {
        return 0;
    }

    @Override
    public void initData() {
        super.initData();
        binding.setMpresenter(new MPresenter());
        shareViewModel = new ViewModelProvider(getActivity()).get(GlobalViewModel.class);
        viewModel.setLogin_name(shareViewModel.get_Login_data().getValue().getLogin_name());
        final UserManagementSorTableView userManagementTableView = binding.userTable;
        if (userManagementTableView != null) {
            userManagementTableDataAdapter = new UserManagementSorTableDataAdapter(getActivity(), new ArrayList<>(), userManagementTableView);
            userManagementTableView.setDataAdapter(userManagementTableDataAdapter);
            userManagementTableView.addDataClickListener(new UserDataClickListener());
        }
        viewModel.LoadUserDatas();
        viewModel.getUser_datas().observe(this, new Observer<List<User_info>>() {
            @Override
            public void onChanged(List<User_info> user_infos) {
                if (user_infos.size() == 0) {
                    userManagementTableDataAdapter.getData().clear();
                } else {
                    userManagementTableDataAdapter.getData().clear();
                    userManagementTableDataAdapter.getData().addAll(user_infos);
                }
                userManagementTableDataAdapter.notifyDataSetChanged();
            }
        });
        LiveEventBus.get("toolbar_swich").postAcrossProcess(new ToolBarSwichBean(1,"主菜单"));
    }

    private class UserDataClickListener implements TableDataClickListener<User_info> {
        @Override
        public void onDataClicked(final int rowIndex, final User_info clickedData) {
            viewModel.postSelect_user_data(clickedData);
            selected_row = rowIndex;
        }
    }

    public class MPresenter {
        public void UserAddClick(View view) {
            // 使用新的权限系统检查用户创建权限
            if (!PermissionUtils.checkPermissionWithMessage(mAppCompatActivity, PermissionType.USER_CREATE, "您没有创建用户的权限")) {
                return;
            }

            CustomDialog.build(mAppCompatActivity, R.layout.layout_user_exit, new CustomDialog.OnBindView() {
                @Override
                public void onBind(final CustomDialog dialog, View v) {
                    EditText txt_name = v.findViewById(R.id.txt_name);
                    EditText txt_password = v.findViewById(R.id.txt_password);
                    MaterialSpinner cb_priority = v.findViewById(R.id.cb_priority);
                    EditText txt_status = v.findViewById(R.id.txt_status);
                    TextView btnCancel = v.findViewById(R.id.btn_user_cancel);
                    TextView btnOk = v.findViewById(R.id.btn_user_ok);
                    txt_name.setText("");
                    txt_password.setText("");
                    cb_priority.setItems(UIContextUtils.getPriority());
                    cb_priority.setSelectedIndex(0);
                    txt_status.setText("");

                    btnOk.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (txt_name.getText().toString().equals("")) {
                                ToastUtils.showShort("员工姓名不能为空！");
                                return;
                            }
                            // 检查角色分配权限
                            if (cb_priority.getSelectedIndex() > 0) {
                                if (!PermissionUtils.checkPermissionWithMessage(mAppCompatActivity, PermissionType.ROLE_ASSIGN, "您没有分配角色的权限")) {
                                    return;
                                }
                            }
                            // 检查系统管理员角色分配权限
                            if (cb_priority.getSelectedIndex() > 1) {
                                if (!PermissionUtils.checkRoleLevelWithMessage(mAppCompatActivity, RoleType.SYSTEM_ADMIN)) {
                                    return;
                                }
                            }
                            if (viewModel.ExistsUser(txt_name.getText().toString())) {
                                ToastUtils.showShort("员工姓名已存在，请重新输入！"); //在插入时做判断，
                                return;
                            }

                            User_info add_user = new User_info();
                            if (cb_priority.getSelectedIndex() <= 2) {
                                add_user.setPriority(cb_priority.getSelectedIndex() + 1);
                            } else if (cb_priority.getSelectedIndex() > 2) {
                                add_user.setPriority(1);
                            } else {
                                ToastUtils.showShort("请选择用户的权限！");
                                return;
                            }
                            add_user.setLogin_name(txt_name.getText().toString());
                            add_user.setLogin_pwd(txt_password.getText().toString());
                            add_user.setStatus(txt_status.getText().toString());
                            viewModel.addUser(add_user, new SubscribeInterface() {
                                @Override
                                public void onSubscribe() {

                                }

                                @Override
                                public void onComplete() {
                                    ToastUtils.showShortSafe("添加新用户成功！");
                                    dialog.doDismiss();
                                }

                                @Override
                                public void onError(Throwable e) {
                                    ToastUtils.showShortSafe("添加新用户出现异常！ 详情：" + e.getMessage());
                                    dialog.doDismiss();
                                }
                            });
                        }
                    });
                    btnCancel.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            dialog.doDismiss();
                        }
                    });
                }
            }).setAlign(CustomDialog.ALIGN.DEFAULT).setCancelable(false).show();

        }

        public void UserDeleteClick(View view) {
            if (selected_row < 0) {
                ToastUtils.showShort("请选择需要删除的用户！");
            }
            User_info select_user = viewModel.getSelect_user_data().getValue();
            if (select_user != null) {
                if (select_user.getLogin_name().trim().equals(shareViewModel.get_Login_data().getValue().getLogin_name())) {
                    MessageDialog.build(mAppCompatActivity)
                            .setTitle("提示")
                            .setMessage("你无权删除自己 ！")
                            .setOkButton("确定", new OnDialogButtonClickListener() {
                                @Override
                                public boolean onClick(BaseDialog baseDialog, View v) {
                                    return false;
                                }
                            })
                            .show();
                    return;
                }
                // 检查用户删除权限
                if (!PermissionUtils.checkPermissionWithMessage(mAppCompatActivity, PermissionType.USER_DELETE, "您没有删除用户的权限")) {
                    return;
                }

                // 检查是否可以删除高权限用户（需要系统管理员权限）
                if (select_user.getPriority() > 1) { // 如果要删除的是系统管理员
                    if (!PermissionUtils.checkRoleLevelWithMessage(mAppCompatActivity, RoleType.SYSTEM_ADMIN)) {
                        return;
                    }
                }
                MessageDialog.build(mAppCompatActivity)
                        .setTitle("提示")
                        .setMessage("您确定要删除吗 ！")
                        .setOkButton("确定", new OnDialogButtonClickListener() {
                            @Override
                            public boolean onClick(BaseDialog baseDialog, View v) {
                                viewModel.DeleteUser(select_user);
                                return false;
                            }
                        })
                        .show();
            }
        }

        public void UserModifyClick(View view) {
            if (selected_row < 0) {
                ToastUtils.showShort("请选择需要删除的用户！");
            }
            User_info select_user = viewModel.getSelect_user_data().getValue();
            if (select_user != null) {
                // 检查用户编辑权限
                if (!PermissionUtils.checkPermissionWithMessage(mAppCompatActivity, PermissionType.USER_EDIT, "您没有编辑用户的权限")) {
                    return;
                }

                // 检查是否可以编辑高权限用户（需要系统管理员权限）
                if (select_user.getPriority() > 1) { // 如果要编辑的是系统管理员
                    if (!PermissionUtils.checkRoleLevelWithMessage(mAppCompatActivity, RoleType.SYSTEM_ADMIN)) {
                        return;
                    }
                }

                CustomDialog.build(mAppCompatActivity, R.layout.layout_user_exit, new CustomDialog.OnBindView() {
                    @Override
                    public void onBind(final CustomDialog dialog, View v) {
                        EditText txt_name = v.findViewById(R.id.txt_name);
                        EditText txt_password = v.findViewById(R.id.txt_password);
                        MaterialSpinner cb_priority = v.findViewById(R.id.cb_priority);
                        EditText txt_status = v.findViewById(R.id.txt_status);
                        TextView btnCancel = v.findViewById(R.id.btn_user_cancel);
                        TextView btnOk = v.findViewById(R.id.btn_user_ok);
                        txt_name.setText(select_user.getLogin_name());
                        txt_password.setText(select_user.getLogin_pwd());
                        cb_priority.setItems(UIContextUtils.getPriority());
                        cb_priority.setSelectedIndex(select_user.getPriority()-1);


                        txt_status.setText(select_user.getStatus());

                        btnOk.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (txt_name.getText().equals("")) {
                                    ToastUtils.showShort("员工姓名不能为空！");
                                    return;
                                }
                                if (cb_priority.getSelectedIndex() > 0) {
                                    if (shareViewModel.get_Login_data().getValue().getPriority() < OperatingAuthority.审核员.getValue()) {
                                        ToastUtils.showShort("你无权增加其他管理员及系统管理员用户！");
                                        return;
                                    }
                                }
                                if (cb_priority.getSelectedIndex() > 1) {
                                    if (shareViewModel.get_Login_data().getValue().getPriority() < OperatingAuthority.系统管理员.getValue()) {
                                        ToastUtils.showShort("你不是系统管理员无权增加其他系统管理员用户！");
                                        return;
                                    }
                                }
                                if (viewModel.ExistsUser(txt_name.getText().toString())) {
                                    ToastUtils.showShort("员工姓名已存在，请重新输入！"); //在插入时做判断，
                                    return;
                                }

                                if (cb_priority.getSelectedIndex() <= 2) {
                                    select_user.setPriority(cb_priority.getSelectedIndex() + 1);
                                } else if (cb_priority.getSelectedIndex() > 2) {
                                    select_user.setPriority(1);
                                } else {
                                    ToastUtils.showShort("请选择用户的权限！");
                                    return;
                                }
                                select_user.setLogin_name(txt_name.getText().toString());
                                select_user.setLogin_pwd(txt_password.getText().toString());
                                select_user.setStatus(txt_status.getText().toString());
                                viewModel.updateUser(select_user, new SubscribeInterface() {
                                    @Override
                                    public void onSubscribe() {

                                    }

                                    @Override
                                    public void onComplete() {
                                        ToastUtils.showShortSafe("更新用户成功！");
                                        dialog.doDismiss();

                                    }

                                    @Override
                                    public void onError(Throwable e) {
                                        ToastUtils.showShortSafe("更新用户出现异常！ 详情：" + e.getMessage());
                                        dialog.doDismiss();
                                    }
                                });
                            }
                        });
                        btnCancel.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                dialog.doDismiss();
                            }
                        });
                    }
                }).setAlign(CustomDialog.ALIGN.DEFAULT).setCancelable(false).show();

            }


        }

        public void UserLoadAllClick(View v) {
            viewModel.getUser_datas();
        }
    }

}