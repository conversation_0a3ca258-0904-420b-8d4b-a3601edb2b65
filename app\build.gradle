apply plugin: 'com.android.application'
apply plugin: 'org.greenrobot.greendao' // apply plugin

def releaseTime() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("UTC"))
}

android {

    signingConfigs {
        config {
            keyAlias 'drugraman'
            keyPassword 'mlsmls'
            storeFile file('D:\\\\work\\basic\\DrugRaman\\app\\drugraman.jks')
            storePassword 'mlsmls'
        }
        debug {
            storeFile file('D:\\\\work\\basic\\DrugRaman\\app\\drugraman.jks')
            storePassword 'mlsmls'
            keyAlias 'drugraman'
            keyPassword 'mlsmls'
        }
    }

    // 添加资源排除配置，解决资源冲突问题
    packagingOptions {
        exclude 'res/layout/test_toolbar.xml'
    }

    compileSdkVersion build_versions.compileSdk
    buildToolsVersion build_versions.buildTools

    defaultConfig {
        applicationId "com.ssraman.drugraman"
        minSdkVersion build_versions.minSdk
        targetSdkVersion build_versions.targetSdk
        versionCode app_version.versionCode
        versionName app_version.versionName

        vectorDrawables.useSupportLibrary = true
        multiDexEnabled = true //21 只需要这里打开即可

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        renderscriptTargetApi build_versions.api   //简单而快速的图像处理
        renderscriptSupportModeEnabled true    // Enable RS support

        //添加如下配置就OK了
        javaCompileOptions { annotationProcessorOptions { includeCompileClasspath = true } }
        externalNativeBuild {
            cmake {
                cppFlags "-fvisibility=hidden"  //,"" for the C++ compiler
                arguments "-DANDROID_STL=c++_shared" //,""

            }
        }
        /* ndk {
             // 加了其他的文件夹（比如 armeabi-v7a，x86等）可能会出问题
             abiFilters "armeabi-v7a","x86"
         }*/
        ndk {
            abiFilters 'armeabi-v7a'
            stl = "c++_shared"//----
        }
        signingConfig signingConfigs.config
    }

    buildFeatures {
        dataBinding true
    }

    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }
    //配置自定义打包名称
    applicationVariants.all { variant ->
        variant.outputs.all {

            def fileName = "${"DrugRaman_" + "${releaseTime()}"+"V"+defaultConfig.versionName+"_" + buildType.name}.apk"
            outputFileName = fileName
        }
    }
    /*针对greenDao的一些配置*/
    greendao {
        schemaVersion 2 //数据库版本号 - 升级到版本2支持RBAC
        daoPackage 'com.ssraman.drugraman.db.gen' //自动生成的工具类的包名
        targetGenDir 'src/main/java' //路径
    }

    sourceSets {
        main {
            res.srcDirs =
                    [

                            'src/main/res/layout/ui_user',
                            'src/main/res/layout/ui_other',
                            'src/main/res/layout/ui_record',
                            'src/main/res/layout/ui_detection',
                            'src/main/res/layout',
                            'src/main/res'
                    ]
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            debuggable true
            jniDebuggable true
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation deps.appc.appcompat
    implementation deps.support.constraintlayout
    implementation project(path: ':control')
    implementation project(path: ':dialog')
    implementation project(path: ':lib_common')
    implementation project(path: ':jninative')
    implementation project(path: ':okgo')
    implementation project(path: ':android-pdf-viewer')
    implementation project(path: ':update-app')
    implementation project(path: ':printerlibs')
    implementation project(path: ':tableview')
    implementation deps.support.navigation_fragment
    implementation deps.support.navigation_ui
    implementation deps.support.legacy_support
    implementation project(path: ':qrcodecore')
    implementation project(path: ':zxing')
    implementation deps.support.material
    implementation deps.support.navigation_fragment
    implementation project(path: ':wifip2p')

    testImplementation deps.test.junit
    androidTestImplementation deps.test.ext
    androidTestImplementation deps.test.espresso

    implementation deps.material.material

    implementation 'org.greenrobot:greendao:3.3.0' // add library

    implementation 'net.zetetic:android-database-sqlcipher:3.5.9'

    implementation deps.MPChart.MPAndroidChart

    implementation deps.rx.rxjava
    implementation deps.rx.rxandroid

    implementation deps.retrofit.gson

    implementation deps.liveeventbus.eventbus
    implementation 'org.apache.commons:commons-math3:3.6.1'
    implementation 'com.bestvike:linq:5.0.0'

    implementation deps.mqtt.mqttv3
    implementation deps.mqtt.service

}