package com.ssraman.drugraman.custom;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;

import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.Description;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.LimitLine;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;

import com.ssraman.drugraman.R;
import com.ssraman.drugraman.util.ColorUtils;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import static com.github.mikephil.charting.components.YAxis.YAxisLabelPosition.OUTSIDE_CHART;

/**
 * LineChart   // 折线表，存线集合，与xml中的控件绑定实力化
 * LineData    // 线集合，所有折线以数组的形式存到此集合中
 * LineDataSet // 点集合，即一条折线
 * Entry       // 某条折线上的一个点
 * Entry -> List<Entry> -> LineDataSet -> LineData -> setData -> LineChart
 * XAxis       // X轴
 * YAxis       // Y轴，Y轴分左右，通过lineChart的getAxisLeft()、getAxisRight()得到
 * Legend      // 图例，即标识哪一条曲线，如用红色标识电流折线，蓝色标识电压折线
 * LimitLine   // 限制线
 * Description // 描述
 *
 * @author: Administrator
 * @date: 2021/6/25
 */
public class SpectrumLineChart extends LineChart {

    private LineData mLineData; // 线集合，所有折现以数组的形式存到此集合中
    private List<LineDataSet> mLineDataSetList; //0线是测试谱图

    private int background_color = Color.TRANSPARENT;
    private int main_spec_color = Color.BLACK;
    private int axis_text_color = Color.BLACK;

    public SpectrumLineChart(Context context) {
        super(context);
        mLineData = new LineData();
        mLineDataSetList = new ArrayList<>();
        InitChart(context, null);
    }

    public SpectrumLineChart(Context context, AttributeSet attrs) {
        super(context, attrs);
        mLineData = new LineData();
        mLineDataSetList = new ArrayList<>();
        InitChart(context, attrs);
    }

    public SpectrumLineChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mLineData = new LineData();
        mLineDataSetList = new ArrayList<>();
        InitChart(context, attrs);
    }

    private void InitChart(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SpectrumLineChart);
            background_color = typedArray.getColor(R.styleable.SpectrumLineChart_backgroundColor, Color.TRANSPARENT);
            main_spec_color = typedArray.getColor(R.styleable.SpectrumLineChart_mainSpecColor, Color.BLACK);
            axis_text_color = typedArray.getColor(R.styleable.SpectrumLineChart_axisTextColor, Color.BLACK);
        }
        /***图表设置***/
        this.setBackgroundColor(background_color); // background color
        this.setDrawGridBackground(false); //是否展示网格线
        this.getDescription().setEnabled(false);//隐藏描述
        this.setDrawBorders(true); //是否显示边界
        this.setBorderWidth(1.0f);
        this.setBorderColor(Color.parseColor("#b3b3b3"));    //边框颜色，默认黑色
        this.setDragEnabled(true); //是否可以拖放
        this.setScaleEnabled(true); // 是否可以缩放
        this.setTouchEnabled(true); //是否有触摸事件
        this.setPinchZoom(false);  //禁止x轴y轴同时进行缩放
        this.setNoDataText("谱图");// 没有数据的时候，显示“谱图”
        this.setMaxVisibleValueCount(4001);

        //设置XY轴动画效果
        //this.animateY(2500);
        this.animateX(1500);





        /***XY轴的设置***/

        /***X轴设置***/
        XAxis xAxis = this.getXAxis();// 得到x轴 可直接使用 mXAxis
        xAxis.setEnabled(true); //是否启用X轴
        xAxis.setDrawAxisLine(true); //是否绘制X轴线
        //设置线为虚线
        xAxis.enableGridDashedLine(10f, 10f, 0f); //背景用虚线表格来绘制  给整成虚线
        //设置从X轴发出横向直线(网格线)
        xAxis.setDrawGridLines(true);
        //设置网格线的颜色
        xAxis.setGridColor(Color.LTGRAY);
        //设置网格线宽度
        xAxis.setGridLineWidth(1f);
        //设置X轴的宽度
        xAxis.setAxisLineWidth(2f);  //??? 500

        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM); //X轴设置显示位置在底部
        xAxis.setAxisMinimum(0f); // 设置X轴的最小值
        xAxis.setAxisMaximum(4000); // 设置X轴的最大值
        xAxis.setLabelCount(8, false); // 设置X轴的刻度数量，第二个参数表示是否平均分配
        xAxis.setGranularityEnabled(true);    //粒度
        //xAxis.setGranularity(10f); // 设置X轴坐标之间的最小间隔
        //this.setVisibleXRangeMaximum(10);// 当前统计图表中最多在x轴坐标线上显示的总量

        //设置是否绘制X轴上的对应值(标签)
        xAxis.setDrawLabels(true);    //是不是显示轴上的刻度
        // xAxis.setTextColor(Color.parseColor("#FFFFFF"));
        xAxis.setTextColor(axis_text_color);
        //xAxis.setValueFormatter((value, axis) -> String.valueOf(xDatas[(int) value % xDatas.length]));

        //其他设置
        //xAxis.setLabelRotationAngle(-60);//设置x坐标的文字倾斜。为倾斜60°
        //xAxis.setTextSize(9);//设置x轴文字大小
        //Typeface tfLight = Typeface.createFromAsset(context.getAssets(), "OpenSans-Light.ttf");
        //xAxis.setTypeface(tfLight);//设置标签的特定字体

        /***左Y轴设置***/
        //得到Y轴
        YAxis leftYAxis = this.getAxisLeft();
        YAxis rightYAxis = this.getAxisRight();
        //设置某一个Y轴是否显示
        rightYAxis.setEnabled(false); //右侧Y轴不显示
        leftYAxis.setEnabled(true); //左侧Y轴显示
        //设置线为虚线
        leftYAxis.enableGridDashedLine(10f, 10f, 0f);
        //设置从Y轴发出横向直线(网格线)
        leftYAxis.setDrawGridLines(true);
        //设置网格线的颜色
        leftYAxis.setGridColor(Color.LTGRAY);
        //设置网格线宽度
        leftYAxis.setGridLineWidth(1f);
        //设置Y轴的宽度
        leftYAxis.setAxisLineWidth(2f);
        //保证Y轴从0开始，不然会上移一点
        leftYAxis.setAxisMinimum(0f); // 设置Y轴的最小值
        //rightYAxis.setAxisMinimum(0f);
        leftYAxis.setAxisMaximum(7000f); // 设置Y轴的最大值
        //rightYAxis.setAxisMaximum(6500f);
        //leftYAxis.setGranularity(1000f); // 设置Y轴坐标之间的最小间隔
        //rightYAxis.setGranularity(1f);
        leftYAxis.setLabelCount(10, false); // 设置Y轴的刻度数量，第二个参数表示是否平均分配
        //this.setVisibleYRangeMaximum(30, YAxis.AxisDependency.LEFT);// 当前统计图表中最多在Y轴坐标线上显示的总量
        //this.setVisibleYRangeMaximum(30, YAxis.AxisDependency.RIGHT);// 当前统计图表中最多在Y轴坐标线上显示的总量

        //leftYAxis.setTextColor(Color.parseColor("#FFFFFF"));
        leftYAxis.setTextColor(axis_text_color);

        leftYAxis.setDrawAxisLine(false);
        //坐标轴绘制在图表的内侧
        leftYAxis.setPosition(OUTSIDE_CHART);

        //leftYAxis.setCenterAxisLabels(true);// 将轴标记居中
        // leftYAxis.setDrawZeroLine(true); // 原点处绘制 一条线
        // leftYAxis.setZeroLineColor(Color.RED);
        // leftYAxis.setZeroLineWidth(1f);
        //this.setAutoScaleMinMaxEnabled(true);//是否启用y轴上的自动缩放的标志

        createLegend();//初始化显示图例设置
        this.setData(mLineData); // 先增加一个空的数据，随后往里面动态添加
    }

    public void NotifyDataChanged() {
        //通知数据已经改变
        this.getData().notifyDataChanged();
        // 像ListView那样的通知数据更新
        this.notifyDataSetChanged();
    }

    /**
     * 功能：根据索引显示或隐藏指定线条
     */
    public void showLine(int index, boolean showed) {
        if (index < this.getLineData().getDataSets().size()) {
            this.getLineData()
                    .getDataSets()
                    .get(index)
                    .setVisible(showed);
            this.invalidate();
        }
    }

    public void showMatchLine(int index, boolean showed) {
        try {
            if (index >= mLineDataSetList.size()) {
                return;
            }
            for (int i = 1; i < mLineDataSetList.size(); i++) {
                mLineDataSetList.get(i)
                        .setVisible(false);
            }
            if (showed) {
                mLineDataSetList.get(index)
                        .setVisible(true);
            }
            this.invalidate();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    //测试用，是否可用需验证
    public void setIcon(int index, int entry_index, Drawable icon) {
        if (index < this.getLineData().getDataSets().size()) {
            this.getLineData()
                    .getDataSets()
                    .get(index)
                    .getEntryForIndex(entry_index)
                    .setIcon(icon);
            this.invalidate();
        }
    }

    //zoomIn()
    //zoomOut()
    //resetZoom()

    /**
     * 功能：x、y轴缩放指定倍数
     */
    public void ZOOMScale(float scale_x, float scale_y) {
        Matrix matrix = new Matrix();
        // x轴缩放scale_x倍,y轴缩放scale_y倍
        matrix.postScale(scale_x, scale_y);
        this.getViewPortHandler().refresh(matrix, this, false);
    }

    /**
     * 功能：动态创建一条曲线 颜色取值外部颜色数组，方便修改
     */
    private int createLine(List<Entry> entries, String label, int YAxisMaximum) {
        LineDataSet lineDataSet = new LineDataSet(entries, label);
        mLineDataSetList.add(lineDataSet);
        int index = mLineDataSetList.size() - 1;
        int color = ColorUtils.LineColor[index];
        YAxis leftYAxis = this.getAxisLeft();
        leftYAxis.mAxisMaximum = YAxisMaximum;
        // 初始化线条
        initLineDataSet(lineDataSet, color, LineDataSet.Mode.CUBIC_BEZIER);

//        if (mLineData == null) {
//            mLineData = new LineData();
//            mLineData.addDataSet(lineDataSet);
//            this.setData(mLineData);
//        } else {
//            this.getLineData().addDataSet(lineDataSet);//mLineData
//        }
        //this.invalidate();
        lineDataSet.setVisible(false);
        this.getLineData().addDataSet(lineDataSet);//mLineData
        return index;
    }

    /**
     * 功能：动态创建多条匹配谱图曲线 颜色取值外部颜色数组，方便修改
     */
    public boolean loadMatchSpecLine(List<SpectrumLineData> mSpecMatchLineList) {
        if (mSpecMatchLineList == null) {
            return false;
        }
        try {
            int count = mSpecMatchLineList.size();
            for (int i = 0; i < count; i++) {
                SpectrumLineData temp_data = mSpecMatchLineList.get(i);
                double max_y = temp_data.getMaxIntensity() + temp_data.getMaxIntensity() / 10;
               // double max_y = Math.ceil(temp_data.getMaxIntensity());
                int index = createLine(temp_data.getEntries(), temp_data.getLabel(), (int) max_y);
                temp_data.setIndex(index);
            }
            this.notifyDataSetChanged();
            this.invalidate();
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 功能：动态创建一条主测试谱图曲线 颜色取值外部颜色数组，方便修改
     */
    public boolean loadMainSpcLine(SpectrumLineData mSpectrumLineData) {
        mLineDataSetList.clear();//清空原有所有曲线
        if (mLineData == null) {
            mLineData = new LineData();
            this.setData(mLineData);
        } else {
            this.clearValues();
        }

        try {

            LineDataSet lineDataSet = new LineDataSet(mSpectrumLineData.getEntries(), mSpectrumLineData.getLabel());
//            mLineDataSetList.add(lineDataSet);
//            int index = mLineDataSetList.size() - 1;
            int index = mLineDataSetList.size();
            //int color = ColorUtil.LineColor[index];
            int color =main_spec_color;
            YAxis leftYAxis = this.getAxisLeft();
            double max_y = mSpectrumLineData.getMaxIntensity() + mSpectrumLineData.getMaxIntensity() / 10;
            double xs = Math.ceil(max_y / 500.0);
            int maxIntensity = (int) (xs * 500);
            leftYAxis.setAxisMaximum(maxIntensity); // 设置Y轴的最大值
            // 初始化线条
            initLineDataSet(lineDataSet, color, LineDataSet.Mode.CUBIC_BEZIER);
            //lineDataSet.notifyDataSetChanged();//
            mLineDataSetList.add(lineDataSet);
            //int index = mLineDataSetList.size() - 1;
            mSpectrumLineData.setIndex(index);
            this.getLineData().addDataSet(lineDataSet);//mLineData
            //this.setViewPortOffsets(10,100, 20, 10);
            lineDataSet.notifyDataSetChanged();
            this.getData().notifyDataChanged();
            this.notifyDataSetChanged();
            this.invalidate();
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 曲线初始化设置,一个LineDataSet 代表一条曲线
     *
     * @param lineDataSet 线条
     * @param color       线条颜色
     * @param mode
     */
    private void initLineDataSet(LineDataSet lineDataSet, int color, LineDataSet.Mode mode) {
//        SpectrumLineChart chart=this;

        lineDataSet.setDrawIcons(true);//设置图标不显示
        lineDataSet.setDrawCircles(false);// 不显示坐标点的小圆点
        lineDataSet.setColor(color); // 设置曲线颜色
        lineDataSet.setCircleColor(color);  // 设置数据点圆形的颜色
        lineDataSet.setLineWidth(1f); // 设置折线宽度
        lineDataSet.setCircleRadius(3f); // 设置折现点圆点半径//
        lineDataSet.setDrawCircleHole(false);// 设置曲线值的圆点是否是空心 ,是否在数据点中间显示一个孔
        lineDataSet.setFormLineWidth(1f);
        lineDataSet.setFormSize(15.f);
        lineDataSet.setValueTextSize(10f); //设置显示值的字体大小

        lineDataSet.setDrawFilled(true); //设置折线图填充
//        lineDataSet.setFillFormatter(new IFillFormatter() {
//            @Override
//            public float getFillLinePosition(ILineDataSet dataSet, LineDataProvider dataProvider) {
//                return chart.getAxisLeft().getAxisMinimum();
//            }
//        });///
        //lineDataSet.setFillAlpha(50); //填充的透明度
        //lineDataSet.setFillColor(ColorTemplate.getHoloBlue());
        //lineDataSet.setFillDrawable(drawable); //设置背景渐变

        lineDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);//设置Y值使用左边Y轴的坐标值

        lineDataSet.setDrawValues(false); // 显示坐标点的数据
        lineDataSet.setHighLightColor(Color.rgb(244, 117, 117));//设置十字线颜色
        lineDataSet.setHighlightEnabled(true);//设置显示定位线,显示十字线，必须显示十字线，否则MarkerView不生效

        if (mode == null) {
            //设置曲线展示为圆滑曲线（如果不设置则默认折线）
            lineDataSet.setMode(LineDataSet.Mode.CUBIC_BEZIER);
        } else {
            lineDataSet.setMode(mode);
        }
    }


    /**
     * 功能：创建图例
     */
    private void createLegend() {
        Legend legend = this.getLegend();
        /***折线图例 标签 设置***/
        //设置显示类型，LINE CIRCLE SQUARE EMPTY 等等 多种方式，查看LegendForm 即可
        legend.setForm(Legend.LegendForm.CIRCLE);
        //legend.setFormSize(30f);//代表图下面图例大小
        //legend.setXEntrySpace(10f);
        legend.setTextColor(Color.CYAN); //设置Legend 文本颜色
        legend.setTextSize(12f);
        //显示位置 左下方
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.BOTTOM);
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.LEFT);
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
        //设置标签是否换行（当多条标签时 需要换行显示、如上右图）
        legend.setWordWrapEnabled(true); //true：可换行。false：不换行
        //是否绘制在图表里面
        legend.setDrawInside(false);
        legend.setEnabled(true);//隐藏Lengend
    }


    /**
     * 设置 可以显示X Y 轴自定义值的 MarkerView
     */
    public void setMarkerView(Context context, int layoutResource) {
        SpectrumMarkerView mv = new SpectrumMarkerView(context, layoutResource);
        mv.setChartView(this);
        this.setMarker(mv);
        this.invalidate();
    }


    /**
     * 设置描述内容
     */
    public void setDescription(String str_description, Color color) {
        Description description = new Description();
        description.setText(str_description);//"X轴描述"
        description.setTextColor(Color.RED);
        this.setDescription(description);
    }

    /**
     * 设置设置是否显示数据点的值
     */
    public void setShowValues(boolean showed) {
        //设置不显示数据点的值
        this.getLineData().setDrawValues(showed);
        this.invalidate();
    }


    /**
     * 动态添加数据
     * 在一个LineChart中存放的折线，其实是以索引从0开始编号的
     *
     * @param yValues y值
     */
    public void addEntry(float yValues, int index) {

        LineData lineData = this.getLineData();
        // 通过索引得到一条折线，之后得到折线上当前点的数量
        int xCount = lineData.getDataSetByIndex(index).getEntryCount();

        Entry entry = new Entry(xCount, yValues); // 创建一个点
        lineData.addEntry(entry, index); // 将entry添加到指定索引处的折线中

        //通知数据已经改变
        lineData.notifyDataChanged();
        this.notifyDataSetChanged();

        //把yValues移到指定索引的位置
        this.moveViewToAnimated(xCount - 4, yValues, YAxis.AxisDependency.LEFT, 1000);// TODO: 2019/5/4 内存泄漏，异步 待修复
        this.invalidate();
    }

    private void addXLimitLine(float limit, String label) {
        XAxis xAxis = this.getXAxis();// 得到x轴
        //设置限制线 limit代表该轴某个值，也就是要画到该轴某个值上
        LimitLine limitLine = new LimitLine(limit, label);
        //设置限制线的宽
        limitLine.setLineWidth(1f);
        //设置限制线的颜色
        limitLine.setLineColor(Color.RED);
        //limitLine.setTextSize(10f);
        //limitLine.setTextColor(Color.RED);  //颜色
        //设置基线的位置
        limitLine.setLabelPosition(LimitLine.LimitLabelPosition.LEFT_TOP);
        //limitLine.setLabel(label);
        //设置限制线为虚线
        limitLine.enableDashedLine(10f, 10f, 0f);
        // draw limit lines behind data instead of on top
        //yAxis.setDrawLimitLinesBehindData(true);
        xAxis.setDrawLimitLinesBehindData(true); //在数据后面而不是上面画限制线
        //左边Y轴添加限制线
        xAxis.addLimitLine(limitLine);
    }

    public boolean addSpecLimitLine(float[] limitlist) {
        try {
            if (limitlist == null) {
                return false;
            }
            for (int i = 0; i < limitlist.length; i++) {
                addXLimitLine(limitlist[i], "");
            }
            this.invalidate();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void setAxisXMax(float max)
    {
        XAxis xAxis = this.getXAxis();
        xAxis.setAxisMaximum(max);
        this.invalidate();
    }

    public void disableSpec()
    {
        this.setDragEnabled(false); //是否可以拖放
        this.setScaleEnabled(false); // 是否可以缩放
        this.setTouchEnabled(false); //是否有触摸事件
        this.invalidate();
    }


    public void ChartClear() {
        // moveViewToAnimated 移动到某个点，有内存泄漏，暂未修复，希望网友可以指着
        this.clearAllViewportJobs();
        this.removeAllViewsInLayout();
        this.removeAllViews();
        this.mLineDataSetList.clear();
        //this.clearValues();
    }

    public void ChartDataClear() {
        this.clearValues();
        this.mLineDataSetList.clear();

    }

    public boolean saveToPng(String title, String pathOnSD, int quality) {
        // restrain quality
        if (quality < 0 || quality > 100)
            quality = 50;

        Bitmap b = getChartBitmap();

        OutputStream stream = null;
        try {
            stream = new FileOutputStream(pathOnSD + "/" + title
                    + ".png");

            /*
             * Write bitmap to file using JPEG or PNG and 40% quality hint for
             * JPEG.
             */
            b.compress(Bitmap.CompressFormat.PNG, quality, stream);

            stream.close();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

}
